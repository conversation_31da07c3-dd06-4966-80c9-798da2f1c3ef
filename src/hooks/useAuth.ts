import { useState } from "react";

interface User {
  id: string;
  username: string;
  name: string;
  role: string;
  department: string;
}

export const useAuth = () => {
  // 模擬已登入的用戶，實際應用中應從API或本地存儲獲取
  const [currentUser] = useState<User>({
    id: "1",
    username: "admin",
    name: "系統管理員",
    role: "admin",
    department: "技術部",
  });

  return {
    currentUser,
    isAuthenticated: !!currentUser,
  };
};
