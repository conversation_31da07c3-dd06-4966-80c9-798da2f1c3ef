import { removePx } from "@/utils/theme";
import { useEffect, useMemo, useState } from "react";

// 定義斷點，與 tailwind.config.ts 保持同步
const BREAKPOINTS = {
  xs: "375px",
  sm: "576px",
  md: "768px",
  lg: "1024px",
  xl: "1280px",
  "2xl": "1536px",
};

type MediaQueryConfig = {
  minWidth?: number;
  maxWidth?: number;
  minHeight?: number;
  maxHeight?: number;
  orientation?: "portrait" | "landscape";
  prefersColorScheme?: "dark" | "light";
  prefersReducedMotion?: boolean;
  devicePixelRatio?: number;
  pointerType?: "coarse" | "fine";
};

const buildMediaQuery = (config: MediaQueryConfig | string): string => {
  if (typeof config === "string") return config;

  const conditions: string[] = [];

  if (config.minWidth) conditions.push(`(min-width: ${config.minWidth}px)`);
  if (config.maxWidth) conditions.push(`(max-width: ${config.maxWidth}px)`);
  if (config.minHeight) conditions.push(`(min-height: ${config.minHeight}px)`);
  if (config.maxHeight) conditions.push(`(max-height: ${config.maxHeight}px)`);
  if (config.orientation)
    conditions.push(`(orientation: ${config.orientation})`);
  if (config.prefersColorScheme)
    conditions.push(`(prefers-color-scheme: ${config.prefersColorScheme})`);
  if (config.prefersReducedMotion)
    conditions.push("(prefers-reduced-motion: reduce)");
  if (config.devicePixelRatio)
    conditions.push(
      `(-webkit-min-device-pixel-ratio: ${config.devicePixelRatio})`
    );
  if (config.pointerType) conditions.push(`(pointer: ${config.pointerType})`);

  return conditions.join(" and ");
};

/**
 * React hook for handling media queries
 *
 * @param config - Media query configuration object or query string
 * @returns boolean - Returns true if the media query matches
 *
 * @example
 * // Basic usage - Mobile detection
 * const isMobile = useMediaQuery({ maxWidth: 768 });
 *
 * @example
 * // Using predefined breakpoints
 * const isDesktop = useMediaQuery(up('lg'));
 *
 * @example
 * // Complex query - Tablet in landscape mode
 * const isTabletLandscape = useMediaQuery({
 *   minWidth: 768,
 *   maxWidth: 1024,
 *   orientation: 'landscape'
 * });
 *
 * @example
 * // User preferences
 * const isDarkMode = useMediaQuery({ prefersColorScheme: 'dark' });
 * const prefersReducedMotion = useMediaQuery({ prefersReducedMotion: true });
 *
 * @example
 * // Device capabilities
 * const isTouchDevice = useMediaQuery({ pointerType: 'coarse' });
 * const isRetina = useMediaQuery({ devicePixelRatio: 2 });
 *
 * @example
 * // Range queries using helpers
 * const isTablet = useMediaQuery(between('sm', 'md'));
 *
 * @example
 * // Raw media query string
 * const isPortrait = useMediaQuery('(orientation: portrait)');
 *
 * @see {@link MediaQueryConfig} for all supported configuration options
 */
export const useMediaQuery = (config: MediaQueryConfig | string) => {
  // 伺服器端渲染時預設為 false
  const [matches, setMatches] = useState(false);

  // 將 config 轉換為 mediaQuery 字串
  const mediaQueryString = useMemo(() => buildMediaQuery(config), [config]);

  useEffect(() => {
    // 檢查是否在瀏覽器環境
    if (typeof window === "undefined") return;

    // 客戶端渲染時立即檢查當前狀態
    const mediaQuery = window.matchMedia(mediaQueryString);
    setMatches(mediaQuery.matches);

    // 監聽變化
    const handler = (e: MediaQueryListEvent) => setMatches(e.matches);

    // 使用標準 addEventListener API
    mediaQuery.addEventListener("change", handler);

    // 清理函數
    return () => {
      mediaQuery.removeEventListener("change", handler);
    };
  }, [mediaQueryString]);

  return matches;
};

type Breakpoints = typeof BREAKPOINTS;
type BreakpointsKeys = keyof Breakpoints;
// 輔助函數
export const up = (key: BreakpointsKeys) => ({
  minWidth: removePx(BREAKPOINTS[key]),
});

export const down = (key: BreakpointsKeys) => ({
  maxWidth: removePx(BREAKPOINTS[key]) - 0.05, // 減去0.05px避免斷點重疊
});

export const between = (start: BreakpointsKeys, end: BreakpointsKeys) => ({
  minWidth: removePx(BREAKPOINTS[start]),
  maxWidth: removePx(BREAKPOINTS[end]) - 0.05,
});
