import apiClient from "../apiClient";
import type {
  CaseCodeListResponse,
  CaseCodeResponse,
  CreateCaseCodeRequest,
  UpdateCaseCodeRequest,
} from "../models/case-code";

/**
 * 案件類型代碼服務
 */
export const caseCodeService = {
  /**
   * 創建新案件類型代碼
   * @param data 案件類型代碼資料
   */
  createCaseCode: (data: CreateCaseCodeRequest) => {
    return apiClient.post<CaseCodeResponse>("/case-codes/", data);
  },

  /**
   * 獲取案件類型代碼列表
   * @param params 查詢參數
   */
  getAllCaseCodes: (params?: {
    page?: number;
    limit?: number;
    code?: string;
    name?: string;
    type?: string;
  }) => {
    return apiClient.get<CaseCodeListResponse>("/case-codes/", params);
  },

  /**
   * 根據ID獲取案件類型代碼
   * @param codeId 代碼ID
   */
  getCaseCode: (codeId: string) => {
    return apiClient.get<CaseCodeResponse>(`/case-codes/${codeId}`);
  },

  /**
   * 更新案件類型代碼資料
   * @param codeId 代碼ID
   * @param data 更新資料
   */
  updateCaseCode: (codeId: string, data: UpdateCaseCodeRequest) => {
    return apiClient.put<CaseCodeResponse>(`/case-codes/${codeId}`, data);
  },

  /**
   * 刪除案件類型代碼
   * @param codeId 代碼ID
   */
  deleteCaseCode: (codeId: string) => {
    return apiClient.delete<boolean>(`/case-codes/${codeId}`);
  },

  /**
   * 根據類型獲取案件類型代碼列表
   * @param typeValue 代碼類型
   */
  getCaseCodesByType: (typeValue: string) => {
    return apiClient.get<CaseCodeResponse[]>(
      `/case-codes/by-type/${typeValue}`
    );
  },
};
