import apiClient from "../apiClient";
import type {
  InitializeDepartmentSettingsParams,
  SystemSettingListParams,
  SystemSettingListResponse,
  SystemSettingResponse,
  UpdateSystemSettingRequest,
} from "../models/system-setttings";

/**
 * 系統設定服務
 */
export const systemSettingService = {
  /**
   * 獲取系統設定列表
   * @param params 查詢參數
   */
  getSystemSettings: (params?: SystemSettingListParams) => {
    return apiClient.get<SystemSettingListResponse>(
      "/system-settings/",
      params
    );
  },

  /**
   * 根據ID獲取系統設定
   * @param settingId 設定ID
   */
  getSystemSetting: (settingId: string) => {
    return apiClient.get<SystemSettingResponse>(
      `/system-settings/${settingId}`
    );
  },
  /**
   * 更新系統設定
   * @param settingId 設定ID
   * @param data 更新資料
   */
  updateSystemSetting: (
    settingId: string,
    data: UpdateSystemSettingRequest
  ) => {
    return apiClient.put<SystemSettingResponse>(
      `/system-settings/${settingId}`,
      data
    );
  },

  /**
   * 初始化部門設定
   * @param data 初始化參數
   */
  initializeDepartmentSettings: (data: InitializeDepartmentSettingsParams) => {
    return apiClient.post<boolean>("/system-settings/initialize", data);
  },
};
