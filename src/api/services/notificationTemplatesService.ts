import apiClient from "../apiClient";
import type {
  ChannelType,
  CreateNotificationTemplateRequest,
  NotificationTemplateListResponse,
  NotificationTemplateResponse,
  TemplateType,
  UpdateNotificationTemplateRequest,
} from "../models/notification-templates";

/**
 * 通知模板服務
 */
export const notificationTemplatesService = {
  /**
   * 創建新通知模板
   * @param data 模板資料
   */
  createTemplate: (data: CreateNotificationTemplateRequest) => {
    return apiClient.post<NotificationTemplateResponse>(
      "/notification-templates/",
      data
    );
  },

  /**
   * 獲取通知模板列表
   * @param params 查詢參數
   */
  getAllTemplates: (params?: {
    page?: number;
    limit?: number;
    name?: string;
    type?: TemplateType;
    channels?: ChannelType;
    created_by?: string;
  }) => {
    return apiClient.get<NotificationTemplateListResponse>(
      "/notification-templates/",
      params
    );
  },

  /**
   * 根據ID獲取通知模板
   * @param templateId 模板ID
   */
  getTemplate: (templateId: string) => {
    return apiClient.get<NotificationTemplateResponse>(
      `/notification-templates/${templateId}`
    );
  },

  /**
   * 更新通知模板資料
   * @param templateId 模板ID
   * @param data 更新資料
   */
  updateTemplate: (
    templateId: string,
    data: UpdateNotificationTemplateRequest
  ) => {
    return apiClient.put<NotificationTemplateResponse>(
      `/notification-templates/${templateId}`,
      data
    );
  },

  /**
   * 刪除通知模板
   * @param templateId 模板ID
   */
  deleteTemplate: (templateId: string) => {
    return apiClient.delete<boolean>(`/notification-templates/${templateId}`);
  },
};
