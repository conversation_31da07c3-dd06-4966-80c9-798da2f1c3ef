import apiClient from "../apiClient";
import type {
  CollectionEnum,
  FileResponse,
  FileUploadResponse,
} from "../models/files";

/**
 * 檔案管理服務
 */
export const fileService = {
  /**
   * 上傳檔案
   * @param file 檔案對象
   * @param refId 關聯記錄ID (可選)
   * @param collection 關聯的集合類型 (可選)
   */
  uploadFile: (file: File, refId?: string, collection?: CollectionEnum) => {
    const formData = new FormData();
    formData.append("file", file);

    if (refId) {
      formData.append("ref_id", refId);
    }

    if (collection) {
      formData.append("collection", collection);
    }

    return apiClient.submitForm<FileUploadResponse>("/files/upload", formData);
  },

  /**
   * 更新檔案引用關係
   * @param fileId 檔案ID
   * @param refId 關聯記錄ID
   * @param collection 關聯的集合類型
   */
  updateFileReference: (
    fileId: string,
    refId: string,
    collection: CollectionEnum
  ) => {
    const formData = new FormData();
    formData.append("ref_id", refId);
    formData.append("collection", collection);

    return apiClient.put<FileResponse>(`/files/${fileId}/reference`, formData);
  },

  /**
   * 下載檔案
   * @param storedName 儲存的檔案名稱
   */
  downloadFile: (storedName: string) => {
    return apiClient.downloadFile(`/files/${storedName}/download`);
  },

  /**
   * 獲取特定實體的檔案列表
   * @param refId 實體ID
   * @param collection 集合類型
   */
  getFilesByEntity: (refId: string, collection: CollectionEnum) => {
    return apiClient.get<FileResponse[]>(`/files/list/${refId}/${collection}`);
  },

  /**
   * 刪除檔案
   * @param storedName 儲存的檔案名稱
   */
  deleteFile: (storedName: string) => {
    return apiClient.delete(`/files/${storedName}`);
  },
};
