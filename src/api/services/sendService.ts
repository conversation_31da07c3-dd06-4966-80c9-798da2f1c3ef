import apiClient from "../apiClient";
import type {
  BatchDeleteRequest,
  BatchDeleteResponse,
  CategoryEnum,
  ContactTypeEnum,
  SendRecordListResponse,
  SendRecordResponse,
  SendRequest,
  SendResponse,
  SendTypeEnum,
  SendUserListResponse,
} from "../models/send";

/**
 * 訊息發送服務
 */
export const sendService = {
  /**
   * 發送訊息
   * @param data 要發送的訊息資料
   */
  sendMessage: (data: SendRequest) => {
    return apiClient.post<SendResponse>("/send/", data);
  },

  /**
   * 獲取發送記錄列表
   * @param params 查詢參數
   */
  getSendRecords: (params?: {
    userId?: string;
    category?: CategoryEnum;
    send_type?: SendTypeEnum;
    start_date?: string;
    end_date?: string;
    keyword?: string;
    page?: number;
    limit?: number;
  }) => {
    return apiClient.get<SendRecordListResponse>("/send/records", { params });
  },
  /**
   * 獲取特定發送記錄詳情
   * @param recordId 記錄ID
   */ getSendRecord: (recordId: string) => {
    return apiClient.get<SendRecordResponse>(`/send/records/${recordId}`);
  },

  /**
   * 獲取用戶列表（根據聯繫方式類型）
   * @param params 查詢參數
   */
  getUsers: (params: {
    contact_type: ContactTypeEnum;
    page?: number;
    limit?: number;
  }) => {
    return apiClient.get<SendUserListResponse>("/send/users", params);
  },

  /**
   * 批次刪除發送記錄
   * @param data 批次刪除請求資料
   */
  batchDeleteSendRecords: (data: BatchDeleteRequest) => {
    return apiClient.delete<BatchDeleteResponse>("/send/records/batch", data);
  },
};
