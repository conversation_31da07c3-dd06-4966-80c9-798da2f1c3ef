import apiClient from "../apiClient";
import type {
  CreateDocumentCodeRequest,
  DocumentCodeListResponse,
  DocumentCodeResponse,
  UpdateDocumentCodeRequest,
} from "../models/document-codes";

/**
 * 文件類別設定服務
 */
export const documentCodeService = {
  /**
   * 創建新文件類別設定
   * @param data 文件類別設定資料
   */
  createDocumentCode: (data: CreateDocumentCodeRequest) => {
    return apiClient.post<DocumentCodeResponse>("/document-codes/", data);
  },

  /**
   * 獲取文件類別設定列表
   * @param params 查詢參數
   */
  getAllDocumentCodes: (params?: {
    page?: number;
    limit?: number;
    code?: string;
    name?: string;
    type?: string;
  }) => {
    return apiClient.get<DocumentCodeListResponse>("/document-codes/", params);
  },

  /**
   * 根據ID獲取文件類別設定
   * @param codeId 代碼ID
   */
  getDocumentCode: (codeId: string) => {
    return apiClient.get<DocumentCodeResponse>(`/document-codes/${codeId}`);
  },

  /**
   * 更新文件類別設定資料
   * @param codeId 代碼ID
   * @param data 更新資料
   */
  updateDocumentCode: (codeId: string, data: UpdateDocumentCodeRequest) => {
    return apiClient.put<DocumentCodeResponse>(
      `/document-codes/${codeId}`,
      data
    );
  },

  /**
   * 刪除文件類別設定
   * @param codeId 代碼ID
   */
  deleteDocumentCode: (codeId: string) => {
    return apiClient.delete<boolean>(`/document-codes/${codeId}`);
  },

  /**
   * 根據代碼獲取文件類別設定
   * @param code 代碼值
   */
  getCodeByCode: (code: string) => {
    return apiClient.get<DocumentCodeResponse>(
      `/document-codes/by-code/${code}`
    );
  },

  /**
   * 獲取特定類型的所有文件類別設定
   * @param typeValue 類型值
   */
  getCodesByType: (typeValue: string) => {
    return apiClient.get<DocumentCodeResponse[]>(
      `/document-codes/by-type/${typeValue}`
    );
  },

  /**
   * 初始化默認文件類型設定
   */
  initDocumentTypes: () => {
    return apiClient.post<boolean>("/document-codes/init");
  },
};
