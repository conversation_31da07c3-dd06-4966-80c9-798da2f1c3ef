import apiClient from "../apiClient";
import type {
  CustomerCreate,
  CustomerPage,
  CustomerRead,
  CustomerUpdate,
} from "../models/customer";

/**
 * 客戶服務
 */
export const customerService = {
  /**
   * 獲取客戶列表，支援分頁和搜索
   * @param params 查詢參數
   */
  getAllCustomers: (params?: {
    search?: string;
    department?: string;
    page?: number;
    limit?: number;
  }) => {
    return apiClient.get<CustomerPage>("/customers/", params);
  },

  /**
   * 創建新客戶
   * @param data 客戶資料
   */
  createCustomer: (data: CustomerCreate) => {
    return apiClient.post<CustomerRead>("/customers/", data);
  },

  /**
   * 獲取客戶詳情
   * @param uuid 客戶 UUID
   */
  getCustomer: (uuid: string) => {
    return apiClient.get<CustomerRead>(`/customers/${uuid}`);
  },

  /**
   * 更新客戶信息
   * @param uuid 客戶 UUID
   * @param data 更新資料
   */
  updateCustomer: (uuid: string, data: CustomerUpdate) => {
    return apiClient.put<CustomerRead>(`/customers/${uuid}`, data);
  },

  /**
   * 刪除客戶
   * @param uuid 客戶 UUID
   */
  deleteCustomer: (uuid: string) => {
    return apiClient.delete(`/customers/${uuid}`);
  },
};
