import apiClient from "../apiClient";

/**
 * 序號管理相關 API 介面類型
 */
export interface SerialCode {
  id: string;
  code: string;
  days: number;
  price: number;
  is_used: boolean;
  is_sent: boolean;
  created_at: string;
  used_at?: string;
  sent_at?: string;
  used_by?: string;
}

export interface ListSerialCodesRequest {
  page?: number;
  limit?: number;
  days?: number;
  isUsed?: string;
  month?: string;
}

export interface ListSerialCodesResponse {
  items: SerialCode[];
  page: number;
  limit: number;
  total_items: number;
  total_pages: number;
}

export interface BatchDeleteRequest {
  ids: string[];
}

export interface BatchDeleteResponse {
  deleted_count: number;
  message: string;
}

export interface GenerateSerialCodesRequest {
  days: number;
  price: number;
  quantity: number;
}

export interface GenerateSerialCodesResponse {
  generated_count: number;
  message: string;
}

export interface MonthlyStatisticsResponse {
  totalGenerated: number;
  totalRevenue: number;
  totalUsed: number;
  unusedCodes: number;
  usageRate: number;
}

/**
 * 序號管理服務
 */
export const serialCodeService = {
  /**
   * 獲取序號列表 (分頁、篩選)
   * @param params 查詢參數
   */
  getSerialCodes: (params?: ListSerialCodesRequest) => {
    return apiClient.get<ListSerialCodesResponse>("/api/serial-codes/", params);
  },

  /**
   * 批量刪除序號
   * @param data 刪除請求資料
   */
  batchDeleteSerialCodes: (data: BatchDeleteRequest) => {
    return apiClient.delete<BatchDeleteResponse>("/api/serial-codes/batch", data);
  },

  /**
   * 生成序號
   * @param data 生成序號請求資料
   */
  generateSerialCodes: (data: GenerateSerialCodesRequest) => {
    return apiClient.post<GenerateSerialCodesResponse>("/api/serial-codes/generate", data);
  },

  /**
   * 獲取月度序號統計
   * @param month 月份篩選 (YYYY-MM)，預設為當前月份
   */
  getMonthlyStatistics: (month?: string) => {
    return apiClient.get<MonthlyStatisticsResponse>("/api/serial-codes/statistics/monthly", {
      month,
    });
  },

  /**
   * 將序號標記為已發送 (使用者點擊複製的時候呼叫)
   * @param id 序號ID
   */
  markSerialCodeAsSent: (id: string) => {
    return apiClient.post<{ message: string }>(`/api/serial-codes/${id}/mark-sent`);
  },
};