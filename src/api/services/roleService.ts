import apiClient from "../apiClient";
import type {
  CreateRoleRequest,
  RoleListResponse,
  RoleResponse,
  UpdateRoleRequest,
} from "../models/roles";

/**
 * 角色服務
 */
export const roleService = {
  /**
   * 創建新角色
   * @param data 角色資料
   */
  createRole: (data: CreateRoleRequest) => {
    return apiClient.post<RoleResponse>("/roles/", data);
  },

  /**
   * 獲取角色列表
   * @param params 查詢參數
   */
  getAllRoles: (params?: {
    page?: number;
    limit?: number;
    role_name?: string;
  }) => {
    return apiClient.get<RoleListResponse>("/roles/", params);
  },

  /**
   * 根據ID獲取角色
   * @param roleId 角色ID
   */
  getRole: (roleId: string) => {
    return apiClient.get<RoleResponse>(`/roles/${roleId}`);
  },

  /**
   * 更新角色資料
   * @param roleId 角色ID
   * @param data 更新資料
   */
  updateRole: (roleId: string, data: UpdateRoleRequest) => {
    return apiClient.put<RoleResponse>(`/roles/${roleId}`, data);
  },

  /**
   * 刪除角色
   * @param roleId 角色ID
   */
  deleteRole: (roleId: string) => {
    return apiClient.delete<boolean>(`/roles/${roleId}`);
  },
};
