import apiClient from "../apiClient";
import type {
  AdminApproveMeetingRequest,
  AdminCancelMeetingRequest,
  ListMeetingRequestsParams,
  MeetingRejectionRequest,
  MeetingRequestListResponse,
  MeetingRequestResponse,
  NotifyRescheduleRequest,
} from "../models/meeting";

/**
 * Zoom 會議安排服務
 */
export const meetingService = {
  /**
   * 列出所有會議請求
   * @param params 查詢參數
   */
  listMeetingRequests: (params?: ListMeetingRequestsParams) => {
    return apiClient.get<MeetingRequestListResponse>("/meet/requests", params);
  },

  /**
   * 列出所有待審批的會議請求
   * @param params 查詢參數
   */
  listPendingMeetingRequests: (params?: { page?: number; limit?: number }) => {
    return apiClient.get<MeetingRequestListResponse>(
      "/meet/requests/pending",
      params
    );
  },

  /**
   * 獲取單個會議請求的詳情
   * @param requestId 會議請求ID
   */
  getSingleMeetingRequest: (requestId: string) => {
    return apiClient.get<MeetingRequestResponse>(`/meet/requests/${requestId}`);
  },

  /**
   * 管理員核准會議請求並設定會議時間
   * @param requestId 會議請求ID
   * @param data 核准會議的資料
   */
  approveMeeting: (requestId: string, data: AdminApproveMeetingRequest) => {
    return apiClient.post<MeetingRequestResponse>(
      `/meet/requests/${requestId}/approve`,
      data
    );
  },

  /**
   * 管理員拒絕會議請求
   * @param requestId 會議請求ID
   * @param data 拒絕原因
   */
  rejectMeeting: (requestId: string, data: MeetingRejectionRequest) => {
    return apiClient.post<MeetingRequestResponse>(
      `/meet/requests/${requestId}/reject`,
      data
    );
  },

  /**
   * 管理員取消已審核的會議
   * @param requestId 會議請求ID
   * @param data 取消原因
   */
  cancelMeeting: (requestId: string, data: AdminCancelMeetingRequest) => {
    return apiClient.post<MeetingRequestResponse>(
      `/meet/requests/${requestId}/cancel`,
      data
    );
  },

  /**
   * 通知請求者會議需要重新安排
   * @param requestId 會議請求ID
   * @param data 重新安排原因
   */
  notifyReschedule: (requestId: string, data: NotifyRescheduleRequest) => {
    return apiClient.post<MeetingRequestResponse>(
      `/meet/requests/${requestId}/notify-reschedule`,
      data
    );
  },

  /**
   * 刪除會議請求（不發送通知）
   * @param requestId 會議請求ID
   */
  deleteMeeting: (requestId: string) => {
    return apiClient.delete(`/meet/requests/${requestId}`);
  },
};

export default meetingService;
