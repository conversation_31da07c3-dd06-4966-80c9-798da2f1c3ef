import apiClient from "../apiClient";
import type {
  CreateMailRecordRequest,
  CreateOutgoingDocumentRequest,
  MailRecordListResponse,
  MailRecordResponse,
  OutgoingDocumentListResponse,
  OutgoingDocumentResponse,
  UpdateMailRecordRequest,
  UpdateOutgoingDocumentRequest,
} from "../models/documents";
import { CollectionEnum } from "../models/files";
import { fileService } from "./fileService";

/**
 * 文件管理服務（收文、發文和郵寄管理）
 */
export const documentService = {
  // 發文管理相關API
  outgoingDocument: {
    /**
     * 創建新發文記錄
     * @param data 發文記錄資料
     * @param file 可選的附件檔案
     */
    createOutgoingDocument: (
      data: CreateOutgoingDocumentRequest,
      file?: File
    ) => {
      // 先創建記錄，獲取ID後再上傳檔案
      return apiClient
        .post<OutgoingDocumentResponse>("/outgoing-documents/", data)
        .then((response) => {
          if (file && response && response.id) {
            return fileService
              .uploadFile(file, response.id, CollectionEnum.OUTGOING_DOCUMENTS)
              .then((fileResponse) => {
                if (fileResponse.success && fileResponse.data) {
                  // 更新記錄，添加檔案路徑
                  const updateData: UpdateOutgoingDocumentRequest = {
                    file_relative_path: fileResponse.data.relativePath,
                  };
                  return apiClient.put<OutgoingDocumentResponse>(
                    `/outgoing-documents/${response.id}`,
                    updateData
                  );
                }
                return response;
              });
          }
          return response;
        });
    },

    /**
     * 獲取發文記錄列表
     * @param params 查詢參數
     */
    getAllOutgoingDocuments: (params?: {
      page?: number;
      limit?: number;
      department?: string;
      send_date_start?: string;
      send_date_end?: string;
      document_type?: string;
      customer_name?: string;
      document_number?: string;
      content?: string;
      status?: string;
      handler_id?: string;
    }) => {
      return apiClient.get<OutgoingDocumentListResponse>(
        "/outgoing-documents/",
        params
      );
    },

    /**
     * 根據ID獲取發文記錄
     * @param documentId 記錄ID
     */
    getOutgoingDocument: (documentId: string) => {
      return apiClient.get<OutgoingDocumentResponse>(
        `/outgoing-documents/${documentId}`
      );
    },

    /**
     * 更新發文記錄資料
     * @param documentId 記錄ID
     * @param data 更新參數
     * @param file 可選的附件檔案
     */
    updateOutgoingDocument: (
      documentId: string,
      data: UpdateOutgoingDocumentRequest,
      file?: File
    ) => {
      if (file) {
        return fileService
          .uploadFile(file, documentId, CollectionEnum.OUTGOING_DOCUMENTS)
          .then((response) => {
            if (response.success && response.data) {
              const updatedData: UpdateOutgoingDocumentRequest = {
                ...data,
                file_relative_path: response.data.relativePath,
              };
              return apiClient.put<OutgoingDocumentResponse>(
                `/outgoing-documents/${documentId}`,
                updatedData
              );
            }
            return apiClient.put<OutgoingDocumentResponse>(
              `/outgoing-documents/${documentId}`,
              data
            );
          });
      }
      return apiClient.put<OutgoingDocumentResponse>(
        `/outgoing-documents/${documentId}`,
        data
      );
    },

    /**
     * 刪除發文記錄
     * @param documentId 記錄ID
     */
    deleteOutgoingDocument: (documentId: string) => {
      return apiClient.delete<boolean>(`/outgoing-documents/${documentId}`);
    },

    /**
     * 獲取特定部門的所有發文記錄
     * @param department 部門名稱
     */
    getDocumentsByDepartment: (department: string) => {
      return apiClient.get<OutgoingDocumentResponse[]>(
        `/outgoing-documents/by-department/${department}`
      );
    },

    /**
     * 獲取特定客戶的所有發文記錄
     * @param customerId 客戶ID
     */
    getDocumentsByCustomer: (customerId: string) => {
      return apiClient.get<OutgoingDocumentResponse[]>(
        `/outgoing-documents/by-customer/${customerId}`
      );
    },
  },

  // 郵寄管理相關API
  mailRecord: {
    /**
     * 創建新郵寄記錄
     * @param data 郵寄記錄資料
     * @param file 可選的附件檔案
     */
    createMailRecord: (data: CreateMailRecordRequest, file?: File) => {
      // 先創建記錄，獲取ID後再上傳檔案
      return apiClient
        .post<MailRecordResponse>("/mail-records/", data)
        .then((response) => {
          if (file && response && response.id) {
            return fileService
              .uploadFile(file, response.id, CollectionEnum.MAIL_RECORDS)
              .then((fileResponse) => {
                if (fileResponse.success && fileResponse.data) {
                  // 由於 API 文件中郵寄記錄沒有明確說明檔案路徑欄位
                  // 假設與其他文件類型一致，使用 file_relative_path
                  const updateData = {
                    file_relative_path: fileResponse.data.relativePath,
                  };
                  return apiClient.put<MailRecordResponse>(
                    `/mail-records/${response.id}`,
                    updateData
                  );
                }
                return response;
              });
          }
          return response;
        });
    },

    /**
     * 獲取郵寄記錄列表
     * @param params 查詢參數
     */
    getAllMailRecords: (params?: {
      page?: number;
      limit?: number;
      department?: string;
      date_start?: string;
      date_end?: string;
      document_type?: string;
      customer_name?: string;
      tracking_number?: string;
      content?: string;
      status?: string;
      handler_id?: string;
    }) => {
      return apiClient.get<MailRecordListResponse>("/mail-records/", params);
    },

    /**
     * 根據ID獲取郵寄記錄
     * @param recordId 記錄ID
     */
    getMailRecord: (recordId: string) => {
      return apiClient.get<MailRecordResponse>(`/mail-records/${recordId}`);
    },

    /**
     * 更新郵寄記錄資料
     * @param recordId 記錄ID
     * @param data 更新參數
     * @param file 可選的附件檔案
     */
    updateMailRecord: (
      recordId: string,
      data: UpdateMailRecordRequest,
      file?: File
    ) => {
      if (file) {
        return fileService
          .uploadFile(file, recordId, CollectionEnum.MAIL_RECORDS)
          .then((response) => {
            if (response.success && response.data) {
              const updatedData = {
                ...data,
                file_relative_path: response.data.relativePath,
              };
              return apiClient.put<MailRecordResponse>(
                `/mail-records/${recordId}`,
                updatedData
              );
            }
            return apiClient.put<MailRecordResponse>(
              `/mail-records/${recordId}`,
              data
            );
          });
      }
      return apiClient.put<MailRecordResponse>(
        `/mail-records/${recordId}`,
        data
      );
    },

    /**
     * 刪除郵寄記錄
     * @param recordId 記錄ID
     */
    deleteMailRecord: (recordId: string) => {
      return apiClient.delete<boolean>(`/mail-records/${recordId}`);
    },

    /**
     * 獲取特定部門的所有郵寄記錄
     * @param department 部門名稱
     */
    getRecordsByDepartment: (department: string) => {
      return apiClient.get<MailRecordResponse[]>(
        `/mail-records/by-department/${department}`
      );
    },

    /**
     * 獲取特定客戶的所有郵寄記錄
     * @param customerId 客戶ID
     */
    getRecordsByCustomer: (customerId: string) => {
      return apiClient.get<MailRecordResponse[]>(
        `/mail-records/by-customer/${customerId}`
      );
    },
  },
};
