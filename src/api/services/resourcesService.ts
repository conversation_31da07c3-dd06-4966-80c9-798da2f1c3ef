import apiClient from "../apiClient";
import type {
  CreateResourceRequest,
  ResourceListResponse,
  ResourceResponse,
  ResourceType,
  UpdateResourceRequest,
} from "../models/resources";

/**
 * 資源服務
 */
export const resourcesService = {
  /**
   * 創建新資源
   * @param data 資源資料
   */
  createResource: (data: CreateResourceRequest) => {
    return apiClient.post<ResourceResponse>("/resources/", data);
  },

  /**
   * 獲取資源列表
   * @param params 查詢參數
   */
  getAllResources: (params?: {
    page?: number;
    limit?: number;
    resource_type?: ResourceType;
    resource_group?: string;
    title?: string;
    media_date_from?: string;
    media_date_to?: string;
    created_by?: string;
  }) => {
    return apiClient.get<ResourceListResponse>("/resources/", params);
  },

  /**
   * 根據ID獲取資源
   * @param resourceId 資源ID
   */
  getResource: (resourceId: string) => {
    return apiClient.get<ResourceResponse>(`/resources/${resourceId}`);
  },

  /**
   * 更新資源資料
   * @param resourceId 資源ID
   * @param data 更新資料
   */
  updateResource: (resourceId: string, data: UpdateResourceRequest) => {
    return apiClient.put<ResourceResponse>(`/resources/${resourceId}`, data);
  },

  /**
   * 刪除資源
   * @param resourceId 資源ID
   */
  deleteResource: (resourceId: string) => {
    return apiClient.delete<boolean>(`/resources/${resourceId}`);
  },
};
