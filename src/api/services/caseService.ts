import apiClient from "../apiClient";
import type {
  AddCaseLogRequest,
  CaseFilterRequest,
  CaseListResponse,
  CaseResponse,
  CreateCaseRequest,
  UpdateCaseRequest,
} from "../models/case";

/**
 * 案件服務
 */
export const caseService = {
  /**
   * 創建新案件
   * @param data 案件資料
   */
  createCase: (data: CreateCaseRequest) => {
    return apiClient.post<CaseResponse>("/cases/", data);
  },

  /**
   * 獲取案件列表
   * @param params 查詢參數
   */
  getAllCases: (
    params?: {
      page?: number;
      limit?: number;
    } & Partial<CaseFilterRequest>
  ) => {
    return apiClient.get<CaseListResponse>("/cases/", params);
  },

  /**
   * 根據ID獲取案件
   * @param caseId 案件ID
   */
  getCase: (caseId: string) => {
    return apiClient.get<CaseResponse>(`/cases/${caseId}`);
  },

  /**
   * 更新案件資料
   * @param caseId 案件ID
   * @param data 更新資料
   */
  updateCase: (caseId: string, data: UpdateCaseRequest) => {
    return apiClient.put<CaseResponse>(`/cases/${caseId}`, data);
  },

  /**
   * 刪除案件
   * @param caseId 案件ID
   */
  deleteCase: (caseId: string) => {
    return apiClient.delete<boolean>(`/cases/${caseId}`);
  },

  /**
   * 主管核准案件
   * @param caseId 案件ID
   */
  approveCase: (caseId: string) => {
    return apiClient.post<CaseResponse>(`/cases/${caseId}/approve`);
  },

  /**
   * 結案
   * @param caseId 案件ID
   */
  closeCase: (caseId: string) => {
    return apiClient.post<CaseResponse>(`/cases/${caseId}/close`);
  },

  /**
   * 獲取案件日誌列表
   * @param caseId 案件ID
   * @param params 分頁參數
   */
  getCaseLogs: (caseId: string, params?: { page?: number; limit?: number }) => {
    return apiClient.get<{
      items: Array<{
        id: string;
        status: string;
        process_date: string;
        description: string;
        handler_name: string;
        file_relative_path?: string;
        file_download_url?: string;
      }>;
      total_count: number;
    }>(`/cases/${caseId}/case-logs`, params);
  },

  /**
   * 添加案件處理紀錄
   * @param caseId 案件ID
   * @param data 處理紀錄資料
   */
  addCaseLog: (caseId: string, data: AddCaseLogRequest) => {
    return apiClient.post<CaseResponse>(`/cases/${caseId}/case-logs`, data);
  },

  /**
   * 修改案件處理紀錄
   * @param caseId 案件ID
   * @param logId 處理紀錄ID
   * @param data 處理紀錄資料
   */
  updateCaseLog: (caseId: string, logId: string, data: AddCaseLogRequest) => {
    return apiClient.put<CaseResponse>(
      `/cases/${caseId}/case-logs/${logId}`,
      data
    );
  },

  /**
   * 刪除案件處理紀錄
   * @param caseId 案件ID
   * @param logId 處理紀錄ID
   */
  deleteCaseLog: (caseId: string, logId: string) => {
    return apiClient.delete<CaseResponse>(
      `/cases/${caseId}/case-logs/${logId}`
    );
  },
};
