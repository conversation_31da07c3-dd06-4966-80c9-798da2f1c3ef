import apiClient from "../apiClient";
import type {
  TripHotspotsResponse,
  UserActivityResponse,
  UserDeviceDistributionResponse,
  DashboardQuery,
} from "../models/dashboard";

export const dashboardService = {
  getTripHotspots: (params?: DashboardQuery) => {
    return apiClient.get<TripHotspotsResponse>("/dashboard/trip-hotspots", params);
  },

  getUserActivity: () => {
    return apiClient.get<UserActivityResponse>("/dashboard/user-activity");
  },

  getUserDeviceDistribution: () => {
    return apiClient.get<UserDeviceDistributionResponse>("/dashboard/user-device-distribution");
  },
};