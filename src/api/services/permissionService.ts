import apiClient from "../apiClient";
import type {
  BatchPermissionUpdateResponse,
  BatchUpdatePermissionRequest,
  CheckPermissionRequest,
  CheckPermissionResponse,
  InitPermissionResponse,
  PermissionDetailResponse,
  PermissionListResponse,
  PermissionUpdateResponse,
  RolePermissionItem,
  UpdatePermissionRequest,
} from "../models/permissions";

/**
 * 權限管理服務
 */
export const permissionService = {
  /**
   * 獲取角色的所有權限
   * @param roleId 角色ID
   */
  getRolePermissions: (roleId: string) => {
    return apiClient.get<RolePermissionItem[]>(`/permissions/role/${roleId}`);
  },

  /**
   * 為角色初始化所有模組的空權限記錄
   * @param roleId 角色ID
   */
  initEmptyPermission: (roleId: string) => {
    return apiClient.post<InitPermissionResponse>(
      `/permissions/role/${roleId}/init`
    );
  },

  /**
   * 更新單個權限記錄的actions
   * @param permissionId 權限ID
   * @param data 更新權限請求數據
   */
  updatePermission: (permissionId: string, data: UpdatePermissionRequest) => {
    return apiClient.patch<PermissionUpdateResponse>(
      `/permissions/${permissionId}`,
      data
    );
  },

  /**
   * 批量更新多個權限記錄的actions
   * @param data 批量更新權限請求數據
   */
  batchUpdatePermissions: (data: BatchUpdatePermissionRequest) => {
    return apiClient.patch<BatchPermissionUpdateResponse>(
      "/permissions/batch",
      data
    );
  },

  /**
   * 檢查當前用戶是否有特定權限
   * @param data 檢查權限請求數據
   */
  checkPermission: (data: CheckPermissionRequest) => {
    return apiClient.post<CheckPermissionResponse>("/permissions/check", data);
  },
};
