import apiClient from "../apiClient";
import type {
  ChangePasswordRequest,
  CreateUserRequest,
  UpdateUserRequest,
  UserListResponse,
  UserResponse,
} from "../models/users";

/**
 * 用戶服務
 */
export const userService = {
  /**
   * 創建新用戶
   * @param data 用戶資料
   */
  createUser: (data: CreateUserRequest) => {
    return apiClient.post<UserResponse>("/users/", data);
  },

  /**
   * 獲取用戶列表
   * @param params 查詢參數
   */
  getUsers: (params?: {
    page?: number;
    limit?: number;
    account?: string;
    user_name?: string;
    role_id?: string;
    department?: string;
  }) => {
    return apiClient.get<UserListResponse>("/users/", params);
  },

  /**
   * 根據ID獲取用戶
   * @param userId 用戶ID
   */
  getUser: (userId: string) => {
    return apiClient.get<UserResponse>(`/users/${userId}`);
  },

  /**
   * 更新用戶資料
   * @param userId 用戶ID
   * @param data 更新資料
   */
  updateUser: (userId: string, data: UpdateUserRequest) => {
    return apiClient.put<UserResponse>(`/users/${userId}`, data);
  },

  /**
   * 刪除用戶
   * @param userId 用戶ID
   */
  deleteUser: (userId: string) => {
    return apiClient.delete<boolean>(`/users/${userId}`);
  },

  /**
   * 根據帳號獲取用戶
   * @param account 用戶帳號
   */
  getUserByAccount: (account: string) => {
    return apiClient.get<UserResponse | null>(`/users/by-account/${account}`);
  },

  /**
   * 更改密碼
   * @param data 密碼更改資料
   */
  changePassword: (data: ChangePasswordRequest) => {
    return apiClient.post<boolean>("/users/change-password", data);
  },
};
