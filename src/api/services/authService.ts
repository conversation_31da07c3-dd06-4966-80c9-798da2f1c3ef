import apiClient from "../apiClient";
import type {
  CurrentUserResponse,
  LoginRequest,
  RefreshTokenQuery,
  TokenResponse,
  UserPermissionsResponse,
} from "../models/auth";

/**
 * 身份驗證服務
 */
export const authService = {
  /**
   * 用戶登入
   * @param data 登入資料
   */
  login: (data: LoginRequest) => {
    return apiClient.post<TokenResponse>("/auth/login", data);
  },

  /**
   * 用戶登出
   */
  logout: () => {
    return apiClient.post<boolean>("/auth/logout");
  },

  /**
   * 獲取當前用戶信息
   */
  getCurrentUser: () => {
    return apiClient.get<CurrentUserResponse>("/auth/me");
  },

  /**
   * 獲取用戶權限
   */
  getUserPermissions: () => {
    return apiClient.get<UserPermissionsResponse>("/auth/permissions");
  },

  /**
   * 刷新訪問令牌
   * @param refreshToken 刷新令牌
   */
  refreshToken: (refreshToken: string) => {
    return apiClient.post<TokenResponse>("/auth/refresh", undefined, {
      headers: {
        "refresh-token": refreshToken,
      },
    });
  },

  /**
   * 檢查用戶是否認證
   */
  checkAuth: () => {
    return apiClient.get<boolean>("/auth/check");
  },
};
