import apiClient from "../apiClient";
import { type EnumResponse, EnumType } from "../models/enum";

/**
 * 列舉服務
 */
export const enumService = {
  /**
   * 獲取指定類型的列舉值列表
   * @param enumType 列舉類型
   */
  getEnumValues: (enumType: EnumType) => {
    return apiClient.get<EnumResponse>(`/enums/${enumType}`);
  },

  /**
   * 獲取所別列表
   */
  getDepartments: () => {
    return enumService.getEnumValues(EnumType.DEPARTMENTS);
  },

  /**
   * 獲取服務項目列表
   */
  getServiceItems: () => {
    return enumService.getEnumValues(EnumType.SERVICE_ITEMS);
  },

  /**
   * 獲取處理狀態列表
   */
  getProcessingStatus: () => {
    return enumService.getEnumValues(EnumType.PROCESSING_STATUS);
  },
};
