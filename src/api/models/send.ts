/**
 * 檔案對象模型
 */
export interface FileObject {
  extension: string;
  originalFile: string;
  size: number;
  type: string;
  url: string;
}

/**
 * 訊息發送類別枚舉
 */
export enum CategoryEnum {
  APPLICATION_REPORT = "申報及報表",
  VIDEO_DATA = "影片資料",
  IMPORTANT_MESSAGE = "重要訊息",
}

/**
 * 訊息發送類型枚舉
 */
export enum SendTypeEnum {
  LINE = "Line",
  EMAIL = "Email",
}

/**
 * 聯繫方式類型枚舉
 */
export enum ContactTypeEnum {
  EMAIL = "email",
  LINE = "line",
}

/**
 * 發送訊息請求模型
 */
export interface SendRequest {
  /** 單選類別 */
  category: CategoryEnum;
  /** 發送類別 */
  send_type: SendTypeEnum;
  /** 收件人（多筆用逗號分隔） */
  recipients: string[];
  /** 主旨 */
  subject: string;
  /** 內容 */
  content: string;
}

/**
 * 發送訊息回應模型
 */
export interface SendResponse {
  /** 操作是否成功 */
  success: boolean;
  /** 回應訊息 */
  message: string;
  /** 發送時間 */
  sent_time: string;
  /** 是否成功保存記錄 */
  records_saved?: boolean;
  /** 檢測到的文件 */
  files_detected?: object[];
}

/**
 * 發送記錄回應模型
 */
export interface SendRecordResponse {
  /** 發送記錄ID */
  _id: string;
  /** 使用者ID */
  userId: string;
  /** 檔案對象 */
  fileObject?: FileObject;
  /** 額外檔案 */
  additionalFiles?: FileObject[];
  /** 發送時間 */
  send_time: string;
  /** 主旨 */
  title: string;
  /** 內容 */
  content: string;
  /** 分類 */
  category: string;
  /** 發送類型 */
  send_type: string;
  /** 創建時間 */
  created_at: string;
  /** 更新時間 */
  updated_at?: string;
  /** 用戶名稱 */
  user_name?: string;
  /** 收件人 */
  recipient?: string;
  /** 客戶名稱 */
  customer_name?: string;
  /** 客戶稅籍編號 */
  customer_tax_id?: string;
  /** 客戶編號 */
  customer_number?: string;
}

/**
 * 發送記錄列表回應模型
 */
export interface SendRecordListResponse {
  /** 發送記錄列表 */
  records: SendRecordResponse[];
  /** 總記錄數 */
  total: number;
  /** 當前頁碼 */
  page: number;
  /** 每頁顯示數量 */
  limit: number;
}

/**
 * 用戶響應模型（用於訊息發送中的用戶選擇）
 */
export interface SendUserResponse {
  /** 用戶名稱 */
  user_name: string;
  /** 收件人（聯繫方式） */
  recipient: string;
  /** 客戶稅籍編號 */
  customer_tax_id: string;
  /** 客戶編號 */
  customer_number: string;
}

/**
 * 用戶列表回應模型
 */
export interface SendUserListResponse {
  /** 總記錄數 */
  total: number;
  /** 當前頁碼 */
  page: number;
  /** 每頁顯示數量 */
  limit: number;
  /** 用戶列表 */
  data: SendUserResponse[];
}

/**
 * 批次刪除請求模型
 */
export interface BatchDeleteRequest {
  /** 要刪除的記錄ID列表（必填，至少包含一個ID） */
  record_ids: string[];
}

/**
 * 批次刪除回應模型
 */
export interface BatchDeleteResponse {
  /** 操作是否成功 */
  success: boolean;
  /** 成功刪除的記錄數量 */
  deleted_count: number;
  /** 刪除失敗的記錄數量 */
  failed_count: number;
  /** 刪除失敗的記錄ID列表 */
  failed_ids: string[];
  /** 操作結果訊息 */
  message: string;
}
