// 文件相關模型（發文和郵寄管理）

// ===== 發文管理 =====

/**
 * 發文管理列表響應模型
 */
export interface OutgoingDocumentListResponse {
  /** 總數量 */
  total_count: number;
  /** 發文記錄列表 */
  items: OutgoingDocumentResponse[];
}

/**
 * 發文管理響應模型
 */
export interface OutgoingDocumentResponse {
  /** 發文記錄ID */
  id: string;
  /** 所別 */
  department: string;
  /** 發文日期 */
  send_date: string;
  /** 文件類別（來自文件類別設定） */
  document_type: string;
  /** 客戶編號（可選填） */
  customer_number?: string;
  /** 客戶名稱（可選填） */
  customer_name?: string;
  /**  承辦人員名稱 */
  handler?: string;
  /** 文號（自由輸入或自動生成） */
  document_number: string;
  /** 內文（純文字或 HTML） */
  content: string;
  /** 發文文件的相對路徑 */
  file_relative_path?: string;
  /** 發文文件的下載連結 */
  file_download_url?: string;
  /** 創建時間 */
  created_at: string;
  /** 創建人員帳號 */
  created_by: string;
  /** 更新時間 */
  updated_at: string;
  /** 更新人員帳號 */
  updated_by: string;
}

/**
 * 創建發文管理的請求模型
 */
export interface CreateOutgoingDocumentRequest {
  /** 所別 */
  department: string;
  /** 發文日期 */
  send_date: string;
  /** 文件類別（來自文件類別設定） */
  document_type: string;
  /** 客戶編號（可選填） */
  customer_number?: string;
  /** 客戶名稱（可選填） */
  customer_name?: string;
  /** 內文（純文字或 HTML） */
  content: string;
  /** 發文文件的相對路徑 */
  file_relative_path?: string;
}

/**
 * 更新發文管理的請求模型
 */
export interface UpdateOutgoingDocumentRequest {
  /** 所別 */
  department?: string;
  /** 發文日期 */
  send_date?: string;
  /** 文件類別（來自文件類別設定） */
  document_type?: string;
  /** 客戶編號（可選填） */
  customer_number?: string;
  /** 客戶名稱（可選填） */
  customer_name?: string;
  /** 內文（純文字或 HTML） */
  content?: string;
  /** 發文文件的相對路徑 */
  file_relative_path?: string;
}

export interface DocumentResponse {
  /** 文件記錄ID */
  id: string;
  /** 所別 */
  department: string;
  /** 文件日期 */
  date: string;
  /** 文件類別（來自文件類別設定） */
  document_type: string;
  /** 客戶名稱（可選填） */
  customer_name?: string;
  /** 文號（自由輸入或自動生成） */
  document_number: string;
  /** 內文（純文字或 HTML） */
  content: string;
  /** 處理狀態 */
  status?: string;
  /** 承辦人 */
  handler_id?: string;
  /** 創建時間 */
  created_at: string;
  /** 創建人員帳號 */
  created_by: string;
  /** 更新時間 */
  updated_at: string;
  /** 更新人員帳號 */
  updated_by: string;
}

// ===== 郵寄管理 =====

/**
 * 郵寄管理列表響應模型
 */
export interface MailRecordListResponse {
  /** 總數量 */
  total_count: number;
  /** 郵寄記錄列表 */
  items: MailRecordResponse[];
}

/**
 * 郵寄管理響應模型
 */
export interface MailRecordResponse {
  /** 郵寄記錄ID */
  id: string;
  /** 所別（寄出部門） */
  department: string;
  /** 寄件日期 */
  date: string;
  /** 文件類別（下拉選單來自 document_categories） */
  document_type: string;
  /** 客戶編號（可選填） */
  customer_number?: string;
  /** 對應客戶（來自 customers） */
  customer_name?: string;
  /** 掛號或快遞單號 */
  tracking_number: string;
  /** 寄件內容簡述 */
  content: string;
  /** 創建時間 */
  created_at: string;
  /** 創建人員帳號 */
  created_by: string;
  /** 更新時間 */
  updated_at: string;
  /** 更新人員帳號 */
  updated_by: string;
}

/**
 * 創建郵寄管理的請求模型
 */
export interface CreateMailRecordRequest {
  /** 所別（寄出部門） */
  department: string;
  /** 寄件日期 */
  date: string;
  /** 文件類別（下拉選單來自 document_categories） */
  document_type: string;
  /** 客戶編號（可選填） */
  customer_number?: string;
  /** 對應客戶（來自 customers） */
  customer_name?: string;
  /** 掛號或快遞單號 */
  tracking_number: string;
  /** 寄件內容簡述 */
  content: string;
}

/**
 * 更新郵寄管理的請求模型
 */
export interface UpdateMailRecordRequest {
  /** 所別（寄出部門） */
  department?: string;
  /** 寄件日期 */
  date?: string;
  /** 文件類別（下拉選單來自 document_categories） */
  document_type?: string;
  /** 客戶編號（可選填） */
  customer_number?: string;
  /** 對應客戶（來自 customers） */
  customer_name?: string;
  /** 掛號或快遞單號 */
  tracking_number?: string;
  /** 寄件內容簡述 */
  content?: string;
}
