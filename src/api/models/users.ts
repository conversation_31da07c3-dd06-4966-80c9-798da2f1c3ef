// 用戶相關模型

/**
 * 用戶列表響應模型
 */
export interface UserListResponse {
  /** 總數量 */
  total_count: number;
  /** 用戶列表 */
  items: UserResponse[];
}

/**
 * 用戶響應模型
 */
export interface UserResponse {
  id: string;
  /** 使用者代碼（登入帳號） */
  account: string;
  /** 使用者名稱 */
  user_name: string;
  /** 職位 */
  position: string;
  /** 所屬角色ID */
  role_id: string;
  /** 所別（部門） */
  department: string;
  /** 員工編號 */
  employee_no: string;
  /** 角色名稱 */
  role_name?: string;
  /** 創建時間 */
  created_at: string;
  /** 更新時間 */
  updated_at: string;
}

/**
 * 創建用戶的請求模型
 */
export interface CreateUserRequest {
  /** 使用者代碼（登入帳號） */
  account: string;
  /** 使用者名稱 */
  user_name: string;
  /** EIP 密碼 */
  eip_password: string;
  /** 職位 */
  position: string;
  /** 所屬角色ID */
  role_id: string;
  /** 所別（部門） */
  department: string;
  /** 員工編號 */
  employee_no: string;
}

/**
 * 更新用戶的請求模型
 */
export interface UpdateUserRequest {
  /** 使用者名稱 */
  user_name?: string;
  /** 職位 */
  position?: string;
  /** 所屬角色ID */
  role_id?: string;
  /** 所別（部門） */
  department?: string;
  /** 員工編號 */
  employee_no?: string;
}

/**
 * 密碼更改請求模型
 */
export interface ChangePasswordRequest {
  /** 使用者帳號 */
  account: string;
  /** 當前密碼 */
  current_password: string;
  /** 新密碼 */
  new_password: string;
  /** 密碼類型（EIP或ERP） */
  password_type: string;
}
