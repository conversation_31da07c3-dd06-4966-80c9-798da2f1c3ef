// 身份驗證相關模型

/**
 * 登入請求模型
 */
export interface LoginRequest {
  /** 登入信箱 */
  account: string;
  /** 登入密碼 */
  password: string;
}

/**
 * 令牌回應模型
 */
export interface TokenResponse {
  /** 回應訊息 */
  message: string;
  /** 訪問令牌 */
  token: string;
  /** 用戶資訊 */
  user: {
    _id: string;
    account: string;
    password: string;
    role: string;
    is_active: boolean;
    is_admin: boolean;
    created_at: string;
    updated_at: string;
  };
}

/**
 * 當前用戶信息響應模型
 */
export interface CurrentUserResponse {
  /** 帳號 */
  account: string;
  /** 用戶名稱 */
  user_name: string;
  /** 角色ID */
  role_id?: string | null;
  /** 角色名稱 */
  role_name?: string | null;
  /** 部門 */
  department?: string | null;
  /** 職位 */
  position?: string | null;
  /** 主體標識符 */
  sub?: string | null;
  /** 其他額外屬性 */
  [key: string]: any;
}

/**
 * 權限操作類型
 */
export type PermissionAction = "view" | "create" | "update" | "delete";

/**
 * 模組權限模型
 */
export interface PermissionModuleItem {
  /** 模組ID */
  module_id: string;
  /** 模組代碼 */
  module_code: string;
  /** 模組名稱 */
  module_name: string;
  /** 排序順序 */
  sort_order: number;
  /** 父模組ID */
  parent_id?: string | null;
  /** 權限ID */
  id?: string | null;
  /** 操作列表 */
  actions: string[];
  /** 創建時間 */
  created_at?: string | null;
  /** 更新時間 */
  updated_at?: string | null;
}

/**
 * 用戶權限回應模型
 */
export interface UserPermissionsResponse {
  /** 用戶信息 */
  user: UserInfoResponse;
  /** 權限列表 */
  permissions_list: PermissionModuleItem[];
}

/**
 * 用戶信息響應模型
 */
export interface UserInfoResponse {
  /** 帳號 */
  account: string;
  /** 用戶名稱 */
  user_name: string;
  /** 角色ID */
  role_id: string;
  /** 角色名稱 */
  role_name: string;
  /** 部門 */
  department?: string | null;
  /** 職位 */
  position?: string | null;
}

/**
 * 刷新令牌請求模型
 */
export interface RefreshTokenQuery {
  /** 刷新令牌 */
  refresh_token: string;
}
