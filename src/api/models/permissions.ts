// 權限管理相關模型

/**
 * 權限列表響應模型
 */
export interface PermissionListResponse {
  /** 總數量 */
  total_count: number;
  /** 權限列表 */
  items: PermissionDetailResponse[];
}

/**
 * 權限詳細響應模型
 */
export interface PermissionDetailResponse {
  id: string;
  /** 對應角色 */
  role_id: string;
  /** 角色名稱 */
  role_name?: string;
  /** 模組代碼 */
  module_code: string;
  /** 模組名稱 */
  module_name?: string;
  /** 權限行為 */
  actions: string[];
  /** 創建時間 */
  created_at: string;
  /** 更新時間 */
  updated_at: string;
}

/**
 * 批量更新權限請求模型
 */
export interface BatchUpdatePermissionsRequest {
  /** 角色ID */
  role_id: string;
  /** 權限項目列表 */
  permissions: {
    /** 模組代碼 */
    module_code: string;
    /** 權限行為 */
    actions: string[];
  }[];
}

/**
 * 檢查權限請求模型
 */
export interface CheckPermissionRequest {
  /** 模組代碼 */
  module_code: string;
  /** 權限行為 */
  action: string;
}

/**
 * 檢查權限響應模型
 */
export interface CheckPermissionResponse {
  /** 是否有權限 */
  has_permission: boolean;
}

/**
 * 更新權限請求模型
 */
export interface UpdatePermissionRequest {
  /** 允許的操作列表 */
  actions: string[];
}

/**
 * 權限更新響應模型
 */
export interface PermissionUpdateResponse {
  id: string;
  /** 角色ID */
  role_id: string;
  /** 模組代碼 */
  module_code: string;
  /** 更新後的操作列表 */
  actions: string[];
  /** 創建時間 */
  created_at?: string;
  /** 更新時間 */
  updated_at?: string;
}

/**
 * 批量更新權限請求模型
 */
export interface BatchUpdatePermissionRequest {
  /** 權限更新列表 */
  permission_updates: {
    /** 權限ID */
    permission_id: string;
    /** 權限行為 */
    actions: string[];
  }[];
}

/**
 * 批量權限更新響應模型
 */
export interface BatchPermissionUpdateResponse {
  /** 操作是否全部成功 */
  success: boolean;
  /** 更新成功的項目 */
  updated: BatchUpdateResult[];
  /** 錯誤項目 */
  errors: BatchUpdateErrorItem[];
  /** 未找到的權限ID */
  not_found: string[];
  /** 統計摘要 */
  summary: Record<string, number>;
}

/**
 * 批量更新結果項目
 */
export interface BatchUpdateResult {
  /** 權限ID */
  id: string;
  /** 模組代碼 */
  module_code: string;
  /** 更新後的操作列表 */
  actions: string[];
}

/**
 * 批量更新錯誤項目
 */
export interface BatchUpdateErrorItem {
  /** 權限ID */
  permission_id?: string;
  /** 錯誤信息 */
  error: string;
}

/**
 * 角色權限項目模型
 */
export interface RolePermissionItem {
  /** 模組ID */
  module_id: string;
  /** 模組代碼 */
  module_code: string;
  /** 模組名稱 */
  module_name: string;
  /** 排序順序 */
  sort_order: number;
  /** 父模組ID */
  parent_id?: string;
  /** 角色ID */
  role_id: string;
  /** 角色名稱 */
  role_name: string;
  /** 權限ID */
  id?: string;
  /** 操作列表 */
  actions: string[];
  /** 創建時間 */
  created_at?: string;
  /** 更新時間 */
  updated_at?: string;
}

/**
 * 初始化權限響應模型
 */
export interface InitPermissionResponse {
  /** 操作是否成功 */
  success: boolean;
  /** 角色ID */
  role_id: string;
  /** 角色名稱 */
  role_name: string;
  /** 統計信息 */
  stats: Record<string, number>;
  /** 操作消息 */
  message: string;
}
