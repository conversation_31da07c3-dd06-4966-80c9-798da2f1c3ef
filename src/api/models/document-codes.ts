// 文件類別設定相關模型

/**
 * 文件類別設定列表響應模型
 */
export interface DocumentCodeListResponse {
  /** 總數量 */
  total_count: number;
  /** 文件類別設定列表 */
  items: DocumentCodeResponse[];
}

/**
 * 文件類別設定響應模型
 */
export interface DocumentCodeResponse {
  id?: string;
  /** 代碼（唯一識別碼，例如：EXP, NOTI） */
  code?: string;
  /** 名稱（顯示用名稱） */
  name?: string;
  /** 類別（例如：收文、發文、內部通知） */
  type?: string;
  /** 創建時間 */
  created_at?: string;
  /** 創建人員帳號 */
  created_by?: string;
  /** 更新時間 */
  updated_at?: string;
  /** 更新人員帳號 */
  updated_by?: string;
}

/**
 * 創建文件類別設定請求模型
 */
export interface CreateDocumentCodeRequest {
  /** 代碼（唯一識別碼，例如：EXP, NOTI） */
  code: string;
  /** 名稱（顯示用名稱） */
  name: string;
  /** 類別（例如：收文、發文、內部通知） */
  type: string;
}

/**
 * 更新文件類別設定請求模型
 */
export interface UpdateDocumentCodeRequest {
  /** 名稱（顯示用名稱） */
  name?: string;
  /** 類別（例如：收文、發文、內部通知） */
  type?: string;
}
