// 案件類型代碼相關模型

/**
 * 案件類型代碼列表響應模型
 */
export interface CaseCodeListResponse {
  /** 總數量 */
  total_count: number;
  /** 案件類型代碼列表 */
  items: CaseCodeResponse[];
}

/**
 * 案件類型代碼響應模型
 */
export interface CaseCodeResponse {
  /** 案件類型代碼ID */
  id?: string;
  /** 代碼值 */
  code?: string;
  /** 代碼類型 */
  type?: string;
  /** 代碼名稱 */
  name?: string;
  /** 創建時間 */
  created_at?: string;
  /** 創建人員帳號 */
  created_by?: string;
  /** 更新時間 */
  updated_at?: string;
  /** 更新人員帳號 */
  updated_by?: string;
}

/**
 * 創建案件類型代碼請求模型
 */
export interface CreateCaseCodeRequest {
  /** 代碼值 */
  code: string;
  /** 代碼類型 */
  type: string;
  /** 代碼名稱 */
  name: string;
}

/**
 * 更新案件類型代碼請求模型
 */
export interface UpdateCaseCodeRequest {
  /** 代碼值 */
  code?: string;
  /** 代碼類型 */
  type?: string;
  /** 代碼名稱 */
  name?: string;
}
