/**
 * 列舉相關模型
 */

/**
 * 列舉類型
 */
export enum EnumType {
  DEPARTMENTS = "departments",
  SERVICE_ITEMS = "service_items",
  PROCESSING_STATUS = "processing_status",
}

/**
 * 列舉值響應模型
 */
export interface EnumResponse {
  /** 列舉值列表 */
  values: string[];
}

/**
 * 獲取列舉值請求參數
 */
export interface GetEnumValuesParams {
  /** 列舉類型 */
  enumType: EnumType;
}

/**
 * 系統設定類型枚舉
 */
export enum SettingType {
  ZOOM = "zoom",
  LINE = "line",
  SMTP = "smtp",
  SYSTEM = "system",
}
