/**
 * 會議請求狀態枚舉
 */
export enum MeetingRequestStatus {
  PENDING = "pending",
  APPROVED = "approved",
  SCHEDULED = "scheduled",
  REJECTED = "rejected",
  CANCELLED = "cancelled",
  CREATION_FAILED = "creation_failed",
}

/**
 * 管理員審核會議請求的輸入模型
 */
export interface AdminApproveMeetingRequest {
  /** 會議時間 (UTC) */
  meeting_time: string;
  /** 會議時長（分鐘），15-480分鐘 */
  duration?: number;
  /** 給客戶的附加訊息 */
  message?: string;
}

/**
 * 拒絕會議請求的輸入模型
 */
export interface MeetingRejectionRequest {
  /** 拒絕原因 */
  reason: string;
}

/**
 * 管理員取消會議的輸入模型
 */
export interface AdminCancelMeetingRequest {
  /** 取消原因 */
  reason: string;
}

/**
 * 通知重新安排會議的輸入模型
 */
export interface NotifyRescheduleRequest {
  /** 需要重新安排的原因 */
  reason: string;
}

/**
 * 會議請求回應模型
 */
export interface MeetingRequestResponse {
  /** 會議請求 ID */
  id: string;
  /** 請求者用戶 ID */
  requester_user_id: string;
  /** 請求者 email */
  requester_email: string;
  /** 被邀請者的 email 列表 */
  attendees_emails: string[];
  /** 會議主旨 */
  subject: string;
  /** 預計會議時間 */
  meeting_time?: string;
  /** 會議時長（分鐘） */
  duration: number;
  /** 會議狀態 */
  status: MeetingRequestStatus;
  /** Zoom 會議連結 */
  zoom_meeting_link?: string;
  /** Zoom 會議 ID */
  zoom_meeting_id?: string;
  /** 拒絕原因 */
  rejection_reason?: string;
  /** 取消原因 */
  cancellation_reason?: string;
  /** 建立時間 */
  created_at: string;
  /** 更新時間 */
  updated_at: string;
  /** 審批者用戶 ID */
  approved_by_user_id?: string;
  /** 審批時間 */
  approved_at?: string;
  /** 客戶附加訊息 */
  customer_message?: string;
  /** 管理員回覆訊息 */
  admin_message?: string;
}

/**
 * 會議請求列表回應模型
 */
export interface MeetingRequestListResponse {
  total: number;
  page: number;
  limit: number;
  data: MeetingRequestResponse[];
}

/**
 * 列出會議請求的查詢參數
 */
export interface ListMeetingRequestsParams {
  /** 頁數 */
  page?: number;
  /** 每頁記錄數 */
  limit?: number;
  /** 狀態過濾 */
  status?: MeetingRequestStatus;
}
