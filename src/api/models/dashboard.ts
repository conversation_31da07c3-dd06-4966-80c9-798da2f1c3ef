export interface TripHotspot {
  latitude: number;
  longitude: number;
}

export interface TripHotspotsResponse {
  hotspots: TripHotspot[];
  total_trip_count: number;
}

export interface UserActivityResponse {
  active_users_count: number;
  active_users_percentage: number;
  inactive_users_count: number;
  inactive_users_percentage: number;
  total_users: number;
}

export interface UserDeviceDistributionResponse {
  android_user_count: number;
  ios_user_count: number;
}

export interface DashboardQuery {
  startDate?: string;
  endDate?: string;
}