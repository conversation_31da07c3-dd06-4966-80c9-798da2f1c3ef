// 檔案相關模型

/**
 * 檔案集合類型枚舉
 */
export enum CollectionEnum {
  CASES = "cases",
  OUTGOING_DOCUMENTS = "outgoing_documents",
  INCOMING_DOCUMENTS = "incoming_documents",
  MAIL_RECORDS = "mail_records",
  CASE_LOGS = "case_logs",
  RESOURCES = "resources",
}

/**
 * 檔案響應模型
 */
export interface FileResponse {
  /** 檔案ID */
  _id: string;
  /** 原始檔案名 */
  originalName: string;
  /** 儲存的檔案名 */
  storedName: string;
  /** 檔案大小 */
  fileSize: number;
  /** 檔案類型 */
  fileType: string;
  /** 關聯的實體ID */
  refId: string;
  /** 關聯的集合名稱 */
  collection: CollectionEnum;
  /** 儲存路徑 */
  storagePath: string;
  /** 相對路徑 */
  relativePath: string;
  /** 下載URL */
  downloadUrl: string;
  /** 預覽URL */
  previewUrl?: string;
  /** 上傳時間 */
  uploadedAt: string;
}

/**
 * 檔案上傳響應
 */
export interface FileUploadResponse {
  /** 是否成功 */
  success: boolean;
  /** 檔案數據 */
  data?: FileResponse;
  /** 錯誤訊息 */
  error?: string;
}
