// 角色相關模型

/**
 * 角色列表響應模型
 */
export interface RoleListResponse {
  /** 總數量 */
  total_count: number;
  /** 角色列表 */
  items: RoleResponse[];
}

/**
 * 角色響應模型
 */
export interface RoleResponse {
  id: string;
  /** 角色名稱 */
  role_name: string;
  /** 角色描述 */
  role_desc?: string;
  /** 創建時間 */
  created_at: string;
  /** 更新時間 */
  updated_at: string;
}

/**
 * 創建角色的請求模型
 */
export interface CreateRoleRequest {
  /** 角色名稱 */
  role_name: string;
  /** 角色描述 */
  role_desc?: string;
}

/**
 * 更新角色的請求模型
 */
export interface UpdateRoleRequest {
  /** 角色名稱 */
  role_name?: string;
  /** 角色描述 */
  role_desc?: string;
}
