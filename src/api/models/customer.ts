// 客戶相關模型

/**
 * 客戶分頁結果
 */
export interface CustomerPage {
  /** 總記錄數 */
  total: number;
  /** 當前頁碼 */
  page: number;
  /** 每頁大小 */
  limit: number;
  /** 客戶列表 */
  items: CustomerRead[];
}

/**
 * 創建客戶模型
 */
export interface CustomerCreate {
  /** 客戶編號（必填） */
  customer_number: string;
  /** 客戶名稱（必填） */
  customer_name: string;
  /** 客戶簡稱 */
  customer_alias?: string | null;
  /** 負責人 */
  attention_name?: string | null;
  /** 統一編號 */
  customer_tax_id?: string | null;
  /** 郵遞區號 */
  mail_number?: string | null;
  /** 地址 */
  address?: string | null;
  /** 電話 */
  telephone?: string | null;
  /** 傳真 */
  fax?: string | null;
  /** 所別 */
  department?: string | null;
}

/**
 * 讀取客戶模型
 */
export interface CustomerRead {
  /** 客戶編號 */
  customer_number: string;
  /** 客戶名稱 */
  customer_name: string;
  /** 客戶簡稱 */
  customer_alias?: string | null;
  /** 負責人 */
  attention_name?: string | null;
  /** 統一編號 */
  customer_tax_id?: string | null;
  /** 郵遞區號 */
  mail_number?: string | null;
  /** 地址 */
  address?: string | null;
  /** 電話 */
  telephone?: string | null /** 傳真 */;
  fax?: string | null;
  /** 所別 */
  dept_code?: string | null;
  /** 所別名稱 */
  department?: string | null;
  /** 客戶UUID */
  uuid: string;
  /** 創建時間 */
  created_at?: string | null;
  /** 更新時間 */
  updated_at?: string | null;
  /** 建檔人員 */
  create_pid?: string | null;
  /** 修改人員 */
  last_edit_pid?: string | null;
}

/**
 * 更新客戶模型
 */
export interface CustomerUpdate {
  /** 客戶編號 */
  customer_number?: string | null;
  /** 客戶名稱 */
  customer_name?: string | null;
  /** 客戶簡稱 */
  customer_alias?: string | null;
  /** 負責人 */
  attention_name?: string | null;
  /** 統一編號 */
  customer_tax_id?: string | null;
  /** 郵遞區號 */
  mail_number?: string | null;
  /** 地址 */
  address?: string | null;
  /** 電話 */
  telephone?: string | null;
  /** 傳真 */
  fax?: string | null;
  /** 所別 */
  department?: string | null;
}
