// 系統設定相關模型
import type { SettingType } from "./enum";

/**
 * 系統設定響應模型
 */
export interface SystemSettingResponse {
  /** 設定 ID */
  _id: string;
  /** 所別名稱 */
  department: string;
  /** 設定鍵名 */
  setting_key: string;
  /** 設定值 */
  setting_value: string;
  /** 設定類型 */
  setting_type: SettingType;
  /** 設定說明 */
  description?: string;
  /** 是否啟用 */
  is_active: boolean;
  /** 創建時間 */
  created_at: string;
  /** 更新時間 */
  updated_at: string;
  /** 創建者 */
  created_by?: string;
  /** 更新者 */
  updated_by?: string;
}

/**
 * 系統設定列表響應模型
 */
export interface SystemSettingListResponse {
  /** 總數 */
  total: number;
  /** 當前頁數 */
  page: number;
  /** 每頁記錄數 */
  limit: number;
  /** 設定列表 */
  settings: SystemSettingResponse[];
}

/**
 * 更新系統設定請求模型
 */
export interface UpdateSystemSettingRequest {
  /** 設定值 */
  setting_value?: string;
  /** 設定類型 */
  setting_type?: SettingType;
  /** 設定說明 */
  description?: string;
  /** 是否啟用 */
  is_active?: boolean;
}

/**
 * 系統設定查詢參數
 */
export interface SystemSettingListParams {
  /** 頁數 */
  page?: number;
  /** 每頁記錄數 */
  limit?: number;
  /** 所別名稱 */
  department?: string;
  /** 設定類型 */
  setting_type?: SettingType;
  /** 設定鍵名 */
  setting_key?: string;
  /** 是否啟用 */
  is_active?: boolean;
}

/**
 * 初始化部門設定參數
 */
export interface InitializeDepartmentSettingsParams {
  /** 要初始化的所別名稱 */
  department?: string;
}
