/**
 * 資源相關模型
 */

/**
 * 資源類型列舉
 */
export enum ResourceType {
  REPORT = "report",
  VIDEO = "video",
}

/**
 * 資源列表響應模型
 */
export interface ResourceListResponse {
  /** 總數量 */
  total: number;
  /** 當前頁碼 */
  page: number;
  /** 每頁數量 */
  limit: number;
  /** 資源列表 */
  resources: ResourceResponse[];
}

/**
 * 資源響應模型
 */
export interface ResourceResponse {
  /** 資源ID */
  _id: string;
  /** 資源類型 */
  resource_type: ResourceType;
  /** 資源組別 (可選) */
  resource_group?: string;
  /** 標題 */
  title: string;
  /** 媒體日期 */
  media_date: string;
  /** URL (可選) */
  url?: string;
  /** 創建者 */
  created_by: string;
  /** 創建時間 */
  created_at: string;
}

/**
 * 建立資源請求模型
 */
export interface CreateResourceRequest {
  /** 資源類型 */
  resource_type: ResourceType;
  /** 資源組別 (可選) */
  resource_group?: string;
  /** 標題 */
  title: string;
  /** 媒體日期 */
  media_date: string;
  /** URL (可選) */
  url?: string;
}

/**
 * 更新資源請求模型
 */
export interface UpdateResourceRequest {
  /** 資源類型 */
  resource_type?: ResourceType;
  /** 資源組別 */
  resource_group?: string;
  /** 標題 */
  title?: string;
  /** 媒體日期 */
  media_date?: string;
  /** URL */
  url?: string;
}
