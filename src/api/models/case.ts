// 案件相關模型

/**
 * 案件狀態枚舉
 */
export enum CaseStatus {
  DRAFT = "draft",
  PROCESSING = "processing",
  PENDING_APPROVAL = "pending_approval",
  APPROVED = "approved",
  REJECTED = "rejected",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
}

/**
 * 案件優先級枚舉
 */
export enum CasePriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  URGENT = "urgent",
}

/**
 * 案件列表響應模型
 */
export interface CaseListResponse {
  /** 總數量 */
  total_count: number;
  /** 案件列表 */
  items: CaseResponse[];
}

/**
 * 案件響應模型
 */
export interface CaseResponse {
  /** 案件ID */
  id: string;
  /** 所別 */
  department: string;
  /** 委任日期 */
  assign_date: string;
  /** 案件類別 */
  case_code_type: string;
  /** 案號 */
  case_no: string;
  /** 客戶編號 */
  customer_number: string;
  /** 客戶名稱 */
  customer_name: string;
  /** 接案人員姓名 */
  agent_name: string;
  /** 處理人員1姓名 */
  handler1_name?: string;
  /** 處理人員2姓名 */
  handler2_name?: string;
  /** 服務項目 */
  service_item: string;
  /** 服務內容 */
  service_content: string;
  /** 預計完成日 */
  estimated_completion_date: string;
  /** 委任書上傳檔案 URL */
  authorization_upload_url?: string;
  /** 案件相關文件的相對路徑 */
  file_relative_path?: string;
  /** 案件相關文件的下載連結 */
  file_download_url?: string;
  /** 主管核准 */
  is_approved: boolean;
  /** 結案核准 */
  is_closed: boolean;
  /** 結案日期 */
  close_date?: string;
  /** 處理明細列表 */
  case_logs: CaseLog[];
  /** 創建時間 */
  created_at: string;
  /** 創建人員帳號 */
  created_by: string;
  /** 更新時間 */
  updated_at: string;
  /** 更新人員帳號 */
  updated_by: string;
}

/**
 * 案件處理記錄模型
 */
export interface CaseLog {
  /** 處理記錄ID */
  id?: string;
  /** 處理情形，下拉值 */
  status: string;
  /** 處理日期 */
  process_date: string;
  /** 情形說明 */
  description: string;
  /** 處理人員姓名 */
  handler_name: string;
  /** 處理記錄附件文件的相對路徑 */
  file_relative_path?: string;
  /** 處理記錄附件的下載連結 */
  file_download_url?: string;
}

/**
 * 創建案件請求模型
 */
export interface CreateCaseRequest {
  /** 所別 */
  dept: string;
  /** 委任日期 */
  assign_date: string;
  /** 案件類別 */
  case_code_type: string;
  /** 客戶編號 */
  customer_number: string;
  /** 客戶名稱 */
  customer_name: string;
  /** 接案人員姓名 */
  agent_name: string;
  /** 處理人員1姓名 */
  handler1_name?: string;
  /** 處理人員2姓名 */
  handler2_name?: string;
  /** 服務項目，下拉值固定 */
  service_item: string;
  /** 服務內容 */
  service_content: string;
  /** 預計完成日 */
  estimated_completion_date: string;
  /** 委任書上傳檔案的相對路徑 */
  file_relative_path?: string;
}

/**
 * 更新案件請求模型
 */
export interface UpdateCaseRequest {
  /** 所別 */
  dept?: string;
  /** 委任日期 */
  assign_date?: string;
  /** 案號 */
  case_no?: string;
  /** 客戶編號 */
  customer_number: string;
  /** 客戶名稱 */
  customer_name?: string;
  /** 接案人員姓名 */
  agent_name?: string;
  /** 處理人員1姓名 */
  handler1_name?: string;
  /** 處理人員2姓名 */
  handler2_name?: string;
  /** 服務項目，下拉值固定 */
  service_item?: string;
  /** 服務內容 */
  service_content?: string;
  /** 預計完成日 */
  estimated_completion_date?: string;
  /** 委任書上傳檔案 URL */
  authorization_upload_url?: string;
  /** 案件相關文件的相對路徑 */
  file_relative_path?: string;
  /** 主管核准 */
  manager_approved?: boolean;
  /** 結案日期 */
  close_date?: string;
  /** 結案核准 */
  close_approved?: boolean;
}

/**
 * 添加案件處理記錄請求模型
 */
export interface AddCaseLogRequest {
  /** 處理情形，下拉值 */
  status: string;
  /** 處理日期 */
  process_date?: string;
  /** 情形說明 */
  description: string;
  /** 處理人員姓名 */
  handler_name: string;
  /** 處理記錄附件文件的相對路徑 */
  file_relative_path?: string;
}

/**
 * 案件過濾請求模型
 */
export interface CaseFilterRequest {
  /** 所別 */
  dept?: string;
  /** 案號 */
  case_no?: string;
  /** 案號模式匹配 */
  case_no_pattern?: string;
  /** 客戶名稱 */
  customer_name?: string;
  /** 客戶編號 */
  customer_number?: string;
  /** 接案人員姓名 */
  agent_name?: string;
  /** 處理人員姓名 */
  handler_name?: string;
  /** 服務項目 */
  service_item?: string;
  /** 日期起始 */
  date_from?: string;
  /** 日期結束 */
  date_to?: string;
  /** 是否已結案 */
  is_closed?: boolean;
  /** 是否已核准 */
  is_approved?: boolean;
}
