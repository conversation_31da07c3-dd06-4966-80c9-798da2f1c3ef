/**
 * 通知模板相關模型
 */

/**
 * 通知渠道列舉
 */
export enum ChannelType {
  EMAIL = "email",
  LINE = "line",
  SMS = "sms",
  ALL = "all",
}

/**
 * 模板類型列舉
 */
export enum TemplateType {
  LONG = "long",
  SHORT = "short",
}

/**
 * 通知模板列表響應模型
 */
export interface NotificationTemplateListResponse {
  /** 總數量 */
  total: number;
  /** 當前頁碼 */
  page: number;
  /** 每頁數量 */
  limit: number;
  /** 模板列表 */
  templates: NotificationTemplateResponse[];
}

/**
 * 通知模板響應模型
 */
export interface NotificationTemplateResponse {
  /** 模板ID */
  _id: string;
  /** 模板名稱 */
  name: string;
  /** 模板類型 */
  type: TemplateType;
  /** 通知渠道 */
  channels: ChannelType;
  /** 郵件主旨 (可選) */
  subject?: string;
  /** 模板內容 */
  content: string;
  /** 創建者 */
  created_by: string;
  /** 創建時間 */
  created_at: string;
}

/**
 * 建立通知模板請求模型
 */
export interface CreateNotificationTemplateRequest {
  /** 模板名稱 */
  name: string;
  /** 模板類型 */
  type: TemplateType;
  /** 通知渠道 */
  channels: ChannelType;
  /** 郵件主旨 (可選，僅用於電子郵件) */
  subject?: string;
  /** 模板內容 */
  content: string;
}

/**
 * 更新通知模板請求模型
 */
export interface UpdateNotificationTemplateRequest {
  /** 模板名稱 */
  name?: string;
  /** 模板類型 */
  type?: TemplateType;
  /** 通知渠道 */
  channels?: ChannelType;
  /** 郵件主旨 */
  subject?: string;
  /** 模板內容 */
  content?: string;
}
