import axios, { type AxiosRequestConfig, type AxiosResponse } from "axios";

import { t } from "@/locales/i18n";
import userStore from "@/store/userStore";

import { toast } from "sonner";

// 舊版API響應結構，僅供參考，現在API直接返回數據本身
export interface ApiResponse<T = any> {
  status: number;
  message: string;
  data: T;
}

// 創建 axios 實例
const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 10000,
  headers: { "Content-Type": "application/json;charset=utf-8" },
});

// 請求攔截
axiosInstance.interceptors.request.use(
  (config) => {
    // 添加授權令牌
    const userToken = userStore.getState().userToken;

    if (userToken?.accessToken) {
      config.headers.Authorization = `Bearer ${userToken.accessToken}`;
    }
    return config;
  },
  (error) => {
    console.error("API 請求預處理錯誤:", error);
    return Promise.reject(error);
  }
);

// 響應攔截
axiosInstance.interceptors.response.use(
  (res: AxiosResponse) => {
    if (!res.data) throw new Error(t("sys.api.apiRequestFailed"));
    return res.data;
  },
  (error) => {
    let errMsg = t("sys.api.errorMessage");
    let status: number | undefined;

    if (axios.isCancel(error)) {
      return Promise.reject(error);
    }

    if (error.response) {
      status = error.response.status;

      // 錯誤響應可能是純文本或具有特定結構的物件
      const data = error.response.data;
      if (typeof data === "string") {
        errMsg = data;
      } else if (data && typeof data === "object") {
        // 嘗試從常見的錯誤欄位中提取訊息
        errMsg =
          data.message ||
          data.error ||
          data.errorMessage ||
          JSON.stringify(data);
      }

      // 添加更多診斷信息
      console.error(`API 錯誤（狀態: ${status}）:`, {
        url: error.config?.url,
        method: error.config?.method,
        headers: error.config?.headers,
        data: error.response?.data,
      });
    } else if (error.message) {
      errMsg = error.message;
      console.error("API 錯誤（無響應）:", error.message);
    }

    // 顯示錯誤消息
    toast.error(errMsg, {
      position: "top-center",
    });

    // 處理401授權錯誤
    // if (status === 401) {
    //   console.warn("收到 401 未授權錯誤，檢查 token:", {
    //     accessToken: `${userStore
    //       .getState()
    //       .userToken.accessToken?.substring(0, 10)}...`,
    //     userInfo: !!userStore.getState().userInfo,
    //   });

    //   toast.error("身份驗證失敗，正在重新導向至登入頁面", {
    //     position: "top-center",
    //   });

    //   // 使用正確的方法名稱清除用戶數據
    //   userStore.getState().actions.clearUserData();

    //   // 直接重定向到登入頁面
    //   setTimeout(() => {
    //     window.location.href = "/login";
    //   }, 1000); // 延遲1秒以確保用戶可以看到提示訊息
    // }

    return Promise.reject(error);
  }
);

/**
 * 封裝API請求客戶端
 */
class APIClient {
  /**
   * GET請求
   * @param url 請求URL
   * @param params 請求參數
   * @param config 其他配置
   * @returns Promise
   */
  get<T = any>(
    url: string,
    params?: Record<string, any>,
    config?: AxiosRequestConfig
  ): Promise<T> {
    return this.request<T>({
      ...config,
      url,
      method: "GET",
      params,
    });
  }

  /**
   * POST請求
   * @param url 請求URL
   * @param data 請求數據
   * @param config 其他配置
   * @returns Promise
   */
  post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    return this.request<T>({
      ...config,
      url,
      method: "POST",
      data,
    });
  }

  /**
   * PUT請求
   * @param url 請求URL
   * @param data 請求數據
   * @param config 其他配置
   * @returns Promise
   */
  put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    return this.request<T>({
      ...config,
      url,
      method: "PUT",
      data,
    });
  }

  /**
   * DELETE請求
   * @param url 請求URL
   * @param data 請求數據
   * @param config 其他配置
   * @returns Promise
   */
  delete<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    return this.request<T>({
      ...config,
      url,
      method: "DELETE",
      data,
    });
  }

  /**
   * PATCH請求
   * @param url 請求URL
   * @param data 請求數據
   * @param config 其他配置
   * @returns Promise
   */
  patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    return this.request<T>({
      ...config,
      url,
      method: "PATCH",
      data,
    });
  }

  /**
   * 發送請求
   * @param config 請求配置
   * @returns Promise
   */
  request<T = any>(config: AxiosRequestConfig): Promise<T> {
    return new Promise((resolve, reject) => {
      axiosInstance
        .request(config)
        .then((res) => {
          // res 已經是經過響應攔截器處理的 data 部分
          resolve(res as unknown as T);
        })
        .catch((e) => {
          reject(e);
        });
    });
  }

  /**
   * 使用FormData提交表單，自動處理文件上傳
   * @param url API路徑
   * @param formData FormData對象或普通對象（會被轉換為FormData）
   * @param config 其他配置
   * @returns Promise
   */
  submitForm<T = any>(
    url: string,
    formData: FormData | Record<string, any>,
    config?: AxiosRequestConfig
  ): Promise<T> {
    let form: FormData;

    // 如果不是FormData，轉換為FormData
    if (!(formData instanceof FormData)) {
      form = new FormData();
      Object.entries(formData).forEach(([key, value]) => {
        form.append(key, value as string);
      });
    } else {
      form = formData;
    }

    return this.post<T>(url, form, {
      ...(config || {}),
      headers: {
        "Content-Type": "multipart/form-data",
        ...(config?.headers || {}),
      },
    });
  }

  /**
   * 下載檔案
   * @param url 檔案下載路徑
   * @param fileName 可選的檔案名稱，如果不提供則使用響應中的檔案名
   * @returns Promise
   */
  downloadFile(url: string, fileName?: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      axiosInstance({
        url,
        method: "GET",
        responseType: "blob",
      })
        .then((response: any) => {
          // 創建一個臨時的 URL 對象指向 blob 數據
          const blob = new Blob([response]);
          const downloadUrl = window.URL.createObjectURL(blob);

          // 從響應頭中獲取文件名稱，如果沒有則使用傳入的文件名或默認名稱
          let finalFileName = fileName;
          const contentDisposition = response.headers?.["content-disposition"];
          if (contentDisposition && !finalFileName) {
            const filenameMatch = contentDisposition.match(
              /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
            );
            if (filenameMatch?.[1]) {
              finalFileName = filenameMatch[1].replace(/['"]/g, "");
            }
          }

          if (!finalFileName) {
            finalFileName = "download";
          }

          // 創建一個隱藏的 <a> 元素來下載文件
          const link = document.createElement("a");
          link.href = downloadUrl;
          link.setAttribute("download", finalFileName);
          document.body.appendChild(link);
          link.click();

          // 清理
          window.URL.revokeObjectURL(downloadUrl);
          document.body.removeChild(link);

          resolve(true);
        })
        .catch((error) => {
          console.error("檔案下載失敗:", error);
          toast.error("檔案下載失敗", {
            position: "top-center",
          });
          reject(error);
        });
    });
  }
}

export { axiosInstance };

export default new APIClient();
