import { useMemo } from "react";

import { CircleLoading } from "@/components/loading";
import { useUserPermissions } from "@/store/userStore";
import { getRoutesFromModules } from "../utils";

import type { PermissionModuleItem } from "@/api/models/auth";
import type { AppRouteObject } from "#/router";

const ROUTE_MODE = import.meta.env.VITE_APP_ROUTER_MODE;

/**
 * 依據權限過濾路由
 * @param routes 路由列表
 * @param permissions 權限列表
 * @returns 過濾後的路由列表
 */
function filterRoutesByPermissions(
  routes: AppRouteObject[],
  permissions: PermissionModuleItem[]
): AppRouteObject[] {
  return routes.map((route) => {
    // 深複製路由資料，避免修改原始資料
    const newRoute = { ...route };

    if (newRoute.children && newRoute.children.length > 0) {
      // 如果有子路由，直接遞迴處理子路由
      newRoute.children = filterRoutesByPermissions(
        newRoute.children,
        permissions
      );
      return newRoute;
    }

    // 檢查路由是否有 meta.module_code
    if (newRoute.meta?.module_code) {
      // 檢查是否有對應權限
      const matchedPermission = permissions.find(
        (permission) => permission.module_code === newRoute.meta?.module_code
      );

      // 如果找到對應權限，並且權限中包含 "view" 操作
      if (matchedPermission?.actions.includes("view")) {
        return newRoute;
      }

      // 如果沒有對應權限或沒有 view 權限，則隱藏路由
      if (newRoute.meta) {
        newRoute.meta.hideMenu = true;
      }
    }

    return newRoute;
  });
}

/**
 * 返回根據權限過濾後的路由
 */
export function usePermissionRoutes() {
  // 如果是 module 模式，直接返回所有路由
  if (ROUTE_MODE === "module") {
    return getRoutesFromModules();
  }

  // 如果是 permission 模式，根據權限過濾路由
  const permissions = useUserPermissions();

  return useMemo(() => {
    // 獲取基礎路由
    const baseRoutes = getRoutesFromModules();

    // 如果沒有權限資料，返回基礎路由（不過濾）
    if (
      !permissions?.permissions_list ||
      permissions.permissions_list.length === 0
    ) {
      return baseRoutes;
    }

    // 過濾路由
    return filterRoutesByPermissions(baseRoutes, permissions.permissions_list);
  }, [permissions]);
}
