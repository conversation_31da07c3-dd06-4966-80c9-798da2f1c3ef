import Icon from "@/components/icon";
import { Suspense, lazy } from "react";

import { CircleLoading } from "@/components/loading";

import type { AppRouteObject } from "#/router";

const OrderListPage = lazy(() => import("@/pages/orders/OrderList"));

const orders: AppRouteObject = {
  order: 3,
  path: "orders",
  element: (
    <Suspense fallback={<CircleLoading />}>
      <OrderListPage />
    </Suspense>
  ),
  meta: {
    label: "訂單管理",
    icon: <Icon library="lucide" name="FileText" size={16} />,
    key: "/orders",
  },
};

export default orders;
