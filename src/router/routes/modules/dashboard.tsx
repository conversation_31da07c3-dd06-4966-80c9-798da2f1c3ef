import Icon from "@/components/icon";
import { Suspense, lazy } from "react";

import { CircleLoading } from "@/components/loading";

import type { AppRouteObject } from "#/router";

const DashboardPage = lazy(() => import("@/pages/dashboard/index"));

const dashboard: AppRouteObject = {
  order: 1,
  path: "dashboard",
  element: (
    <Suspense fallback={<CircleLoading />}>
      <DashboardPage />
    </Suspense>
  ),
  meta: {
    label: "Dashboard",
    icon: <Icon library="lucide" name="LayoutDashboard" size={16} />,
    key: "/dashboard",
  },
};

export default dashboard;
