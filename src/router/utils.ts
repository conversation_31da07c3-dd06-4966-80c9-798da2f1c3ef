import { ascend } from "ramda";

import type { AppRouteObject, RouteMeta } from "#/router";

/**
 * 過濾選單項目
 * 保留有 meta.key 且非 hideMenu 的路由
 * 如果路由有子項目，將自動遞迴處理
 */
export const menuFilter = (items: AppRouteObject[]) => {
  return items
    .filter((item) => {
      // 如果路由被隱藏，則直接不顯示
      if (item.meta?.hideMenu) {
        return false;
      }

      // 路由需要有 meta.key 才能顯示
      const hasKey = !!item.meta?.key;

      // 如果有子項目，則遞迴處理
      if (item.children) {
        item.children = menuFilter(item.children);

        // 如果過濾後的子項目不為空，則顯示此項目
        // 這確保有子項目的選單不會因為其本身沒有權限而被過濾掉
        return hasKey || item.children.length > 0;
      }

      return hasKey;
    })
    .sort(ascend((item) => item.order || Number.POSITIVE_INFINITY));
};

/**
 * 基于 src/router/routes/modules 文件结构动态生成路由
 */
export function getRoutesFromModules() {
  const menuModules: AppRouteObject[] = [];

  const modules = import.meta.glob("./routes/modules/**/*.tsx", {
    eager: true,
  });
  for (const key in modules) {
    const mod = (modules as any)[key].default || {};
    const modList = Array.isArray(mod) ? [...mod] : [mod];
    menuModules.push(...modList);
  }
  return menuModules;
}

/**
 * return the routes will be used in sidebar menu
 */
export function getMenuRoutes(appRouteObjects: AppRouteObject[]) {
  return menuFilter(appRouteObjects);
}

/**
 * return flatten routes
 */
export function flattenMenuRoutes(routes: AppRouteObject[]) {
  return routes.reduce<RouteMeta[]>((prev, item) => {
    const { meta, children } = item;
    if (meta) prev.push(meta);
    if (children) prev.push(...flattenMenuRoutes(children));
    return prev;
  }, []);
}
