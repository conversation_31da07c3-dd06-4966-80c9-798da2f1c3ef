/**
 * Theme utilities for color manipulation
 *
 * 注意: 大部分這些功能現在可以通過 Tailwind CSS 直接實現:
 * - 使用 text-primary/80 代替 rgbAlpha
 * - 使用主題變量代替手動顏色處理
 */

/**
 * Converts a hex color to RGB channel format
 * @param hex Hex color string (e.g. "#RRGGBB")
 * @returns RGB channel string (e.g. "R, G, B")
 *
 * @deprecated 推薦使用 Tailwind CSS 的顏色系統
 */
export function hexToRgbChannel(hex: string): string {
  // Remove the hash if it exists
  const hexColor = hex.startsWith("#") ? hex.slice(1) : hex;

  // Convert to RGB
  const r = parseInt(hexColor.substring(0, 2), 16);
  const g = parseInt(hexColor.substring(2, 4), 16);
  const b = parseInt(hexColor.substring(4, 6), 16);

  return `${r}, ${g}, ${b}`;
}

/**
 * Creates a rgba color string with the specified opacity
 * @param color Either a RGB channel string (e.g. "R, G, B") or a CSS variable
 * @param alpha Alpha value (0-1)
 * @returns RGBA color string
 *
 * @deprecated 推薦使用 Tailwind CSS 的透明度修飾符，例如 text-primary/80
 * 對於 CSS 變量，請使用 bg-[color:var(--color-variable)]
 */
export function rgbAlpha(color: string, alpha: number): string {
  // Handle CSS variables
  if (color.startsWith("var(--")) {
    return `rgba(${color}, ${alpha})`;
  }

  // Handle regular RGB channel strings
  return `rgba(${color}, ${alpha})`;
}

/**
 * Removes 'px' from a pixel value string and converts it to a number
 * @param value CSS pixel value (e.g. "16px")
 * @returns Number value without 'px'
 *
 * @deprecated 推薦使用 Tailwind CSS 的尺寸系統
 */
export function removePx(value: string): number {
  if (!value) return 0;
  return typeof value === "string"
    ? parseInt(value.replace("px", ""), 10)
    : value;
}

/**
 * 使用 Tailwind CSS 風格創建一個帶有透明度的顏色類名
 * @param baseClass 基礎 Tailwind 類名 (例如 "text-primary")
 * @param opacity 透明度值 (0-100)
 * @returns 帶透明度的 Tailwind 類名 (例如 "text-primary/80")
 */
export function twOpacity(baseClass: string, opacity: number): string {
  // 確保 opacity 在有效範圍內
  const safeOpacity = Math.max(0, Math.min(100, Math.round(opacity * 100)));
  return `${baseClass}/${safeOpacity}`;
}
