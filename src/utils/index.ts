import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  try {
    // 過濾掉非有效值（undefined, null, 非字符串的對象等）
    const validInputs = inputs.filter(
      (input) =>
        input !== undefined &&
        input !== null &&
        (typeof input === "string" ||
          typeof input === "object" ||
          typeof input === "number" ||
          typeof input === "boolean")
    );

    // 確保所有傳入的物件都能正確處理
    const safeInputs = validInputs.map((input) => {
      // 處理非字符串類型
      if (typeof input !== "string" && input !== null) {
        // 如果是對象但不是字符串，進行特殊處理
        if (typeof input === "object" && !(input instanceof String)) {
          try {
            // 深度複製避免修改原對象
            return JSON.parse(JSON.stringify(input));
          } catch (err) {
            console.warn("Failed to process object in cn function:", err);
            return "";
          }
        }
        // 將其他非字符串類型轉為字符串
        return String(input);
      }
      return input;
    });

    const result = twMerge(clsx(safeInputs));
    return typeof result === "string" ? result : "";
  } catch (error) {
    console.error("Error in cn function:", error);
    // 發生錯誤時返回空字串，避免應用中斷
    return "";
  }
}
