import { useSettings } from "@/store/settingStore";
import { useEffect } from "react";
import { ThemeMode } from "#/enum";

interface ThemeProviderProps {
  children: React.ReactNode;
  adapters?: UILibraryAdapter[];
}

// 定義 UI 庫適配器接口
interface UILibraryAdapter {
  (props: { children: React.ReactNode; mode: ThemeMode }): JSX.Element;
  name: string;
}

export function ThemeProvider({ children, adapters = [] }: ThemeProviderProps) {
  const { themeMode, themeColorPresets, fontFamily, fontSize } = useSettings();

  // Update HTML class to support Tailwind dark mode
  useEffect(() => {
    const root = window.document.documentElement;

    // 移除所有主題模式類
    root.classList.remove(ThemeMode.Light, ThemeMode.Dark);

    // 設置當前主題模式類
    root.classList.add(themeMode);

    // 設置 Tailwind 深色模式類 - 如果使用 class 策略
    if (themeMode === ThemeMode.Dark) {
      root.classList.add("dark");
    } else {
      root.classList.remove("dark");
    }

    // 設置顏色預設類
    root.dataset.theme = themeColorPresets;
  }, [themeMode, themeColorPresets]);

  // Update font size and font family
  useEffect(() => {
    const root = window.document.documentElement;
    root.style.fontSize = `${fontSize}px`;

    const body = window.document.body;
    body.style.fontFamily = fontFamily;
  }, [fontFamily, fontSize]);

  // Wrap children with adapters
  const wrappedWithAdapters = adapters.reduce(
    (children, Adapter) => (
      <Adapter key={Adapter.name} mode={themeMode}>
        {children}
      </Adapter>
    ),
    children
  );

  // 使用 Tailwind 類名
  return (
    <div className="min-h-screen bg-bg-default text-text-primary dark:bg-dark-bg-default dark:text-dark-text-primary">
      {wrappedWithAdapters}
    </div>
  );
}
