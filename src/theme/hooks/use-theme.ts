import { useSettingActions, useSettings } from "@/store/settingStore";
import type { ThemeMode } from "#/enum";

/**
 * 主題鉤子，提供當前主題配置
 * 注意：現在使用 Tailwind 配置取代了之前的 tokens 系統
 */
export function useTheme() {
  const settings = useSettings();
  const { setSettings } = useSettingActions();

  return {
    mode: settings.themeMode,
    setMode: (mode: ThemeMode) => {
      setSettings({
        ...settings,
        themeMode: mode,
      });
    },
    // 提供 Tailwind 類名的資訊和當前主題狀態
    settings,
    isDarkMode: settings.themeMode === "dark",
  };
}
