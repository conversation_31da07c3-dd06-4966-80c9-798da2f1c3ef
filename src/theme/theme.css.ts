/**
 * 提供與以前的 themeVars 相容的 API
 * 使用 CSS 變量以保持與之前使用 vanilla-extract 的代碼相容
 */

export const themeVars = {
  colors: {
    palette: {
      primary: {
        lighter: "var(--primary-lighter, #C8FAD6)",
        light: "var(--primary-light, #5BE49B)",
        default: "var(--primary, #00A76F)",
        dark: "var(--primary-dark, #007867)",
        darker: "var(--primary-darker, #004B50)",
      },
      secondary: {
        lighter: "var(--secondary-lighter, #D6E4FF)",
        light: "var(--secondary-light, #84A9FF)",
        default: "var(--secondary, #3366FF)",
        dark: "var(--secondary-dark, #1939B7)",
        darker: "var(--secondary-darker, #091A7A)",
      },
      success: {
        lighter: "var(--success-lighter, #D8FBDE)",
        light: "var(--success-light, #86E8AB)",
        default: "var(--success, #36B37E)",
        dark: "var(--success-dark, #1B806A)",
        darker: "var(--success-darker, #0A5554)",
      },
      warning: {
        lighter: "var(--warning-lighter, #FFF5CC)",
        light: "var(--warning-light, #FFD666)",
        default: "var(--warning, #FFAB00)",
        dark: "var(--warning-dark, #B76E00)",
        darker: "var(--warning-darker, #7A4100)",
      },
      error: {
        lighter: "var(--error-lighter, #FFE9D5)",
        light: "var(--error-light, #FFAC82)",
        default: "var(--error, #FF5630)",
        dark: "var(--error-dark, #B71D18)",
        darker: "var(--error-darker, #7A0916)",
      },
      info: {
        lighter: "var(--info-lighter, #CAFDF5)",
        light: "var(--info-light, #61F3F3)",
        default: "var(--info, #00B8D9)",
        dark: "var(--info-dark, #006C9C)",
        darker: "var(--info-darker, #003768)",
      },
    },
    text: {
      primary: "var(--text-primary, #212B36)",
      secondary: "var(--text-secondary, #637381)",
      disabled: "var(--text-disabled, #919EAB)",
    },
    background: {
      paper: "var(--bg-paper, #FFFFFF)",
      paperChannel: "255, 255, 255",
      default: "var(--bg-default, #F9FAFB)",
      defaultChannel: "249, 250, 251",
      neutral: "var(--bg-neutral, #F4F6F8)",
    },
    common: {
      white: "#FFFFFF",
      black: "#000000",
    },
  },
  borderRadius: {
    none: "0px",
    xs: "2px",
    sm: "4px",
    default: "8px",
    md: "12px",
    lg: "16px",
    xl: "24px",
    full: "9999px",
  },
  shadows: {
    card: "var(--shadow-card, 0px 0px 2px 0px rgba(145, 158, 171, 0.2), 0px 12px 24px -4px rgba(145, 158, 171, 0.12))",
  },
};
