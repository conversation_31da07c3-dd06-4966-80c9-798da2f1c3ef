@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* 自定義全局樣式 */
@layer base {
  html {
    @apply font-primary antialiased;
  }

  body {
    @apply bg-bg-default text-text-primary min-h-screen;
  }

  /* 深色模式 */
  .dark body {
    @apply bg-dark-bg-default text-dark-text-primary;
  }
}

/* 自定義元件樣式 */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded px-4 py-2 font-medium transition-colors;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-dark shadow-primary;
  }

  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary-dark shadow-secondary;
  }

  .input {
    @apply rounded border border-gray-300 px-3 py-2 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary;
  }

  .card {
    @apply rounded-lg bg-bg-paper p-6 shadow-card;
  }

  /* 深色模式的元件樣式 */
  .dark .card {
    @apply bg-dark-bg-paper;
  }

  /* 實用工具類 */
  @layer utilities {
    .scrollbar-none {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }

    .scrollbar-none::-webkit-scrollbar {
      display: none;
    }

    .text-balance {
      text-wrap: balance;
    }
  }
}
