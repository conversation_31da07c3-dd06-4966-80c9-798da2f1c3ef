/* font */
@import "@fontsource-variable/open-sans";
@import "@fontsource-variable/inter";

/* simplebar */
@import "simplebar-react/dist/simplebar.min.css";

/* tailwind */
@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* 全局重置 */
body {
  font-family: "Open Sans Variable", sans-serif;
}

/* 主題相關的樣式 */
:root {
  color-scheme: light dark;

  /* 添加全局 CSS 變數 */
  --primary: #06b6d4;
  --primary-light: #a5f3fc;
  --primary-dark: #0891b2;
  --primary-darker: #0e7490;

  --secondary: #0ea5e9;
  --secondary-light: #bae6fd;
  --secondary-dark: #0284c7;
  --secondary-darker: #0369a1;

  --success: #22c55e;
  --success-light: #86efac;
  --success-dark: #16a34a;

  --warning: #eab308;
  --warning-light: #fde68a;
  --warning-dark: #ca8a04;

  --error: #ef4444;
  --error-light: #fca5a5;
  --error-dark: #dc2626;

  --info: #6366f1;
  --info-light: #a5b4fc;
  --info-dark: #4f46e5;

  --text-primary: #212b36;
  --text-secondary: #637381;
  --text-disabled: #919eab;

  --bg-paper: #ffffff;
  --bg-default: #f9fafb;
  --bg-neutral: #f4f6f8;

  --border: rgba(145, 158, 171, 0.1);
}

/* 亮色主題 */
:root.light {
  color-scheme: light;
}

/* 暗色主題 */
:root.dark {
  color-scheme: dark;

  --primary: #06b6d4;
  --primary-light: #a5f3fc;

  --text-primary: #f9fafb;
  --text-secondary: #dfe3e8;
  --text-disabled: #919eab;

  --bg-paper: #212b36;
  --bg-default: #161c24;
  --bg-neutral: #454f5b;

  --border: rgba(145, 158, 171, 0.24);
}

/* Toast 樣式 */
.toast-wrapper [data-sonner-toast] {
  font-weight: 600;
  font-size: 14px;
}

.toast-wrapper [data-sonner-toast] [data-cancel] {
  color: var(--text-primary, #212b36);
  background-color: transparent;
}

.toast-wrapper [data-sonner-toast] [data-cancel]:hover {
  background-color: rgba(145, 158, 171, 0.08);
}

.toast-wrapper [data-sonner-toast] [data-action] {
  color: var(--primary, #06b6d4);
  background-color: transparent;
}

.toast-wrapper [data-sonner-toast] [data-action]:hover {
  background-color: rgba(6, 182, 212, 0.08);
}

.toast-wrapper [data-sonner-toast][data-type="info"] [data-action] {
  color: var(--info, #6366f1);
  background-color: transparent;
}

.toast-wrapper [data-sonner-toast][data-type="info"] [data-action]:hover {
  background-color: rgba(99, 102, 241, 0.08);
}

.toast-wrapper [data-sonner-toast][data-type="error"] [data-action] {
  color: var(--error, #ef4444);
  background-color: transparent;
}

.toast-wrapper [data-sonner-toast][data-type="error"] [data-action]:hover {
  background-color: rgba(239, 68, 68, 0.08);
}

.toast-wrapper [data-sonner-toast][data-type="success"] [data-action] {
  color: var(--success, #22c55e);
  background-color: transparent;
}

.toast-wrapper [data-sonner-toast][data-type="success"] [data-action]:hover {
  background-color: rgba(34, 197, 94, 0.08);
}

.toast-wrapper [data-sonner-toast][data-type="warning"] [data-action] {
  color: var(--warning, #eab308);
  background-color: transparent;
}

.toast-wrapper [data-sonner-toast][data-type="warning"] [data-action]:hover {
  background-color: rgba(234, 179, 8, 0.08);
}

.toast-wrapper [data-sonner-toast] [data-close-button] {
  top: 0;
  right: 0;
  left: auto;
  border-width: 1px;
  border-style: dashed;
  background-color: var(--bg-paper, #ffffff);
  border-color: var(--border, rgba(145, 158, 171, 0.1));
}

/* Markdown 樣式 */
.markdown-content {
  display: grid;
}

.markdown-content h1 {
  font-size: 64px;
  line-height: 1.25;
  font-weight: 800;
}

.markdown-content h2 {
  font-size: 56px;
  line-height: 1.25;
  font-weight: 700;
}

.markdown-content h3 {
  font-size: 48px;
  line-height: 1.25;
  font-weight: 700;
}

.markdown-content h4 {
  font-size: 40px;
  line-height: 1.25;
  font-weight: 700;
}

.markdown-content h5 {
  font-size: 32px;
  line-height: 1.25;
  font-weight: 700;
}

.markdown-content a {
  color: #1677ff;
}

.markdown-content img {
  border-radius: 4px;
}

.markdown-content br {
  display: grid;
  content: "";
  margin-top: 0.75em;
}

.markdown-content hr {
  margin: 0;
  border-width: 1px;
  border-style: solid;
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 1.5rem;
  list-style-position: outside;
  list-style-type: disc;
}

.markdown-content ol {
  list-style-type: decimal;
}

.markdown-content li {
  display: list-item !important;
  line-height: 2;
}

.markdown-content blockquote {
  line-height: 1.5;
  font-size: 1em;
  margin: 20px 0;
  position: relative;
  padding: 24px 24px 24px 48px;
  border-radius: 16px;
  background-color: var(--bg-neutral, #f4f6f8);
  color: var(--text-secondary, #637381);
}

.markdown-content blockquote p,
.markdown-content blockquote span {
  margin-bottom: 0;
  font-size: inherit;
  font-family: inherit;
}

.markdown-content blockquote::before {
  left: 16px;
  top: -8px;
  display: block;
  font-size: 3em;
  position: absolute;
  content: '"';
}

.markdown-content pre,
.markdown-content pre > code {
  font-size: 16px;
  overflow-x: auto;
  white-space: pre;
  border-radius: 8px;
}

.markdown-content code {
  font-size: 14px;
  border-radius: 4px;
  white-space: pre;
  padding: 0px;
  background-color: var(--bg-neutral, #f4f6f8);
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid var(--border, rgba(145, 158, 171, 0.1));
}

.markdown-content th,
.markdown-content td {
  padding: 8px;
  border: 1px solid var(--border, rgba(145, 158, 171, 0.1));
}

.markdown-content tbody tr:nth-of-type(odd) {
  background-color: var(--bg-neutral, #f4f6f8);
}

.markdown-content input {
  margin-right: 10px;
}

.markdown-content input[type="checkbox"] {
  position: relative;
  cursor: pointer;
}

.markdown-content input[type="checkbox"]::before {
  content: "";
  top: -2px;
  left: -2px;
  width: 17px;
  height: 17px;
  border-radius: 3px;
  position: absolute;
  background-color: #1677ff;
}

.markdown-content input[type="checkbox"]:checked::before {
  background-color: #1677ff;
}

.markdown-content input[type="checkbox"]:checked::after {
  content: "";
  top: 1px;
  left: 5px;
  width: 4px;
  height: 9px;
  position: absolute;
  transform: rotate(45deg);
  border: solid white;
  border-width: 0 2px 2px 0;
}
