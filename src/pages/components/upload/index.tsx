import {
  Card,
  Col,
  Row,
  Space,
  Switch,
  Tabs,
  type TabsProps,
  Typography,
  Checkbox,
} from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import { Upload, UploadAvatar, UploadBox } from "@/components/upload";

export default function UploadPage() {
  const [thumbnail, setThumbnail] = useState<boolean>(false);
  const { t } = useTranslation();

  const onChange = (checked: boolean) => {
    setThumbnail(checked);
  };

  const ThumbnailSwitch = (
    <Switch size="small" checked={thumbnail} onChange={onChange} />
  );

  const boxPlaceHolder = (
    <div className="flex flex-col">
      <Typography.Text type="secondary" className="">
        {t("sys.components.upload.singleFile")}
      </Typography.Text>
    </div>
  );

  const UploadFileTab = (
    <Space direction="vertical" size="middle" style={{ display: "flex" }}>
      <Card
        title={t("sys.components.upload.singleFile")}
        className="w-full"
        extra={ThumbnailSwitch}
      >
        <Upload thumbnail={thumbnail} name="multi" />
      </Card>
      <Card
        title={t("sys.components.upload.singleFile")}
        extra={ThumbnailSwitch}
      >
        <Upload thumbnail={thumbnail} maxCount={1} name="single" />
      </Card>
    </Space>
  );

  const UploadAvatarTab = (
    <Card
      title={t("sys.components.upload.avatar")}
      styles={{
        body: {
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
        },
      }}
    >
      <UploadAvatar />
    </Card>
  );

  const UploadBoxTab = (
    <Row gutter={[16, 16]}>
      <Col span={24} md={4}>
        <UploadBox />
      </Col>
      <Col span={24} md={20}>
        <UploadBox placeholder={boxPlaceHolder} />
      </Col>
    </Row>
  );

  const TABS: TabsProps["items"] = [
    {
      key: "upload--file",
      label: t("sys.components.upload.singleFile"),
      children: UploadFileTab,
    },
    {
      key: "upload-avatar",
      label: t("sys.components.upload.avatar"),
      children: UploadAvatarTab,
    },
    {
      key: "upload-box",
      label: t("sys.components.upload.box"),
      children: UploadBoxTab,
    },
  ];

  return <Tabs items={TABS} />;
}
