import { Button, Form, Input } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import type { LoginRequest } from "@/api/models/auth";
import { useSignIn } from "@/store/userStore";

import {
  LoginStateEnum,
  useLoginStateContext,
} from "./providers/LoginStateProvider";

interface LoginFormValues {
  account: string;
  password: string;
}

function LoginForm() {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const { loginState } = useLoginStateContext();
  const signIn = useSignIn();

  if (loginState !== LoginStateEnum.LOGIN) return null;

  const handleFinish = async ({ account, password }: LoginFormValues) => {
    setLoading(true);
    try {
      await signIn({ account, password });
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div className="mb-6 text-xl text-gray-700 text-center">
        {t("sys.login.signInFormTitle")}
      </div>
      <Form
        name="normal_login"
        size="large"
        initialValues={{
          remember: true,
          account: "<EMAIL>",
          password: "123456",
        }}
        onFinish={handleFinish}
        className="w-full"
      >
        <Form.Item
          name="account"
          rules={[{ required: true, message: t("sys.login.emailPlaceholder") }]}
        >
          <Input placeholder={t("sys.login.emailPlaceholder")} />
        </Form.Item>
        <Form.Item
          name="password"
          rules={[
            { required: true, message: t("sys.login.passwordPlaceholder") },
          ]}
        >
          <Input.Password
            type="password"
            placeholder={t("sys.login.passwordPlaceholder")}
          />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            className="w-full"
            loading={loading}
            size="large"
          >
            {t("sys.login.loginButton")}
          </Button>
        </Form.Item>
      </Form>
    </>
  );
}

export default LoginForm;
