import { Layout, Typography } from "antd";
import { Navigate } from "react-router";

import Overlay from "@/assets/images/background/overlay.jpg";
import { useUserToken } from "@/store/userStore";

import LoginForm from "./LoginForm";
import { LoginStateProvider } from "./providers/LoginStateProvider";

const { VITE_APP_HOMEPAGE: HOMEPAGE } = import.meta.env;

function Login() {
  const token = useUserToken();

  // 判斷用戶是否有權限
  if (token.accessToken) {
    // 如果有授權，則跳轉到首頁
    return <Navigate to={HOMEPAGE} replace />;
  }

  const bg = `linear-gradient(135deg, #74ebd5 0%, #ACB6E5 100%) center center / cover no-repeat,url(${Overlay})`;

  return (
    <Layout
      className="flex !min-h-screen !w-full items-center justify-center"
      style={{ background: bg }}
    >
      <div className="flex flex-col items-center bg-white rounded-lg shadow-lg p-8 w-full max-w-[420px] mx-4">
        <div className="mb-6 text-3xl font-bold text-center">
          Cosmos-Fleet Admin
        </div>
        <div className="w-full">
          <LoginStateProvider>
            <LoginForm />
          </LoginStateProvider>
        </div>
        <Typography.Text className="mt-6 text-gray-500 text-center">
          Cosmos Fleet 後台管理系統
        </Typography.Text>
      </div>
    </Layout>
  );
}
export default Login;
