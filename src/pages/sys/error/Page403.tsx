import { NavLink } from "react-router";
import { m } from "framer-motion";
import { Typography } from "antd";
import { Helmet } from "react-helmet-async";

import MotionContainer from "@/components/animate/motion-container";
import { varBounce } from "@/components/animate/variants/bounce";
import { useTranslation } from "react-i18next";

const HOMEPAGE = "/dashboard";

// 安全的 SVG 渲染組件
function SafeSvgRender() {
  return (
    <svg viewBox="0 0 480 360" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient
          id="BG"
          x1="19.496%"
          x2="77.479%"
          y1="71.822%"
          y2="16.69%"
        >
          <stop offset="0%" stopColor="#078DEE" stopOpacity={0} />
          <stop offset="100%" stopColor="#078DEE" stopOpacity={0.16} />
        </linearGradient>
      </defs>

      <path
        fill="url(#BG)"
        fillRule="nonzero"
        d="M0 198.78c0 41.458 14.945 79.236 39.539 107.786 28.214 32.765 69.128 53.365 114.734 53.434a148.44 148.44 0 0056.495-11.036c9.051-3.699 19.182-3.274 27.948 1.107a75.779 75.779 0 0033.957 8.01c5.023 0 9.942-.494 14.7-1.433 13.58-2.67 25.94-8.99 36.09-17.94 6.378-5.627 14.547-8.456 22.897-8.446h.142c27.589 0 53.215-8.732 74.492-23.696 19.021-13.36 34.554-31.696 44.904-53.224C474.92 234.58 480 213.388 480 190.958c0-76.93-59.774-139.305-133.498-139.305-7.516 0-14.88.663-22.063 1.899C305.418 21.42 271.355 0 232.499 0a103.651 103.651 0 00-45.88 10.661c-13.24 6.487-25.011 15.705-34.64 26.939-32.698.544-62.931 11.69-87.676 30.291C25.351 97.155 0 144.882 0 198.781z"
        opacity={0.2}
      />

      <path
        fill="#078DEE"
        fillRule="nonzero"
        d="M297.295 166.657C270.625 159.696 214.905 153.622 168 140c-43.959 12.723-90.244 35.67-118.837 58.464-6.419 72.925 99.254 86.07 106.56 138.97 23.288-53.99 212.379-30.339 225.982-135.05-9.115-69.59-58.809-29.453-84.41-35.727z"
        opacity={0.08}
      />

      <g
        fill="#078DEE"
        fillRule="nonzero"
        opacity={0.12}
        transform="rotate(-11 257 -103.022)"
      >
        <path d="M32 1.646L15.075 25.06 0 1.646z" />
        <path d="M16 26.646l31.675-.001-32-45z" />
      </g>

      <g
        fill="#078DEE"
        fillRule="nonzero"
        opacity={0.2}
        transform="rotate(-11 372 9.022)"
      >
        <path d="M32 1.646L15.075 25.06 0 1.646z" />
        <path d="M16 26.646l31.675-.001-32-45z" />
      </g>

      <g
        fill="#078DEE"
        fillRule="nonzero"
        opacity={0.16}
        transform="rotate(19 22 246.505)"
      >
        <path d="M32 1.646L15.075 25.06 0 1.646z" />
        <path d="M16 26.646l31.675-.001-32-45z" />
      </g>

      <g
        fill="#078DEE"
        fillRule="nonzero"
        opacity={0.24}
        transform="rotate(19 141 321.505)"
      >
        <path d="M32 1.646L15.075 25.06 0 1.646z" />
        <path d="M16 26.646l31.675-.001-32-45z" />
      </g>

      <g
        fill="#078DEE"
        fillRule="nonzero"
        opacity={0.18}
        transform="rotate(-34 218.501 -2.642)"
      >
        <path d="M32 1.646L15.075 25.06 0 1.646z" />
        <path d="M16 26.646l31.675-.001-32-45z" />
      </g>

      <g
        fill="#078DEE"
        fillRule="nonzero"
        opacity={0.14}
        transform="rotate(-18 400 95.004)"
      >
        <path d="M32 1.646L15.075 25.06 0 1.646z" />
        <path d="M16 26.646l31.675-.001-32-45z" />
      </g>

      <g
        fill="#078DEE"
        fillRule="nonzero"
        opacity={0.28}
        transform="rotate(-34 343.501 72.488)"
      >
        <path d="M32 1.646L15.075 25.06 0 1.646z" />
        <path d="M16 26.646l31.675-.001-32-45z" />
      </g>
    </svg>
  );
}

export default function Page403() {
  const { t } = useTranslation();
  return (
    <>
      <Helmet>
        <title> 403 {t("sys.error.403.title")}</title>
      </Helmet>

      <div className="m-auto max-w-[400px]">
        <MotionContainer className="flex flex-col items-center justify-center px-2">
          <m.div variants={varBounce().in}>
            <Typography.Title level={3} className="text-center">
              {t("sys.error.403.title")}
            </Typography.Title>
          </m.div>

          <m.div variants={varBounce().in}>
            <Typography.Paragraph type="secondary" className="text-center">
              {t("sys.error.403.description")}
            </Typography.Paragraph>
          </m.div>

          <m.div variants={varBounce().in}>
            <SafeSvgRender />
          </m.div>

          <NavLink
            to={HOMEPAGE}
            className="rounded-md p-4 !text-text-primary !bg-primary"
          >
            {t("sys.error.403.goHome")}
          </NavLink>
        </MotionContainer>
      </div>
    </>
  );
}
