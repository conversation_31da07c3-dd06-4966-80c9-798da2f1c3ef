import { useState } from "react";
import { Card, Select, Input, Button, Table, DatePicker, Collapse, Tag } from "antd";
import type { TableProps } from "antd";

interface OrderData {
  orderId: string;
  type: string;
  pickupLocation: string;
  dropoffLocation: string;
  status: string;
  amount: number;
  driverCode: string;
}

export default function OrderList() {

  const columns: TableProps<OrderData>["columns"] = [
    {
      title: "訂單編號",
      dataIndex: "orderId",
      key: "orderId",
    },
    {
      title: "類型",
      dataIndex: "type",
      key: "type",
    },
    {
      title: "上車地點",
      dataIndex: "pickupLocation",
      key: "pickupLocation",
    },
    {
      title: "狀態",
      dataIndex: "status",
      key: "status",
      render: (status: string) => {
        const config = {
          'waiting': { color: '#F2B75B', text: '等待接單' },
          'canceled': { color: '#999999', text: '乘客取消' },
          'transfer': { color: '#FF7B7B', text: '流單' },
          'ongoing': { color: '#4D94FF', text: '執行任務' },
          'to_pickup': { color: '#4D94FF', text: '前往上車點' },
          'completed': { color: '#67C23A', text: '完成' },
        }[status] || { color: '#999999', text: status };
        
        return <Tag color={config.color} style={{ borderRadius: '12px' }}>{config.text}</Tag>;
      },
    },
    {
      title: "司機",
      dataIndex: "driverCode",
      key: "driverCode",
    },
  ];

  return (
    <div className="p-4">
      <Card title="即時單列表">
        <div className="flex justify-end">
          <Button type="primary">新增訂單</Button>
        </div>

        <div className="mt-8">
          <Collapse 
            className="mb-4"
            items={[
              {
                key: '1',
                label: '訂單查詢',
                children: (
                  <>
                    <div className="grid grid-cols-4 gap-4">
                      <div>
                        <div className="mb-2">日期區間</div>
                        <DatePicker.RangePicker style={{ width: "100%" }} />
                      </div>
                      <div>
                        <div className="mb-2">車隊</div>
                        <Select style={{ width: "100%" }} placeholder="RKS" />
                      </div>
                      <div>
                        <div className="mb-2">客群</div>
                        <Select style={{ width: "100%" }} placeholder="所有客群" />
                      </div>
                      <div>
                        <div className="mb-2">狀態</div>
                        <Select 
                          style={{ width: "100%" }} 
                          placeholder="所有狀態"
                          options={[
                            { value: 'waiting', label: <span><Tag color="#F2B75B" style={{ borderRadius: '12px' }}>等待接單</Tag></span> },
                            { value: 'canceled', label: <span><Tag color="#999999" style={{ borderRadius: '12px' }}>乘客取消</Tag></span> },
                            { value: 'transfer', label: <span><Tag color="#FF7B7B" style={{ borderRadius: '12px' }}>流單</Tag></span> },
                            { value: 'ongoing', label: <span><Tag color="#4D94FF" style={{ borderRadius: '12px' }}>執行任務</Tag></span> },
                            { value: 'to_pickup', label: <span><Tag color="#4D94FF" style={{ borderRadius: '12px' }}>前往上車點</Tag></span> },
                            { value: 'completed', label: <span><Tag color="#67C23A" style={{ borderRadius: '12px' }}>完成</Tag></span> },
                          ]}
                        />
                      </div>
                    </div>
                    <div className="mt-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <div className="mb-2">上車地點/下車地點</div>
                          <Input placeholder="請輸入" />
                        </div>
                        <div>
                          <div className="mb-2">編號</div>
                          <Input placeholder="請輸入" />
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-end mt-4">
                      <Button type="primary">重置</Button>
                      <Button type="primary" className="ml-2">
                        查詢
                      </Button>
                    </div>
                  </>
                ),
              }
            ]}
            defaultActiveKey={['1']}
          />

          <Card title="訂單資料">
            <div className="flex gap-2 mb-4">
              <Input
                placeholder="可輸入 e.g. RA/台中高鐵"
                style={{ maxWidth: "300px" }}
              />
              <Button type="primary" size="middle">
                快速新增
              </Button>
            </div>
            <Table
              columns={columns}
              // dataSource={data}
              pagination={{
                total: 996,
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
              }}
            />
          </Card>
        </div>
      </Card>
    </div>
  );
}
