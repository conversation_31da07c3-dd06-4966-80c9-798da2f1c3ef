import { Card, Table, Tag, message, Typography } from "antd";
import type { TableProps } from "antd";
import { CreateOrderForm, OrderSearchForm } from "@/components/order";

interface OrderData {
  orderId: string;
  type: string;
  pickupLocation: string;
  dropoffLocation: string;
  status: string;
  amount: number;
  driverCode: string;
}

const { Title } = Typography;

export default function OrderList() {
  const handleCreateOrder = (values: any) => {
    // 这里可以调用API创建订单
    console.log("创建订单:", values);
    message.success("订单创建成功");
  };

  const handleSearch = (values: any) => {
    // 这里可以调用API查询订单
    console.log("查询订单:", values);
    message.success("查询完成");
  };

  const handleReset = () => {
    // 重置查询条件
    console.log("重置查询条件");
    message.info("已重置查询条件");
  };

  const columns: TableProps<OrderData>["columns"] = [
    {
      title: "訂單編號",
      dataIndex: "orderId",
      key: "orderId",
    },
    {
      title: "車隊",
      dataIndex: "fleet",
      key: "fleet",
    },
    {
      title: "上車地點",
      dataIndex: "pickupLocation",
      key: "pickupLocation",
    },
    {
      title: "狀態",
      dataIndex: "status",
      key: "status",
      render: (status: string) => {
        const config = {
          waiting: { color: "#F2B75B", text: "等待接單" },
          canceled: { color: "#999999", text: "乘客取消" },
          transfer: { color: "#FF7B7B", text: "流單" },
          ongoing: { color: "#4D94FF", text: "執行任務" },
          to_pickup: { color: "#4D94FF", text: "前往上車點" },
          completed: { color: "#67C23A", text: "完成" },
        }[status] || { color: "#999999", text: status };

        return (
          <Tag color={config.color} style={{ borderRadius: "12px" }}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: "司機",
      dataIndex: "driverCode",
      key: "driverCode",
    },
  ];

  return (
    <div className="p-4">
      <div className="mb-4">
        <Title
          level={2}
          style={{ margin: 0, fontSize: "24px", fontWeight: 600 }}
        >
          即時單列表
        </Title>
      </div>
      {/* 新增订单 */}
      <CreateOrderForm onSubmit={handleCreateOrder} />
      {/* 订单查询 */}
      <div className="mt-6">
        <OrderSearchForm onSearch={handleSearch} onReset={handleReset} />
      </div>
      {/* 订单资料 */}
      <div className="mt-6">
        <Card title="訂單資料">
          <Table
            columns={columns}
            // dataSource={data}
            pagination={{
              total: 996,
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
            }}
          />
        </Card>
      </div>
    </div>
  );
}
