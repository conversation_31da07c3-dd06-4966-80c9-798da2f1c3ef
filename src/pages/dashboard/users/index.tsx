import type { RoleResponse } from "@/api/models/roles";
import type {
  CreateUserRequest,
  UpdateUserRequest,
  UserResponse,
} from "@/api/models/users";
import { enumService } from "@/api/services";
import { roleService } from "@/api/services/roleService";
import { userService } from "@/api/services/userService";
import Icon from "@/components/icon";
import {
  Button,
  Card,
  Empty,
  Form,
  Input,
  Modal,
  Select,
  Space,
  Table,
  message,
} from "antd";
import Title from "antd/es/typography/Title";
import { useEffect, useState } from "react";
import { Helmet } from "react-helmet-async";
import { useTranslation } from "react-i18next";

const UserManagementPage = () => {
  const { t } = useTranslation();
  const [users, setUsers] = useState<UserResponse[]>([]);
  const [roles, setRoles] = useState<RoleResponse[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentUser, setCurrentUser] = useState<UserResponse | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [departmentOptions, setDepartmentOptions] = useState<
    Array<{
      value: string;
      label: string;
    }>
  >([]);

  // 獲取所別選項
  const fetchDepartmentOptions = async () => {
    try {
      const response = await enumService.getDepartments();
      const options = response.values.map((value) => ({
        value,
        label: value,
      }));
      setDepartmentOptions(options);
    } catch (error) {
      console.error("獲取所別選項失敗:", error);
    }
  };

  const fetchUsers = async (params?: any) => {
    try {
      setLoading(true);
      const response = await userService.getUsers({
        page: currentPage,
        limit: pageSize,
        ...params,
      });
      setUsers(response.items || []);
      setTotal(response.total_count || 0);
    } catch (error) {
      console.error("獲取用戶列表失敗:", error);
      message.error(t("common.result.operationFailed"));
    } finally {
      setLoading(false);
    }
  };

  const fetchRoles = async () => {
    try {
      const response = await roleService.getAllRoles();
      setRoles(response.items || []);
    } catch (error) {
      console.error("獲取角色列表失敗:", error);
      message.error(t("common.result.operationFailed"));
    }
  };

  // 只在組件首次載入時獲取部門選項
  useEffect(() => {
    fetchDepartmentOptions();
  }, []);

  useEffect(() => {
    fetchUsers();
    fetchRoles();
  }, [currentPage, pageSize]);

  const handleSearch = async () => {
    const values = await searchForm.validateFields();
    // 將搜索表單的字段名映射回後端需要的字段名
    const params = {
      account: values.search_account,
      user_name: values.search_user_name,
      department: values.search_department,
    };
    setCurrentPage(1);
    fetchUsers(params);
  };

  const handleReset = () => {
    searchForm.resetFields();
    setCurrentPage(1);
    fetchUsers();
  };

  const showCreateModal = () => {
    setIsEditing(false);
    setCurrentUser(null);
    form.resetFields();
    form.setFieldsValue({
      department:
        departmentOptions.length > 0 ? departmentOptions[0].value : "",
    });
    setIsModalVisible(true);
  };

  const showEditModal = (user: UserResponse) => {
    setIsEditing(true);
    setCurrentUser(user);
    form.setFieldsValue({
      account: user.account,
      user_name: user.user_name,
      position: user.position,
      role_id: user.role_id,
      department: user.department,
      employee_no: user.employee_no,
    });
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleDelete = async (userId: string) => {
    Modal.confirm({
      title: t("common.confirm.confirmDelete"),
      content: t("user.confirmDeleteDesc"),
      onOk: async () => {
        try {
          await userService.deleteUser(userId);
          message.success(t("common.result.deleteSuccess"));
          fetchUsers();
        } catch (error) {
          console.error("刪除用戶失敗:", error);
          message.error(t("common.result.deleteFailed"));
        }
      },
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (isEditing && currentUser) {
        // 更新用戶
        const updateData: UpdateUserRequest = {
          user_name: values.user_name,
          position: values.position,
          role_id: values.role_id,
          department: values.department,
          employee_no: values.employee_no,
        };

        await userService.updateUser(currentUser.id, updateData);
        message.success(t("common.result.updateSuccess"));
      } else {
        // 創建用戶
        const createData: CreateUserRequest = {
          account: values.account,
          user_name: values.user_name,
          eip_password: values.eip_password,
          position: values.position,
          role_id: values.role_id,
          department: values.department,
          employee_no: values.employee_no,
        };

        await userService.createUser(createData);
        message.success(t("common.result.createSuccess"));
      }
      setIsModalVisible(false);
      form.resetFields();
      fetchUsers();
    } catch (error) {
      console.error("提交表單失敗:", error);
      message.error(
        isEditing
          ? t("common.result.updateFailed")
          : t("common.result.createFailed")
      );
    }
  };

  const columns = [
    {
      title: t("user.account"),
      dataIndex: "account",
      key: "account",
    },
    {
      title: t("user.name"),
      dataIndex: "user_name",
      key: "user_name",
    },
    {
      title: t("user.position"),
      dataIndex: "position",
      key: "position",
    },
    {
      title: t("user.department"),
      dataIndex: "department",
      key: "department",
    },
    {
      title: t("user.employee_no"),
      dataIndex: "employee_no",
      key: "employee_no",
    },
    {
      title: t("user.role"),
      dataIndex: "role_name",
      key: "role_name",
    },
    {
      title: t("common.basic.actions"),
      key: "action",
      render: (_: any, record: UserResponse) => (
        <Space>
          <Button
            type="text"
            icon={<Icon library="lucide" name="Edit" size={16} />}
            onClick={() => showEditModal(record)}
            title={t("common.basic.edit")}
          />
          <Button
            type="text"
            danger
            icon={<Icon library="lucide" name="Trash2" size={16} />}
            onClick={() => handleDelete(record.id)}
            title={t("common.basic.delete")}
          />
        </Space>
      ),
    },
  ];

  return (
    <>
      <Helmet>
        <title>{t("user.title")}</title>
      </Helmet>

      <Card
        title={<Title level={4}>{t("user.title")}</Title>}
        extra={
          <Button
            type="primary"
            icon={<Icon library="lucide" name="Plus" size={16} />}
            onClick={showCreateModal}
          >
            {t("common.basic.add")}
          </Button>
        }
      >
        <Form
          form={searchForm}
          layout="horizontal"
          style={{ marginBottom: 24 }}
          name="searchForm"
        >
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fill, minmax(240px, 1fr))",
              gap: "16px",
              marginBottom: "16px",
            }}
          >
            <Form.Item name="search_account" label={t("user.account")}>
              <Input placeholder={t("common.form.inputAccount")} allowClear />
            </Form.Item>
            <Form.Item name="search_user_name" label={t("user.name")}>
              <Input placeholder={t("common.form.inputName")} allowClear />
            </Form.Item>
            <Form.Item name="search_department" label={t("user.department")}>
              <Select
                placeholder={t("documents.outgoing.selectDepartment")}
                options={departmentOptions}
                allowClear
                style={{ width: "100%" }}
              />
            </Form.Item>
          </div>
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Form.Item style={{ marginRight: 8, marginBottom: 0 }}>
              <Button
                type="primary"
                htmlType="button"
                icon={<Icon library="lucide" name="Search" size={16} />}
                onClick={handleSearch}
              >
                {t("common.basic.search")}
              </Button>
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <Button htmlType="button" onClick={handleReset}>
                {t("common.basic.reset")}
              </Button>
            </Form.Item>
          </div>
        </Form>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          scroll={{ x: "max-content" }}
          loading={loading}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("documents.common.emptyData")}
              />
            ),
          }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
            showTotal: (total) =>
              `${t("common.table.total")} ${total} ${t("common.table.unit")}`,
          }}
        />
      </Card>

      <Modal
        title={isEditing ? t("user.edit") : t("user.add")}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        destroyOnClose
        forceRender
      >
        <Form form={form} layout="vertical" name="userForm" preserve={false}>
          <Form.Item
            name="account"
            label={t("user.account")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <Input
              placeholder={t("common.form.inputAccount")}
              disabled={isEditing}
            />
          </Form.Item>
          <Form.Item
            name="user_name"
            label={t("user.name")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <Input placeholder={t("common.form.inputName")} />
          </Form.Item>
          {!isEditing && (
            <Form.Item
              name="eip_password"
              label={t("user.eip_password")}
              rules={[{ required: true, message: t("common.form.required") }]}
            >
              <Input.Password placeholder={t("user.input_eip_password")} />
            </Form.Item>
          )}
          <Form.Item
            name="position"
            label={t("user.position")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <Input placeholder={t("user.input_position")} />
          </Form.Item>
          <Form.Item
            name="role_id"
            label={t("user.role")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <Select placeholder={t("common.form.select")}>
              {roles.map((role) => (
                <Select.Option key={role.id} value={role.id}>
                  {role.role_name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="department"
            label={t("user.department")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <Select
              placeholder={t("documents.outgoing.selectDepartment")}
              options={departmentOptions}
              allowClear
              style={{ width: "100%" }}
            />
          </Form.Item>
          <Form.Item
            name="employee_no"
            label={t("user.employee_no")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <Input placeholder={t("user.input_employee_no")} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default UserManagementPage;
