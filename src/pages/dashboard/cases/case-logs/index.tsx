import type {
  AddCaseLogRequest,
  CaseLog,
  CaseResponse,
} from "@/api/models/case";
import { CollectionEnum } from "@/api/models/files";
import { caseService } from "@/api/services/caseService";
import { enumService } from "@/api/services/enumService";
import { fileService } from "@/api/services/fileService";
import CustomerSelect from "@/components/custom-select";
import UserSelect from "@/components/custom-select/userSelect";
import Icon from "@/components/icon";
import {
  Button,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Empty,
  Form,
  Input,
  Modal,
  Popconfirm,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Typography,
  Upload,
  message,
} from "antd";
import type { ColumnType } from "antd/es/table";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { Helmet } from "react-helmet-async";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router";

const { Title, Text } = Typography;
const { TextArea } = Input;

// 日誌操作類型對應的顏色
const actionColors = {
  data_collection: "blue",
  customer_feedback: "cyan",
  document_processing: "purple",
  document_sent: "geekblue",
  completed: "green",
  invoice_payment: "gold",
  paused: "orange",
  other: "default",
  // 保留舊的類型以保持兼容性
  create: "green",
  update: "blue",
  status_change: "orange",
  assign: "purple",
  delete: "red",
};

const CaseLogsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const [searchForm] = Form.useForm();
  const [logForm] = Form.useForm();

  // 案件列表相關狀態
  const [cases, setCases] = useState<CaseResponse[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 日誌相關狀態
  const [caseLogs, setCaseLogs] = useState<CaseLog[]>([]);
  const [caseLogListVisible, setCaseLogListVisible] = useState(false);
  const [selectedCase, setSelectedCase] = useState<CaseResponse | null>(null);
  const [addLogModalVisible, setAddLogModalVisible] = useState(false);
  const [editingLog, setEditingLog] = useState<CaseLog | null>(null);

  // 所別選項
  const [departmentOptions, setDepartmentOptions] = useState<
    Array<{ value: string; label: string }>
  >([]);

  // 服務項目選項
  const [serviceItemOptions, setServiceItemOptions] = useState<
    Array<{ value: string; label: string }>
  >([]);

  // 從URL參數中獲取案件ID
  const getQueryParam = (name: string) => {
    const params = new URLSearchParams(location.search);
    return params.get(name);
  };

  const caseNumber = getQueryParam("caseNumber");

  // 獲取所別選項
  const fetchDepartmentOptions = async () => {
    try {
      const response = await enumService.getDepartments();
      const options = response.values.map((value) => ({
        value,
        label: value,
      }));
      setDepartmentOptions(options);
    } catch (error) {
      console.error("獲取所別選項失敗:", error);
    }
  };

  // 獲取服務項目選項
  const fetchServiceItemOptions = async () => {
    try {
      const response = await enumService.getServiceItems();
      const options = response.values.map((value) => ({
        value,
        label: value,
      }));
      setServiceItemOptions(options);
    } catch (error) {
      console.error("獲取服務項目選項失敗:", error);
      message.error("獲取服務項目選項失敗");
    }
  };

  // 初始化查詢表單
  useEffect(() => {
    if (caseNumber) {
      searchForm.setFieldsValue({
        case_no: caseNumber,
      });
    }
  }, [caseNumber, searchForm]);

  // 加載案件列表數據
  const fetchCases = async (params?: any) => {
    try {
      setLoading(true);

      const queryParams = {
        page: currentPage,
        limit: pageSize,
        ...params,
      };

      // 使用案件列表API
      const response = await caseService.getAllCases(queryParams);
      setCases(response.items || []);
      setTotal(response.total_count || 0);
    } catch (error) {
      console.error("獲取案件列表失敗:", error);
      message.error(t("case.fetchFailed"));
      setCases([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 只在組件首次載入時獲取部門選項
  useEffect(() => {
    fetchDepartmentOptions();
    fetchServiceItemOptions();
  }, []);

  // 頁碼、頁面大小或案例號碼改變時重新獲取案件列表
  useEffect(() => {
    const initialParams = caseNumber ? { case_no: caseNumber } : {};
    fetchCases(initialParams);
  }, [currentPage, pageSize, caseNumber]);

  // 處理表單搜索
  const handleSearch = async () => {
    try {
      const values = await searchForm.validateFields();

      // 處理日期範圍
      const { date_range, statusTags, ...otherValues } = values;

      statusTags?.forEach((tag: string) => {
        if (tag === t("case.closed")) {
          otherValues.is_closed = true;
        } else if (tag === t("case.isApproved")) {
          otherValues.is_approved = true;
        }
      });

      // 準備搜索參數
      const searchParams = {
        ...otherValues,
        page: 1,
      };

      setCurrentPage(1);
      fetchCases(searchParams);
    } catch (error) {
      console.error("搜索表單驗證失敗:", error);
    }
  };

  // 重置表單
  const handleReset = () => {
    searchForm.resetFields();
    setCurrentPage(1);
    fetchCases();
  };

  // 刷新日誌和案件數據
  const refreshCaseLogs = async () => {
    if (!selectedCase) return;

    try {
      setLoading(true);

      // 1. 更新當前選中的案件數據
      const updatedCase = await caseService.getCase(selectedCase.id);
      setSelectedCase(updatedCase);

      // 2. 如果案件自帶日誌，直接使用
      if (updatedCase.case_logs && updatedCase.case_logs.length > 0) {
        setCaseLogs(updatedCase.case_logs);
      } else {
        // 否則重新獲取日誌
        const logsResponse = await caseService.getCaseLogs(selectedCase.id, {
          page: 1,
          limit: 50,
        });
        setCaseLogs(logsResponse.items || []);
      }

      // 3. 刷新整個案件列表
      fetchCases({
        page: currentPage,
        limit: pageSize,
        case_no: searchForm.getFieldValue("case_no"),
        customer_name: searchForm.getFieldValue("customer_name"),
      });
    } catch (error) {
      console.error("刷新案件數據失敗:", error);
      message.error(t("case.refreshFailed"));
    } finally {
      setLoading(false);
    }
  };

  // 打開案件日誌列表彈窗
  const handleViewCaseLogs = (record: CaseResponse) => {
    setSelectedCase(record);

    // 直接使用案件中的case_logs屬性
    if (record.case_logs && record.case_logs.length > 0) {
      setCaseLogs(record.case_logs);
    } else {
      // 如果案件中沒有日誌或日誌為空，顯示空列表
      setCaseLogs([]);
    }

    setCaseLogListVisible(true);
  };
  // 添加或更新日誌
  const handleAddOrUpdateLog = async () => {
    try {
      if (!selectedCase) {
        message.error(t("case.noCaseSelected"));
        return;
      }

      const values = await logForm.validateFields();
      const logData: AddCaseLogRequest & { file_download_url?: string } = {
        status: values.status,
        process_date: values.process_date
          ? values.process_date.format("YYYY-MM-DD")
          : dayjs().format("YYYY-MM-DD"),
        description: values.description,
        handler_name: values.handler_name || "",
      };

      if (editingLog) {
        // 編輯模式：如果有檔案先上傳檔案，再更新日誌
        if (values.upload_file && values.upload_file.fileList?.length > 0) {
          try {
            // 先上傳檔案
            const file = values.upload_file.fileList[0].originFileObj;
            // 使用fileService上傳檔案
            const uploadResponse = await fileService.uploadFile(
              file,
              editingLog.id,
              CollectionEnum.CASE_LOGS
            );

            // 設置檔案相對路徑
            if (uploadResponse?.data?.relativePath) {
              logData.file_relative_path = uploadResponse.data.relativePath;
            }
          } catch (error) {
            console.error("上傳檔案失敗:", error);
            message.error(t("case.uploadFileFailed"));
            // 繼續執行不中斷流程，只是沒有檔案
          }
        }

        // 更新日誌
        await caseService.updateCaseLog(
          selectedCase.id,
          editingLog.id,
          logData
        );
        message.success(t("case.log.updateSuccess"));
      } else {
        // 新增模式：先新增日誌取得ID，如果有檔案則上傳後再更新
        // 1. 先新增日誌（不包含檔案）
        const addResponse = await caseService.addCaseLog(
          selectedCase.id,
          logData
        );
        // 2. 從回應中找到新建立的日誌ID
        let newLogId: string | null = null;
        if (addResponse?.case_logs && addResponse.case_logs.length > 0) {
          // 找到最新的日誌（通常是最後一個）
          const latestLog =
            addResponse.case_logs[addResponse.case_logs.length - 1];
          newLogId = latestLog.id;
        }

        // 3. 如果有檔案且成功取得日誌ID，則上傳檔案並更新日誌
        if (
          newLogId &&
          values.upload_file &&
          values.upload_file.fileList?.length > 0
        ) {
          try {
            const file = values.upload_file.fileList[0].originFileObj;
            // 使用新日誌的ID上傳檔案
            const uploadResponse = await fileService.uploadFile(
              file,
              newLogId,
              CollectionEnum.CASE_LOGS
            );

            // 如果檔案上傳成功，更新日誌記錄
            if (uploadResponse?.data?.relativePath) {
              const updateData = {
                ...logData,
                file_relative_path: uploadResponse.data.relativePath,
              };

              await caseService.updateCaseLog(
                selectedCase.id,
                newLogId,
                updateData
              );
            }
          } catch (error) {
            console.error("上傳檔案失敗:", error);
            message.error(t("case.uploadFileFailed"));
            // 即使檔案上傳失敗，日誌已經新增成功
          }
        }

        message.success(t("case.log.addSuccess"));
      }

      setAddLogModalVisible(false);
      setEditingLog(null);
      logForm.resetFields();

      // 刷新日誌和案件數據
      refreshCaseLogs();
    } catch (error) {
      console.error("操作日誌失敗:", error);
      message.error(
        editingLog ? t("case.log.updateFailed") : t("case.log.addFailed")
      );
    }
  };

  // 刪除日誌
  const handleDeleteLog = async (logId: string) => {
    try {
      if (!selectedCase) {
        message.error(t("case.noCaseSelected"));
        return;
      }

      await caseService.deleteCaseLog(selectedCase.id, logId);
      message.success(t("case.log.deleteSuccess"));

      // 刷新日誌和案件數據
      refreshCaseLogs();
    } catch (error) {
      console.error("刪除日誌失敗:", error);
      message.error(t("case.log.deleteFailed"));
    }
  };

  // 編輯日誌
  const handleEditLog = (log: CaseLog) => {
    setEditingLog(log);
    // 設置表單初始值
    logForm.setFieldsValue({
      id: log.id,
      status: log.status,
      process_date: dayjs(log.process_date),
      description: log.description,
      handler_name: log.handler_name,
    });
    setAddLogModalVisible(true);
  };

  // 案件表格列定義
  const caseColumns: ColumnType<CaseResponse>[] = [
    {
      title: t("case.caseNumber"),
      dataIndex: "case_no",
      key: "case_no",
      width: 120,
    },
    {
      title: t("case.customer"),
      dataIndex: "customer_name",
      key: "customer_name",
      width: 150,
      ellipsis: true,
      render: (text: string) => text || "",
    },
    {
      title: t("case.serviceItem"),
      dataIndex: "service_item",
      key: "service_item",
      width: 120,
      render: (text: string) => text || "",
    },
    {
      title: t("case.agent"),
      dataIndex: "agent_name",
      key: "agent_name",
      width: 120,
      render: (text: string) => text || "",
    },
    {
      title: t("case.statusDescription"),
      key: "description",
      render: (_: any, record: CaseResponse) => {
        // 獲取最新的日誌作為情形說明
        if (record.case_logs && record.case_logs.length > 0) {
          const latestLog = record.case_logs[record.case_logs.length - 1];
          return latestLog.description || "";
        }
        return "";
      },
      ellipsis: true,
      width: 180,
    },
    {
      title: t("common.basic.actions"),
      key: "actions",
      width: 120,
      render: (_: any, record: CaseResponse) => (
        <Button
          type="primary"
          icon={<Icon library="lucide" name="List" size={16} />}
          onClick={(e) => {
            e.stopPropagation();
            handleViewCaseLogs(record);
          }}
          size="small"
        >
          {t("case.viewLogs")}
        </Button>
      ),
    },
  ];

  // 定義展開行的內容
  const expandedRowRender = (record: CaseResponse) => {
    return (
      <div className="px-4 py-2">
        <Row gutter={[16, 16]}>
          <Col span={8} className="h-8 flex items-center">
            <Text strong>{t("case.assignDate")}：</Text>
            <Text>
              {record.assign_date
                ? dayjs(record.assign_date).format("YYYY-MM-DD")
                : ""}
            </Text>
          </Col>
          <Col span={8} className="h-8 flex items-center">
            <Text strong>{t("case.estimated_completion_date")}：</Text>
            <Text>
              {record.estimated_completion_date
                ? dayjs(record.estimated_completion_date).format("YYYY-MM-DD")
                : ""}
            </Text>
          </Col>
          <Col span={8} className="h-8 flex items-center">
            <Text strong>{t("case.file")}：</Text>
            <Button
              type="link"
              onClick={() => {
                window.open(record.file_download_url || "", "_blank");
              }}
              style={{ padding: 0 }}
              disabled={!record.file_download_url}
            >
              {t("case.file")}
            </Button>
          </Col>
          <Col span={24} className="h-8 flex items-center">
            <Text strong>{t("case.serviceContent")}：</Text>
            <Text>{record.service_content || ""}</Text>
          </Col>
        </Row>
      </div>
    );
  };

  // 案件日誌列表彈窗的列定義
  const caseLogColumns: ColumnType<CaseLog>[] = [
    {
      title: t("case.log.action"),
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: string) => {
        // 定義狀態顯示文字
        const statusTextMap: Record<string, string> = {
          data_collection: t("case.log.status.data_collection"),
          customer_feedback: t("case.log.status.customer_feedback"),
          document_processing: t("case.log.status.document_processing"),
          document_sent: t("case.log.status.document_sent"),
          completed: t("case.log.status.completed"),
          invoice_payment: t("case.log.status.invoice_payment"),
          paused: t("case.log.status.paused"),
          other: t("case.log.status.other"),
          // 保留舊的類型以保持兼容性
          create: t("case.log.status.create"),
          update: t("case.log.status.update"),
          status_change: t("case.log.status.status_change"),
          assign: t("case.log.status.assign"),
          delete: t("case.log.status.delete"),
        };

        return (
          <Tag
            color={
              actionColors[status as keyof typeof actionColors] || "default"
            }
          >
            {statusTextMap[status] || status || ""}
          </Tag>
        );
      },
    },
    {
      title: t("case.statusDescription"),
      dataIndex: "description",
      key: "description",
      ellipsis: true,
      width: 180,
    },
    {
      title: t("case.log.processDate"),
      dataIndex: "process_date",
      key: "process_date",
      width: 120,
      render: (date: string) => (date ? dayjs(date).format("YYYY-MM-DD") : ""),
      responsive: ["md"],
    },
    {
      title: t("case.log.handler"),
      dataIndex: "handler_name",
      key: "handler_name",
      width: 120,
      render: (text: string) => text || "",
      responsive: ["md"],
    },
    {
      title: t("case.log.file"),
      dataIndex: "file_download_url",
      key: "file_download_url",
      width: 100,
      render: (url: string) => {
        return url ? (
          <Button
            type="link"
            onClick={() => window.open(url, "_blank")}
            style={{ padding: 0 }}
          >
            {t("case.log.downloadFile")}
          </Button>
        ) : (
          ""
        );
      },
      responsive: ["md"],
    },
    {
      title: t("common.basic.actions"),
      key: "actions",
      fixed: "right" as const,
      width: 120,
      render: (_: any, record: CaseLog) => (
        <Space size="small" wrap>
          <Button
            type="text"
            icon={<Icon library="lucide" name="Edit" size={16} />}
            onClick={() => handleEditLog(record)}
            title={t("common.basic.edit")}
          />
          <Popconfirm
            title={t("case.log.logConfirmDelete")}
            description={t("case.log.logConfirmDeleteDesc")}
            onConfirm={() => record.id && handleDeleteLog(record.id)}
            okText={t("common.basic.yes")}
            cancelText={t("common.basic.no")}
          >
            <Button
              danger
              type="text"
              icon={<Icon library="lucide" name="Trash2" size={16} />}
              title={t("common.basic.delete")}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Helmet>
        <title>{t("case.log.title")}</title>
      </Helmet>

      <Card title={<Title level={4}>{t("case.log.title")}</Title>}>
        {/* 搜索表單 */}
        <Form
          form={searchForm}
          layout="vertical"
          onFinish={handleSearch}
          style={{ marginBottom: 16 }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8} lg={8}>
              <Form.Item name="case_no_pattern" label={t("case.caseNumber")}>
                <Input placeholder={t("case.enterCaseNumber")} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={8}>
              <Form.Item name="service_item" label={t("case.serviceItem")}>
                <Select
                  placeholder={t("case.enterServiceItem")}
                  allowClear
                  options={serviceItemOptions}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={8}>
              <Form.Item name="dept" label={t("case.dept")}>
                <Select
                  placeholder={t("case.selectDept")}
                  allowClear
                  options={departmentOptions}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="statusTags"
                label={`${t("case.closed")}/${t("case.isApproved")}`}
              >
                <Select
                  mode="multiple"
                  allowClear
                  style={{ width: "100%" }}
                  placeholder={`${t("common.form.select")}`}
                  options={[
                    { value: t("case.closed"), label: t("case.closed") },
                    {
                      value: t("case.isApproved"),
                      label: t("case.isApproved"),
                    },
                  ]}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item name="date_range" label={t("case.dateRange")}>
                <DatePicker.RangePicker
                  style={{ width: "100%" }}
                  placeholder={[
                    t("common.date.startDate"),
                    t("common.date.endDate"),
                  ]}
                  onChange={(dates) => {
                    if (dates) {
                      searchForm.setFieldsValue({
                        date_from: dates[0]?.format("YYYY-MM-DD"),
                        date_to: dates[1]?.format("YYYY-MM-DD"),
                      });
                    } else {
                      searchForm.setFieldsValue({
                        date_from: undefined,
                        date_to: undefined,
                      });
                    }
                  }}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={12} lg={12}>
              <Form.Item name="agent_name" label={t("case.agent")}>
                <UserSelect
                  placeholder={t("case.enterAgentName")}
                  valueField="user_name"
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={12} lg={12}>
              <Form.Item name="handler_name" label={t("case.handlerName")}>
                <UserSelect
                  placeholder={t("case.enterHandlerName")}
                  value={editingLog?.handler_name}
                  valueField="user_name"
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={12} lg={12}>
              <Form.Item name="customer_name" hidden>
                <Input />
              </Form.Item>
              <Form.Item name="customer_number" label={t("case.customer")}>
                <CustomerSelect
                  fullData={true}
                  valueField="customer_number"
                  placeholder={t("case.enterCustomerName")}
                  onChange={(value) => {
                    if (value) {
                      searchForm.setFieldsValue({
                        customer_name: value.customer_name,
                        customer_number: value.customer_number,
                      });
                    } else {
                      searchForm.setFieldsValue({
                        customer_name: "",
                        customer_number: "",
                      });
                    }
                  }}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={24} lg={24}>
              <Form.Item
                label=" "
                colon={false}
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "flex-end",
                  marginBottom: 0,
                }}
              >
                <Space wrap>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<Icon library="lucide" name="Search" size={16} />}
                  >
                    {t("common.basic.search")}
                  </Button>
                  <Button
                    onClick={handleReset}
                    icon={<Icon library="lucide" name="RotateCcw" size={16} />}
                  >
                    {t("common.basic.reset")}
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>

        {/* 案件主表格 */}
        <Table
          columns={caseColumns}
          dataSource={cases}
          rowKey="id"
          scroll={{ x: "max-content" }}
          loading={loading}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("case.emptyData")}
              />
            ),
          }}
          expandable={{
            expandedRowRender,
            expandRowByClick: true,
          }}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
            showTotal: (total) =>
              `${t("common.table.total")} ${total} ${t("common.table.unit")}`,
          }}
        />
      </Card>

      {/* 案件日誌列表彈窗 */}
      <Modal
        title={`${t("case.log.title")} - ${selectedCase?.case_no || ""}`}
        open={caseLogListVisible}
        onCancel={() => setCaseLogListVisible(false)}
        width="90%"
        footer={[
          <Button
            key="add"
            type="primary"
            icon={<Icon library="lucide" name="PlusCircle" size={16} />}
            onClick={() => {
              setEditingLog(null);
              logForm.resetFields();
              // 設定預設值為當天
              logForm.setFieldsValue({
                process_date: dayjs(),
              });
              setAddLogModalVisible(true);
            }}
          >
            {t("case.log.addNew")}
          </Button>,
          <Button key="close" onClick={() => setCaseLogListVisible(false)}>
            {t("common.basic.close")}
          </Button>,
        ]}
      >
        <Table
          columns={caseLogColumns}
          dataSource={caseLogs}
          rowKey="id"
          scroll={{ x: "max-content" }}
          size="small"
          pagination={false}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("case.emptyData")}
              />
            ),
            filterConfirm: t("common.basic.ok"),
            filterReset: t("common.basic.reset"),
          }}
        />
      </Modal>

      {/* 添加/編輯日誌模態框 */}
      <Modal
        title={editingLog ? t("case.log.editLog") : t("case.log.addNew")}
        open={addLogModalVisible}
        onOk={handleAddOrUpdateLog}
        onCancel={() => {
          setAddLogModalVisible(false);
          setEditingLog(null);
        }}
        width="90%"
        okText={t("common.basic.save")}
        cancelText={t("common.basic.cancel")}
      >
        <Form form={logForm} layout="vertical">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} className="hidden">
              <Form.Item name="id">
                <Input hidden />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="status"
                label={t("case.log.handlerSituation")}
                rules={[
                  { required: true, message: t("case.log.actionRequired") },
                ]}
              >
                <Select placeholder={t("case.log.selectHandlerSituation")}>
                  <Select.Option value="data_collection">
                    {t("case.log.status.data_collection")}
                  </Select.Option>
                  <Select.Option value="customer_feedback">
                    {t("case.log.status.customer_feedback")}
                  </Select.Option>
                  <Select.Option value="document_processing">
                    {t("case.log.status.document_processing")}
                  </Select.Option>
                  <Select.Option value="document_sent">
                    {t("case.log.status.document_sent")}
                  </Select.Option>
                  <Select.Option value="completed">
                    {t("case.log.status.completed")}
                  </Select.Option>
                  <Select.Option value="invoice_payment">
                    {t("case.log.status.invoice_payment")}
                  </Select.Option>
                  <Select.Option value="paused">
                    {t("case.log.status.paused")}
                  </Select.Option>
                  <Select.Option value="other">
                    {t("case.log.status.other")}
                  </Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="process_date" label={t("case.log.processDate")}>
                <DatePicker
                  style={{ width: "100%" }}
                  placeholder={t("case.log.selectDate")}
                />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="description"
                label={t("case.log.situationDescription")}
                rules={[
                  {
                    required: true,
                    message: t("case.log.logDescriptionRequired"),
                  },
                ]}
              >
                <TextArea
                  rows={4}
                  placeholder={t("case.log.situationDescriptionPlaceholder")}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="handler_name"
                label={t("case.log.handler")}
                rules={[
                  { required: true, message: t("case.log.handlerRequired") },
                ]}
              >
                <UserSelect
                  placeholder={t("case.log.enterHandler")}
                  valueField="user_name"
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="upload_file"
                label={t("case.log.uploadFile")}
                valuePropName="file"
              >
                <Upload beforeUpload={() => false} maxCount={1}>
                  <Button
                    icon={<Icon library="lucide" name="Upload" size={16} />}
                  >
                    {t("case.log.chooseFile")}
                  </Button>
                </Upload>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default CaseLogsPage;
