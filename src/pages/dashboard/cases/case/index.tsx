import {
  <PERSON><PERSON>,
  Card,
  Col,
  DatePicker,
  Empty,
  Form,
  Input,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Typography,
  Upload,
  message,
} from "antd";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { Helmet } from "react-helmet-async";
import { useTranslation } from "react-i18next";

import { CaseStatus } from "@/api/models/case";
import type {
  CaseFilterRequest,
  CaseResponse,
  CreateCaseRequest,
  UpdateCaseRequest,
} from "@/api/models/case";
import { CollectionEnum } from "@/api/models/files";
import { caseCodeService } from "@/api/services/caseCodeService";
import { caseService } from "@/api/services/caseService";
import { enumService } from "@/api/services/enumService";
import { fileService } from "@/api/services/fileService";
import CustomerSelect from "@/components/custom-select";
import UserSelect from "@/components/custom-select/userSelect";
import Icon from "@/components/icon";

const { Title, Text } = Typography;
const { TextArea } = Input;

const CasePage = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [caseForm] = Form.useForm();

  const [loading, setLoading] = useState(true);
  const [cases, setCases] = useState<CaseResponse[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [filters, setFilters] = useState<CaseFilterRequest>({});
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState("");
  const [editingCase, setEditingCase] = useState<CaseResponse | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [caseTypeOptions, setCaseTypeOptions] = useState<
    Array<{ value: string; label: string }>
  >([]);
  // 所別選項
  const [departmentOptions, setDepartmentOptions] = useState<
    Array<{ value: string; label: string }>
  >([]);

  // 服務項目選項
  const [serviceItemOptions, setServiceItemOptions] = useState<
    Array<{ value: string; label: string }>
  >([]);

  // 獲取案件類型選項
  const fetchCaseTypeOptions = async () => {
    try {
      const response = await caseCodeService.getAllCaseCodes({ limit: 999 });
      if (response?.items) {
        const options = response.items.map((item) => ({
          value: item.type,
          label: item.name,
        }));
        setCaseTypeOptions(options);
      }
    } catch (error) {
      console.error("獲取案件類型代碼失敗:", error);
      message.error("獲取案件類型代碼失敗");
    }
  };

  // 獲取所別選項
  const fetchDepartmentOptions = async () => {
    try {
      const response = await enumService.getDepartments();
      const options = response.values.map((value) => ({
        value,
        label: value,
      }));
      setDepartmentOptions(options);
    } catch (error) {
      console.error("獲取所別選項失敗:", error);
    }
  };

  // 獲取服務項目選項
  const fetchServiceItemOptions = async () => {
    try {
      const response = await enumService.getServiceItems();
      const options = response.values.map((value) => ({
        value,
        label: value,
      }));
      setServiceItemOptions(options);
    } catch (error) {
      console.error("獲取服務項目選項失敗:", error);
      message.error("獲取服務項目選項失敗");
    }
  };

  // 獲取案件列表
  const fetchCases = async (
    page = currentPage,
    limit = pageSize,
    filterParams = filters
  ) => {
    try {
      setLoading(true);
      const response = await caseService.getAllCases({
        page,
        limit,
        ...filterParams,
      });
      setCases(response.items);
      setTotalCount(response.total_count);
    } catch (error) {
      message.error(t("case.fetchFailed"));
    } finally {
      setLoading(false);
    }
  };
  // 只在組件首次載入時獲取部門選項、案件類型選項和服務項目選項
  useEffect(() => {
    fetchCaseTypeOptions();
    fetchDepartmentOptions();
    fetchServiceItemOptions();
  }, []);

  // 頁碼或頁面大小改變時重新獲取案件列表
  useEffect(() => {
    fetchCases();
  }, [currentPage, pageSize]);

  // 處理篩選
  const handleFilter = async (values: any) => {
    const filterParams: CaseFilterRequest & { statusTags?: string[] } = {};

    if (values.dept) filterParams.dept = values.dept;
    if (values.case_no_pattern)
      filterParams.case_no_pattern = values.case_no_pattern;
    if (values.customer_name) filterParams.customer_name = values.customer_name;
    if (values.customer_number)
      filterParams.customer_number = values.customer_number;
    if (values.agent_name) filterParams.agent_name = values.agent_name;
    if (values.handler_name) filterParams.handler_name = values.handler_name;
    if (values.service_item) filterParams.service_item = values.service_item;
    if (values.date_range?.[0] && values.date_range[1]) {
      filterParams.date_from = values.date_range[0].format("YYYY-MM-DD");
      filterParams.date_to = values.date_range[1].format("YYYY-MM-DD");
    }

    if (values.statusTags) {
      filterParams.statusTags = values.statusTags.map((tag: string) => {
        if (tag === t("case.closed")) {
          filterParams.is_closed = true;
        }

        if (tag === t("case.isApproved")) {
          filterParams.is_approved = true;
        }
      });
    }

    setFilters(filterParams);
    setCurrentPage(1);
    await fetchCases(1, pageSize, filterParams);
  };

  // 處理重置篩選
  const handleResetFilter = () => {
    form.resetFields();
    setFilters({});
    setCurrentPage(1);
    fetchCases(1, pageSize, {});
  };

  // 新增或更新案件
  const handleSaveCase = async () => {
    try {
      setSubmitting(true);
      const values = await caseForm.validateFields();

      // 準備基本數據
      const caseData: CreateCaseRequest | UpdateCaseRequest = {
        dept: values.dept,
        assign_date: values.assign_date.format("YYYY-MM-DD"),
        case_code_type: values.case_code_type || "",
        customer_number: values.customer_number,
        customer_name: values.customer_name,
        agent_name: values.agent_name,
        handler1_name: values.handler1_name,
        handler2_name: values.handler2_name,
        service_item: values.service_item,
        service_content: values.service_content,
        estimated_completion_date:
          values.estimated_completion_date.format("YYYY-MM-DD"),
      };

      // 如果是編輯模式，添加額外字段
      if (editingCase) {
        (caseData as UpdateCaseRequest).manager_approved =
          values.manager_approved === "true";
        if (values.close_date) {
          (caseData as UpdateCaseRequest).close_date =
            values.close_date.format("YYYY-MM-DD");
        }
        (caseData as UpdateCaseRequest).close_approved =
          values.close_approved === "true";
      }

      // 如果有上傳檔案
      const uploadFiles = values.authorization_upload;
      if (uploadFiles && uploadFiles.length > 0) {
        try {
          // 獲取表單中的文件對象
          const file = uploadFiles[0].originFileObj;
          if (file) {
            if (editingCase) {
              // 編輯模式，上傳並更新檔案
              const uploadResponse = await fileService.uploadFile(
                file,
                editingCase.id,
                CollectionEnum.CASES
              );

              if (uploadResponse?.data?.relativePath) {
                caseData.file_relative_path = uploadResponse.data.relativePath;
              }
            } else {
              // 新增模式，先上傳檔案取得路徑
              const uploadResponse = await fileService.uploadFile(file);

              if (uploadResponse?.data?.relativePath) {
                caseData.file_relative_path = uploadResponse.data.relativePath;
              }
            }
          }
        } catch (error) {
          console.error("上傳檔案失敗:", error);
          message.error(t("case.uploadFileFailed"));
        }
      }

      if (editingCase) {
        // 更新案件
        await caseService.updateCase(
          editingCase.id,
          caseData as UpdateCaseRequest
        );
        message.success(t("case.updateSuccess"));
      } else {
        // 創建案件
        await caseService.createCase(caseData as CreateCaseRequest);
        message.success(t("case.createSuccess"));
      }

      setModalVisible(false);
      setEditingCase(null);
      caseForm.resetFields();
      fetchCases(1, pageSize, filters);
    } catch (error) {
      console.error("儲存案件失敗:", error);
      message.error(t("case.saveFailed"));
    } finally {
      setSubmitting(false);
    }
  };

  // 刪除案件
  const handleDeleteCase = async (id: string) => {
    try {
      await caseService.deleteCase(id);
      message.success(t("case.deleteSuccess"));
      fetchCases(currentPage, pageSize, filters);
    } catch (error) {
      console.error("刪除案件失敗:", error);
      message.error(t("case.deleteFailed"));
    }
  };

  // 核准案件
  const handleApproveCase = async (id: string) => {
    try {
      await caseService.approveCase(id);
      message.success(t("case.approveSuccess"));
      fetchCases(currentPage, pageSize, filters);
    } catch (error) {
      console.error("核准案件失敗:", error);
      message.error(t("case.approveFailed"));
    }
  };

  // 結案
  const handleCloseCase = async (id: string) => {
    try {
      await caseService.closeCase(id);
      message.success(t("case.closeSuccess"));
      fetchCases(currentPage, pageSize, filters);
    } catch (error) {
      console.error("結案失敗:", error);
      message.error(t("case.closeFailed"));
    }
  };

  // 顯示新增案件對話框
  const showCreateModal = () => {
    setEditingCase(null);
    caseForm.resetFields();

    // 設置初始值
    caseForm.setFieldsValue({
      department:
        departmentOptions.length > 0 ? departmentOptions[0].value : "",
      assign_date: dayjs(),
      estimated_completion_date: dayjs().add(30, "day"),
      manager_approved: "false",
      close_approved: "false",
    });

    setModalTitle(t("case.createNew"));
    setModalVisible(true);
  };

  // 顯示編輯案件對話框
  const showEditModal = async (record: CaseResponse) => {
    setEditingCase(record);
    caseForm.resetFields();

    console.log(record);

    caseForm.setFieldsValue({
      department: record.department,
      assign_date: dayjs(record.assign_date),
      case_code_type: record.case_code_type,
      case_no: record.case_no,
      customer_name: record.customer_name,
      customer_number: record.customer_number,
      agent_name: record.agent_name,
      handler1_name: record.handler1_name,
      handler2_name: record.handler2_name,
      service_item: record.service_item,
      service_content: record.service_content,
      estimated_completion_date: dayjs(record.estimated_completion_date),
      manager_approved: record.is_approved ? "true" : "false",
      close_date: record.close_date ? dayjs(record.close_date) : null,
      close_approved: record.is_closed ? "true" : "false",
      authorization_upload: [],
    });

    setModalTitle(t("case.edit"));
    setModalVisible(true);
  };

  // 表格列定義
  const columns = [
    {
      title: t("case.caseNumber"),
      dataIndex: "case_no",
      key: "case_no",
      width: 120,
    },
    {
      title: t("case.customer"),
      dataIndex: "customer_name",
      key: "customer_name",
      width: 200,
      ellipsis: true,
      render: (text: string) => text || "",
    },
    {
      title: t("case.status"),
      key: "status",
      width: 100,
      render: (_: any, record: CaseResponse) => {
        // 根據is_approved判斷狀態：false為未核准，true為已核准
        const status = record.is_approved
          ? CaseStatus.APPROVED
          : CaseStatus.PROCESSING;

        const color = status === "approved" ? "green" : "blue";

        return <Tag color={color}>{t(`case.statusOptions.${status}`)}</Tag>;
      },
    },
    {
      title: t("case.closingStatus"),
      key: "closing_status",
      width: 100,
      render: (_: any, record: CaseResponse) => {
        // 根據is_closed判斷狀態：false為未結案，true為已結案
        const status = record.is_closed ? "closed" : "open";

        const color = status === "closed" ? "green" : "blue";

        return (
          <Tag color={color}>{t(`case.closingStatusOptions.${status}`)}</Tag>
        );
      },
    },
    {
      title: t("case.dept"),
      dataIndex: "department",
      key: "department",
      width: 160,
      render: (text: string) => text || "",
    },
    {
      title: t("common.basic.actions"),
      key: "actions",
      width: 160,
      render: (_: any, record: CaseResponse) => (
        <Space>
          <Button
            icon={<Icon library="lucide" name="Edit" size={16} />}
            onClick={(e) => {
              e.stopPropagation();
              showEditModal(record);
            }}
            size="small"
            type="text"
            title={t("common.basic.edit")}
          />
          <Button
            icon={<Icon library="lucide" name="UserRoundCheck" size={16} />}
            onClick={(e) => {
              e.stopPropagation();
              handleApproveCase(record.id);
            }}
            size="small"
            type="text"
            style={{ color: "#1890ff" }}
            title={t("case.approve")}
          />
          <Button
            icon={<Icon library="lucide" name="FileCheck2" size={16} />}
            onClick={(e) => {
              e.stopPropagation();
              handleCloseCase(record.id);
            }}
            size="small"
            type="text"
            style={{ color: "#52c41a" }}
            title={t("case.close")}
          />
          <Popconfirm
            title={t("case.confirmDelete")}
            description={t("case.confirmDeleteDesc")}
            onConfirm={(e) => {
              e.stopPropagation();
              handleDeleteCase(record.id);
            }}
            okText={t("common.basic.yes")}
            cancelText={t("common.basic.no")}
          >
            <Button
              onClick={(e) => {
                e.stopPropagation();
              }}
              icon={<Icon library="lucide" name="Trash2" size={16} />}
              size="small"
              type="text"
              title={t("common.basic.delete")}
              danger
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 定義展開行的內容
  const expandedRowRender = (record: CaseResponse) => {
    return (
      <div className="px-4 py-2">
        <Row gutter={[16, 16]}>
          <Col span={8} className="h-8 flex items-center">
            <Text strong>{t("case.assignDate")}：</Text>
            <Text>
              {record.assign_date
                ? dayjs(record.assign_date).format("YYYY-MM-DD")
                : ""}
            </Text>
          </Col>
          <Col span={8} className="h-8 flex items-center">
            <Text strong>{t("case.serviceItem")}：</Text>
            <Text>{record.service_item || ""}</Text>
          </Col>
          <Col span={8} className="h-8 flex items-center">
            <Text strong>{t("case.estimated_completion_date")}：</Text>
            <Text>
              {record.estimated_completion_date
                ? dayjs(record.estimated_completion_date).format("YYYY-MM-DD")
                : ""}
            </Text>
          </Col>
          <Col span={24} className="h-8 flex items-center">
            <Text strong>{t("case.serviceContent")}：</Text>
            <Text>{record.service_content || ""}</Text>
          </Col>
        </Row>
      </div>
    );
  };

  return (
    <>
      <Helmet>
        <title>{t("case.title")}</title>
      </Helmet>

      <Card
        className="w-full"
        title={<Title level={4}>{t("case.title")}</Title>}
        extra={
          <Button
            type="primary"
            icon={<Icon library="lucide" name="PlusCircle" size={16} />}
            onClick={showCreateModal}
          >
            {t("case.createNew")}
          </Button>
        }
      >
        {/* 篩選表單 */}
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFilter}
          style={{ marginBottom: 16 }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8} lg={8}>
              <Form.Item name="case_no_pattern" label={t("case.caseNumber")}>
                <Input placeholder={t("case.enterCaseNumber")} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={8}>
              <Form.Item name="service_item" label={t("case.serviceItem")}>
                <Select
                  placeholder={t("case.serviceItemRequired")}
                  allowClear
                  options={serviceItemOptions}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={8}>
              <Form.Item name="dept" label={t("case.dept")}>
                <Select
                  placeholder={t("case.selectDept")}
                  allowClear
                  options={departmentOptions}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="statusTags"
                label={`${t("case.closed")}/${t("case.isApproved")}`}
              >
                <Select
                  mode="multiple"
                  allowClear
                  style={{ width: "100%" }}
                  placeholder={t("common.form.select")}
                  options={[
                    { value: t("case.closed"), label: t("case.closed") },
                    {
                      value: t("case.isApproved"),
                      label: t("case.isApproved"),
                    },
                  ]}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item name="date_range" label={t("case.dateRange")}>
                <DatePicker.RangePicker
                  style={{ width: "100%" }}
                  placeholder={[
                    t("common.date.startDate"),
                    t("common.date.endDate"),
                  ]}
                  onChange={(dates) => {
                    if (dates) {
                      form.setFieldsValue({
                        date_from: dates[0]?.format("YYYY-MM-DD"),
                        date_to: dates[1]?.format("YYYY-MM-DD"),
                      });
                    } else {
                      form.setFieldsValue({
                        date_from: undefined,
                        date_to: undefined,
                      });
                    }
                  }}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={12} lg={12}>
              <Form.Item name="agent_name" label={t("case.agent")}>
                <UserSelect
                  placeholder={t("case.enterAgentName")}
                  valueField="user_name"
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={12} lg={12}>
              <Form.Item name="handler_name" label={t("case.handlerName")}>
                <UserSelect
                  placeholder={t("case.enterHandlerName")}
                  valueField="user_name"
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={12} lg={12}>
              <Form.Item name="customer_name" hidden>
                <Input />
              </Form.Item>
              <Form.Item name="customer_number" label={t("case.customer")}>
                <CustomerSelect
                  fullData={true}
                  valueField="customer_number"
                  placeholder={t("case.enterCustomerName")}
                  onChange={(value) => {
                    if (value) {
                      form.setFieldsValue({
                        customer_name: value.customer_name,
                        customer_number: value.customer_number,
                      });
                    } else {
                      form.setFieldsValue({
                        customer_name: "",
                        customer_number: "",
                      });
                    }
                  }}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={24} lg={24}>
              <Form.Item
                label=" "
                colon={false}
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "flex-end",
                  marginBottom: 0,
                }}
              >
                <Space wrap>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<Icon library="lucide" name="Search" size={16} />}
                  >
                    {t("common.basic.search")}
                  </Button>
                  <Button
                    onClick={handleResetFilter}
                    icon={<Icon library="lucide" name="RotateCcw" size={16} />}
                  >
                    {t("common.basic.reset")}
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>

        {/* 案件表格 */}
        <Table
          columns={columns}
          dataSource={cases}
          rowKey="id"
          scroll={{ x: "max-content" }}
          loading={loading}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("case.emptyData")}
              />
            ),
          }}
          expandable={{
            expandedRowRender,
            expandRowByClick: true,
          }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: totalCount,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
            showTotal: (total) =>
              `${t("common.table.total")} ${total} ${t("common.table.unit")}`,
          }}
        />
      </Card>

      {/* 案件表單模態框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleSaveCase}
        onCancel={() => {
          setModalVisible(false);
          setEditingCase(null);
          caseForm.resetFields();
        }}
        confirmLoading={submitting}
        width={900}
        okText={t("common.basic.save")}
        cancelText={t("common.basic.cancel")}
      >
        <Form form={caseForm} layout="vertical">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="department"
                label={t("case.dept")}
                rules={[{ required: true, message: t("case.deptRequired") }]}
              >
                <Select
                  options={departmentOptions}
                  placeholder={t("case.selectDept")}
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="assign_date"
                label={t("case.assignDate")}
                rules={[
                  { required: true, message: t("case.assignDateRequired") },
                ]}
              >
                <DatePicker style={{ width: "100%" }} />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="case_code_type"
                label={t("case.caseCodeType")}
                rules={[
                  { required: true, message: t("case.caseCodeTypeRequired") },
                ]}
              >
                <Select
                  options={caseTypeOptions}
                  placeholder={t("case.selectCaseCodeType")}
                />
              </Form.Item>
            </Col>

            {editingCase?.case_no && (
              <Col xs={24} sm={12}>
                <Form.Item name="case_no" label={t("case.caseNumber")}>
                  <Input placeholder={t("case.enterCaseNumber")} disabled />
                </Form.Item>
              </Col>
            )}

            <Col xs={24} sm={12}>
              <Form.Item name="customer_name" hidden>
                <Input />
              </Form.Item>
              <Form.Item
                name="customer_number"
                label={t("case.customer")}
                rules={[
                  { required: true, message: t("case.customerRequired") },
                ]}
              >
                <CustomerSelect
                  fullData={true}
                  valueField="customer_number"
                  placeholder={t("case.enterCustomerName")}
                  onChange={(value) => {
                    if (value) {
                      caseForm.setFieldsValue({
                        customer_name: value.customer_name,
                        customer_number: value.customer_number,
                      });
                    }
                  }}
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="agent_name"
                label={t("case.agent")}
                rules={[{ required: true, message: t("case.agentRequired") }]}
              >
                <UserSelect
                  placeholder={t("case.agent")}
                  valueField="user_name"
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item name="handler1_name" label={t("case.handler1")}>
                <UserSelect
                  placeholder={t("case.handler1")}
                  valueField="user_name"
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item name="handler2_name" label={t("case.handler2")}>
                <UserSelect
                  placeholder={t("case.handler2")}
                  valueField="user_name"
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="service_item"
                label={t("case.serviceItem")}
                rules={[
                  { required: true, message: t("case.serviceItemRequired") },
                ]}
              >
                <Select
                  options={serviceItemOptions}
                  placeholder={t("case.enterServiceItem")}
                />
              </Form.Item>
            </Col>

            <Col xs={24}>
              <Form.Item
                name="service_content"
                label={t("case.serviceContent")}
                rules={[
                  { required: true, message: t("case.serviceContentRequired") },
                ]}
              >
                <TextArea
                  rows={4}
                  placeholder={t("case.enterServiceContent")}
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="estimated_completion_date"
                label={t("case.estimated_completion_date")}
                rules={[
                  {
                    required: true,
                    message: t("case.estimatedCompletionDateRequired"),
                  },
                ]}
              >
                <DatePicker style={{ width: "100%" }} />
              </Form.Item>
            </Col>

            {!editingCase?.file_download_url ? (
              <Col xs={24} sm={12}>
                <Form.Item
                  name="authorization_upload"
                  label={t("case.authorizationFile")}
                  valuePropName="fileList"
                  getValueFromEvent={(e) => {
                    if (Array.isArray(e)) {
                      return e;
                    }
                    return e?.fileList;
                  }}
                >
                  <Upload maxCount={1} beforeUpload={() => false}>
                    <Button
                      icon={<Icon library="lucide" name="Upload" size={16} />}
                    >
                      {t("common.basic.upload")}
                    </Button>
                  </Upload>
                </Form.Item>
              </Col>
            ) : (
              <Col xs={24} sm={12} className="flex flex-col items-start">
                <Text className="mb-2">{t("case.authorizationFile")}</Text>
                <Button
                  type="link"
                  onClick={() => {
                    window.open(editingCase?.file_download_url || "", "_blank");
                  }}
                  style={{ padding: 0 }}
                  disabled={!editingCase?.file_download_url}
                >
                  {t("case.authorizationFileDownload")}
                </Button>
              </Col>
            )}

            {editingCase && (
              <Col xs={24} sm={12}>
                <Form.Item name="close_date" label={t("case.closeDate")}>
                  <DatePicker style={{ width: "100%" }} />
                </Form.Item>
              </Col>
            )}
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default CasePage;
