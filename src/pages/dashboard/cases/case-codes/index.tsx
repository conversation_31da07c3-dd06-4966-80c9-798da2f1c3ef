import {
  Button,
  Card,
  Empty,
  Form,
  Input,
  Modal,
  Space,
  Table,
  message,
} from "antd";
import { useEffect, useState } from "react";
import { Helmet } from "react-helmet-async";
import { useTranslation } from "react-i18next";

import type {
  CaseCodeResponse,
  CreateCaseCodeRequest,
  UpdateCaseCodeRequest,
} from "@/api/models/case-code";
import { caseCodeService } from "@/api/services/caseCodeService";
import Icon from "@/components/icon";
import { useUserInfo } from "@/store/userStore";
import Title from "antd/lib/typography/Title";

const CaseCodesPage = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const userInfo = useUserInfo();

  const [caseCodes, setCaseCodes] = useState<CaseCodeResponse[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentCode, setCurrentCode] = useState<CaseCodeResponse | null>(null);

  // 獲取案件類型代碼列表
  const fetchCaseCodes = async (params?: any) => {
    try {
      setLoading(true);
      const response = await caseCodeService.getAllCaseCodes({
        page: currentPage,
        limit: pageSize,
        ...params,
      });
      setCaseCodes(response.items || []);
      setTotal(response.total_count || 0);
    } catch (error) {
      console.error("獲取案件類型列表失敗:", error);
      message.error(t("caseCode.fetchFailed"));
    } finally {
      setLoading(false);
    }
  };

  // 初始加載
  useEffect(() => {
    fetchCaseCodes();
  }, [currentPage, pageSize]);

  // 處理搜索
  const handleSearch = async () => {
    try {
      const values = await searchForm.validateFields();
      setCurrentPage(1);
      fetchCaseCodes(values);
    } catch (error) {
      console.error("搜索表單驗證失敗:", error);
    }
  };

  // 重置搜索表單
  const handleReset = () => {
    searchForm.resetFields();
    setCurrentPage(1);
    fetchCaseCodes();
  };

  // 顯示創建模態框
  const showCreateModal = () => {
    setIsEditing(false);
    setCurrentCode(null);
    form.resetFields();
    form.setFieldsValue({
      type: "",
      name: "",
      code: "",
    });
    setIsModalVisible(true);
  };

  // 顯示編輯模態框
  const showEditModal = (code: CaseCodeResponse) => {
    setIsEditing(true);
    setCurrentCode(code);
    form.setFieldsValue({
      code: code.code,
      type: code.type,
      name: code.name,
    });
    setIsModalVisible(true);
  };

  // 取消模態框
  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  // 刪除案件類型代碼
  const handleDelete = async (codeId: string) => {
    Modal.confirm({
      title: t("common.confirm.confirmDelete"),
      content: t("caseCode.confirmDeleteDesc"),
      onOk: async () => {
        try {
          await caseCodeService.deleteCaseCode(codeId);
          message.success(t("common.result.deleteSuccess"));
          fetchCaseCodes();
        } catch (error) {
          console.error("刪除案件類型失敗:", error);
          message.error(t("common.result.deleteFailed"));
        }
      },
    });
  };

  // 提交表單
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (isEditing && currentCode) {
        // 更新案件類型
        const updateData: UpdateCaseCodeRequest = {
          name: values.name,
          type: values.type,
        };

        if (currentCode.id) {
          await caseCodeService.updateCaseCode(currentCode.id, updateData);
        } else {
          throw new Error("Current code ID is missing.");
        }
        message.success(t("common.result.updateSuccess"));
      } else {
        // 創建案件類型
        const createData: CreateCaseCodeRequest = {
          code: values.code,
          type: values.type,
          name: values.name,
        };

        await caseCodeService.createCaseCode(createData);
        message.success(t("common.result.createSuccess"));
      }

      setIsModalVisible(false);
      form.resetFields();
      fetchCaseCodes();
    } catch (error) {
      console.error("提交表單失敗:", error);
      message.error(
        isEditing
          ? t("common.result.updateFailed")
          : t("common.result.createFailed")
      );
    }
  };

  // 表格列定義
  const columns = [
    {
      title: t("caseCode.caseCategory"),
      dataIndex: "type",
      key: "type",
    },
    {
      title: t("caseCode.caseName"),
      dataIndex: "name",
      key: "name",
    },
    {
      title: t("caseCode.caseCategoryNumber"),
      dataIndex: "code",
      key: "code",
      ellipsis: true,
    },
    {
      title: t("common.basic.actions"),
      key: "action",
      render: (_: any, record: CaseCodeResponse) => (
        <Space>
          <Button
            type="text"
            icon={<Icon library="lucide" name="Edit" size={16} />}
            onClick={() => showEditModal(record)}
            title={
              userInfo.isAdmin ? t("common.basic.edit") : t("caseCode.isAdmin")
            }
            disabled={!userInfo.isAdmin} // 只有管理員可以編輯
          />
          <Button
            type="text"
            danger
            icon={<Icon library="lucide" name="Trash2" size={16} />}
            onClick={() => record.id && handleDelete(record.id)}
            title={t("common.basic.delete")}
          />
        </Space>
      ),
    },
  ];

  return (
    <>
      <Helmet>
        <title>{t("caseCode.title")}</title>
      </Helmet>

      <Card
        title={<Title level={4}>{t("caseCode.title")}</Title>}
        extra={
          <Space>
            <Button
              type="primary"
              icon={<Icon library="lucide" name="FilePlus" size={16} />}
              disabled={!userInfo.isAdmin} // 只有管理員可以新增
              onClick={showCreateModal}
              title={t("caseCode.isAdmin")}
            >
              {t("common.basic.add")}
            </Button>
          </Space>
        }
      >
        {/* 搜索表單 */}
        <Form
          form={searchForm}
          onFinish={handleSearch}
          style={{ marginBottom: 24 }}
        >
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fill, minmax(250px, 1fr))",
              gap: "16px",
              marginBottom: "16px",
            }}
          >
            <Form.Item
              name="type"
              label={t("caseCode.caseCategory")}
              style={{ marginBottom: 0 }}
            >
              <Input placeholder={t("caseCode.enterCaseCategory")} allowClear />
            </Form.Item>

            <Form.Item
              name="name"
              label={t("caseCode.caseName")}
              style={{ marginBottom: 0 }}
            >
              <Input placeholder={t("caseCode.enterCaseName")} allowClear />
            </Form.Item>

            <Form.Item
              name="code"
              label={t("caseCode.caseCategoryNumber")}
              style={{ marginBottom: 0 }}
            >
              <Input
                placeholder={t("caseCode.enterCaseCategoryNumber")}
                allowClear
              />
            </Form.Item>
          </div>

          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Form.Item style={{ marginBottom: 0 }}>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<Icon library="lucide" name="Search" size={16} />}
                >
                  {t("common.basic.search")}
                </Button>
                <Button
                  onClick={handleReset}
                  icon={<Icon library="lucide" name="RotateCcw" size={16} />}
                >
                  {t("common.basic.reset")}
                </Button>
              </Space>
            </Form.Item>
          </div>
        </Form>

        {/* 案件類型代碼表格 */}
        <Table
          columns={columns}
          dataSource={caseCodes}
          rowKey="id"
          scroll={{ x: "max-content" }}
          loading={loading}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("case.emptyData")}
              />
            ),
          }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
            showTotal: (total) =>
              `${t("common.table.total")} ${total} ${t("common.table.unit")}`,
          }}
        />
      </Card>

      {/* 新增/編輯模態框 */}
      <Modal
        title={isEditing ? t("caseCode.editTitle") : t("caseCode.createTitle")}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="type"
            label={t("caseCode.caseCategory")}
            rules={[
              { required: true, message: t("caseCode.caseCategoryRequired") },
              { max: 50, message: t("caseCode.caseCategoryTooLong") },
            ]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="name"
            label={t("caseCode.caseName")}
            rules={[
              { required: true, message: t("caseCode.caseNameRequired") },
              { max: 100, message: t("caseCode.caseNameTooLong") },
            ]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="code"
            label={t("caseCode.caseCategoryNumber")}
            rules={[
              {
                required: true,
                message: t("caseCode.caseCategoryNumberRequired"),
              },
              { max: 50, message: t("caseCode.caseCategoryNumberTooLong") },
            ]}
            extra={t("caseCode.codeHint")}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default CaseCodesPage;
