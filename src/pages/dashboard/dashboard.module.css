.module-card {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.module-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
}

.disabled-card {
  background-color: #f9f9f9;
  box-shadow: none;
}

.disabled-card:hover {
  transform: none;
  box-shadow: none;
}

.module-card .ant-card-body {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.module-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: #f0f7ff;
  margin-bottom: 16px;
}

.module-card-button {
  margin-top: auto;
}
