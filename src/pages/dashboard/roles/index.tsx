import type {
  CreateRoleRequest,
  RoleResponse,
  UpdateRoleRequest,
} from "@/api/models/roles";
import { permissionService } from "@/api/services/permissionService";
import { roleService } from "@/api/services/roleService";
import Icon from "@/components/icon";
import {
  Button,
  Card,
  Empty,
  Form,
  Input,
  Modal,
  Space,
  Table,
  message,
} from "antd";
import Title from "antd/es/typography/Title";
import { useEffect, useState } from "react";
import { Helmet } from "react-helmet-async";
import { useTranslation } from "react-i18next";

const RoleManagementPage = () => {
  const { t } = useTranslation();
  const [roles, setRoles] = useState<RoleResponse[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentRole, setCurrentRole] = useState<RoleResponse | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();

  const fetchRoles = async (params?: any) => {
    try {
      setLoading(true);
      const response = await roleService.getAllRoles({
        page: currentPage,
        limit: pageSize,
        ...params,
      });
      setRoles(response.items);
      setTotal(response.total_count || 0);
    } catch (error) {
      console.error("獲取角色列表失敗:", error);
      message.error(t("roles.fetchFailed"));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRoles();
  }, [currentPage, pageSize]);

  const handleSearch = async () => {
    const values = await searchForm.validateFields();
    setCurrentPage(1);
    fetchRoles(values);
  };

  const handleReset = () => {
    searchForm.resetFields();
    setCurrentPage(1);
    fetchRoles();
  };

  const showCreateModal = () => {
    setIsEditing(false);
    setCurrentRole(null);
    setIsModalVisible(true);

    setTimeout(() => {
      form.resetFields();
    }, 100);
  };

  const showEditModal = (role: RoleResponse) => {
    setIsEditing(true);
    setCurrentRole(role);
    setIsModalVisible(true);

    setTimeout(() => {
      form.setFieldsValue({
        role_name: role.role_name,
        role_desc: role.role_desc,
      });
    }, 100);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleDelete = async (roleId: string) => {
    Modal.confirm({
      title: t("common.confirm.confirmDelete"),
      content: t("roles.confirmDeleteDesc"),
      onOk: async () => {
        try {
          await roleService.deleteRole(roleId);
          message.success(t("common.result.deleteSuccess"));
          fetchRoles();
        } catch (error) {
          console.error("刪除角色失敗:", error);
          message.error(t("common.result.deleteFailed"));
        }
      },
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (isEditing && currentRole) {
        // 更新角色
        await roleService.updateRole(currentRole.id, {
          role_name: values.role_name,
          role_desc: values.role_desc,
        } as UpdateRoleRequest);
        message.success(t("common.result.updateSuccess"));
      } else {
        // 創建角色
        const response = await roleService.createRole({
          role_name: values.role_name,
          role_desc: values.role_desc,
        } as CreateRoleRequest);
        message.success(t("common.result.createSuccess"));

        // 為新建的角色初始化權限
        try {
          await permissionService.initEmptyPermission(response.id);
          message.success(t("permissions.initPermissionSuccess"));
        } catch (error) {
          console.error("初始化角色權限失敗:", error);
          message.warning(t("permissions.initPermissionFailed"));
        }
      }
      setIsModalVisible(false);
      form.resetFields();
      fetchRoles();
    } catch (error) {
      console.error("提交表單失敗:", error);
      message.error(t("common.result.submitFailed"));
    }
  };

  const columns = [
    {
      title: t("roles.name"),
      dataIndex: "role_name",
      key: "role_name",
    },
    {
      title: t("roles.description"),
      dataIndex: "role_desc",
      key: "role_desc",
      width: "160px",
    },
    {
      title: t("common.basic.actions"),
      key: "action",
      render: (_: any, record: RoleResponse) => (
        <Space>
          <Button
            type="text"
            icon={<Icon library="lucide" name="Edit" size={16} />}
            onClick={() => showEditModal(record)}
            title={t("common.basic.edit")}
          />
          <Button
            type="text"
            danger
            icon={<Icon library="lucide" name="Trash2" size={16} />}
            onClick={() => handleDelete(record.id)}
            title={t("common.basic.delete")}
          />
        </Space>
      ),
    },
  ];

  return (
    <>
      <Helmet>
        <title>{t("roles.title")}</title>
      </Helmet>

      <Card
        title={<Title level={4}>{t("roles.title")}</Title>}
        extra={
          <Button
            type="primary"
            icon={<Icon library="lucide" name="Plus" size={16} />}
            onClick={showCreateModal}
          >
            {t("roles.add")}
          </Button>
        }
      >
        <Form
          form={searchForm}
          layout="horizontal"
          style={{ marginBottom: 24 }}
        >
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fill, minmax(240px, 1fr))",
              gap: "16px",
              marginBottom: "16px",
            }}
          >
            <Form.Item name="role_name" label={t("roles.name")}>
              <Input placeholder={t("roles.inputName")} allowClear />
            </Form.Item>
          </div>
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Form.Item style={{ marginRight: 8, marginBottom: 0 }}>
              <Button
                type="primary"
                htmlType="button"
                icon={<Icon library="lucide" name="Search" size={16} />}
                onClick={handleSearch}
              >
                {t("common.basic.search")}
              </Button>
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <Button htmlType="button" onClick={handleReset}>
                {t("common.basic.reset")}
              </Button>
            </Form.Item>
          </div>
        </Form>

        <Table
          columns={columns}
          dataSource={roles}
          rowKey="id"
          scroll={{ x: "max-content" }}
          loading={loading}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("documents.common.emptyData")}
              />
            ),
          }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => t("common.table.total", { total }),
          }}
        />
      </Card>

      <Modal
        title={isEditing ? t("roles.edit") : t("roles.add")}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="role_name"
            label={t("roles.name")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <Input placeholder={t("roles.inputName")} />
          </Form.Item>
          <Form.Item
            name="role_desc"
            label={t("roles.description")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <Input.TextArea
              placeholder={t("roles.inputDescription")}
              rows={4}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default RoleManagementPage;
