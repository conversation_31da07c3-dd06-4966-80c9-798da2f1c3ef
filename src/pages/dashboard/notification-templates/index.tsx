import { ChannelType, TemplateType } from "@/api/models/notification-templates";
import type {
  CreateNotificationTemplateRequest,
  NotificationTemplateListResponse,
  NotificationTemplateResponse,
  UpdateNotificationTemplateRequest,
} from "@/api/models/notification-templates";
import { notificationTemplatesService } from "@/api/services/notificationTemplatesService";
import Icon from "@/components/icon";
import {
  Button,
  Card,
  Empty,
  Form,
  Input,
  Modal,
  Select,
  Space,
  Table,
  message,
} from "antd";
import Title from "antd/es/typography/Title";
import dayjs from "dayjs";
import htmlToMd from "html-to-md";
import { useEffect, useMemo, useState } from "react";
// 預先導入 ReactDOMServer
import * as ReactDOMServer from "react-dom/server";
import { Helmet } from "react-helmet-async";
import { useTranslation } from "react-i18next";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";

// Markdown 編輯器組件
const MarkdownEditor = ({
  value,
  onChange,
}: {
  value: string;
  onChange: (value: string) => void;
}) => {
  const { t } = useTranslation();

  return (
    <div className="markdown-editor">
      <Input.TextArea
        rows={8}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={t("notification.templates_page.markdown_placeholder")}
      />
      <div style={{ marginTop: 8, fontSize: 12, color: "#999" }}>
        {t(
          "notification.templates_page.markdown_tip",
          "支持 Markdown 格式輸入"
        )}
      </div>
    </div>
  );
};

// 共用的 Markdown 轉 HTML 函數
const convertMarkdownToHtml = (markdown: string): string => {
  try {
    const reactElement = (
      <ReactMarkdown rehypePlugins={[rehypeRaw]}>{markdown}</ReactMarkdown>
    );
    return ReactDOMServer.renderToString(reactElement);
  } catch (error) {
    console.error("Markdown 轉 HTML 失敗:", error);
    return markdown;
  }
};

const NotificationTemplatesPage = () => {
  const { t } = useTranslation();
  const [templates, setTemplates] = useState<NotificationTemplateResponse[]>(
    []
  );
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentTemplate, setCurrentTemplate] =
    useState<NotificationTemplateResponse | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [markdownValue, setMarkdownValue] = useState<string>("");
  const [selectedChannel, setSelectedChannel] = useState<ChannelType>(
    ChannelType.EMAIL
  );

  // 使用 useMemo 優化選項數組
  const templateTypeOptions = useMemo(
    () => [
      {
        value: TemplateType.LONG,
        label: t("notification.template_types.long"),
      },
      {
        value: TemplateType.SHORT,
        label: t("notification.template_types.short"),
      },
    ],
    [t]
  );

  const channelTypeOptions = useMemo(
    () => [
      {
        value: ChannelType.EMAIL,
        label: t("notification.channel_types.email"),
      },
      { value: ChannelType.LINE, label: t("notification.channel_types.line") },
      { value: ChannelType.SMS, label: t("notification.channel_types.sms") },
      { value: ChannelType.ALL, label: t("notification.channel_types.all") },
    ],
    [t]
  );

  const fetchTemplates = async (params?: any) => {
    try {
      setLoading(true);
      const response = await notificationTemplatesService.getAllTemplates({
        page: currentPage,
        limit: pageSize,
        ...params,
      });

      // 使用類型斷言處理 API 返回結構
      const apiResponse = response as NotificationTemplateListResponse;

      // 轉換返回的數據為前端需要的格式
      const templates = apiResponse.templates || [];

      setTemplates(templates);
      setTotal(apiResponse.total || 0);
    } catch (error: any) {
      console.error("獲取通知模板列表失敗:", error);
      message.error(t("common.result.operationFailed"));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTemplates();
  }, [currentPage, pageSize]);

  const handleSearch = async () => {
    try {
      const values = await searchForm.validateFields();
      const params = {
        name: values.search_name,
        type: values.search_type,
        channels: values.search_channels,
      };
      setCurrentPage(1);
      await fetchTemplates(params);
    } catch (error: any) {
      console.error("搜索表單驗證失敗:", error);
    }
  };

  const handleReset = () => {
    searchForm.resetFields();
    setCurrentPage(1);
    fetchTemplates();
  };

  const showCreateModal = () => {
    setIsEditing(false);
    setCurrentTemplate(null);
    form.resetFields();
    setSelectedChannel(ChannelType.EMAIL);
    setMarkdownValue("");
    setIsModalVisible(true);
  };

  const showEditModal = (template: NotificationTemplateResponse) => {
    setIsEditing(true);
    setCurrentTemplate(template);
    setSelectedChannel(template.channels);

    // 準備內容值
    let content = template.content || "";

    // 如果是電子郵件渠道，將 HTML 轉換為 Markdown
    if (template.channels === ChannelType.EMAIL && template.content) {
      try {
        content = htmlToMd(template.content);
      } catch (error: any) {
        console.error("HTML 轉 Markdown 失敗:", error);
        // 如果轉換失敗，保持原始內容
      }
    }

    // 設置 Markdown 編輯器的值
    setMarkdownValue(content);

    // 設置表單字段值
    form.setFieldsValue({
      name: template.name,
      type: template.type,
      channels: template.channels,
      subject: template.subject,
      content,
    });

    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleDelete = async (templateId: string) => {
    Modal.confirm({
      title: t("common.confirm.confirmDelete"),
      content: t("common.confirmDelete"),
      okText: t("common.basic.confirm"),
      cancelText: t("common.basic.cancel"),
      onOk: async () => {
        try {
          await notificationTemplatesService.deleteTemplate(templateId);
          message.success(t("common.result.deleteSuccess"));
          fetchTemplates();
        } catch (error: any) {
          console.error("刪除通知模板失敗:", error);
          message.error(t("common.result.deleteFailed"));
        }
      },
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 處理內容格式
      let content = "";

      // 如果是電子郵件渠道，則將 Markdown 轉換為 HTML
      if (values.channels === ChannelType.EMAIL) {
        content = convertMarkdownToHtml(markdownValue);
      } else {
        // 其他渠道使用普通文本
        content = values.content;
      }

      if (isEditing && currentTemplate) {
        // 更新模板
        const updateData: UpdateNotificationTemplateRequest = {
          name: values.name,
          type: values.type,
          channels: values.channels,
          subject: values.subject,
          content,
        };

        await notificationTemplatesService.updateTemplate(
          currentTemplate._id,
          updateData
        );
        message.success(t("common.result.updateSuccess"));
      } else {
        // 創建模板
        const createData: CreateNotificationTemplateRequest = {
          name: values.name,
          type: values.type,
          channels: values.channels,
          subject: values.subject,
          content,
        };

        await notificationTemplatesService.createTemplate(createData);
        message.success(t("common.result.createSuccess"));
      }
      setIsModalVisible(false);
      form.resetFields();
      fetchTemplates();
    } catch (error: any) {
      console.error("提交表單失敗:", error);
      message.error(
        isEditing
          ? t("common.result.updateFailed")
          : t("common.result.createFailed")
      );
    }
  };

  // 當渠道改變時更新 selectedChannel
  const handleChannelChange = (value: ChannelType) => {
    const prevChannel = selectedChannel;
    setSelectedChannel(value);

    // 處理渠道切換時的內容轉換
    const currentContent = form.getFieldValue("content") || "";

    // 如果從其他渠道切換到電子郵件渠道
    if (value === ChannelType.EMAIL && prevChannel !== ChannelType.EMAIL) {
      // 將表單中的純文本內容設置為 markdownValue
      setMarkdownValue(currentContent);
      // 清空表單字段，因為電子郵件渠道使用 markdownValue
      form.setFieldValue("content", "");
    }
    // 如果從電子郵件渠道切換到其他渠道
    else if (value !== ChannelType.EMAIL && prevChannel === ChannelType.EMAIL) {
      const htmlContent = convertMarkdownToHtml(markdownValue);
      form.setFieldValue("content", htmlContent);
    }
  };

  const columns = [
    {
      title: t("notification.templates_page.name"),
      dataIndex: "name",
      key: "name",
    },
    {
      title: t("notification.templates_page.type"),
      dataIndex: "type",
      key: "type",
      render: (type: TemplateType) => {
        const option = templateTypeOptions.find((opt) => opt.value === type);
        return option ? option.label : type;
      },
    },
    {
      title: t("notification.templates_page.channel"),
      dataIndex: "channels",
      key: "channels",
      render: (channel: ChannelType) => {
        const option = channelTypeOptions.find((opt) => opt.value === channel);
        return option ? option.label : channel;
      },
    },
    {
      title: t("notification.templates_page.created_by"),
      dataIndex: "created_by",
      key: "created_by",
    },
    {
      title: t("notification.templates_page.created_at"),
      dataIndex: "created_at",
      key: "created_at",
      render: (date: string) =>
        date ? dayjs(date).format("YYYY/MM/DD HH:mm:ss") : "",
    },
    {
      title: t("common.basic.actions"),
      key: "action",
      render: (_: any, record: NotificationTemplateResponse) => (
        <Space size="middle">
          <Button
            type="text"
            onClick={() => showEditModal(record)}
            icon={<Icon library="lucide" name="Edit" size={16} />}
            title={t("common.basic.edit")}
          />
          <Button
            type="text"
            danger
            onClick={() => handleDelete(record._id)}
            icon={<Icon library="lucide" name="Trash2" size={16} />}
            title={t("common.basic.delete")}
          />
        </Space>
      ),
    },
  ];

  return (
    <>
      <Helmet>
        <title>{t("notification.templates_page.title")} - Admin</title>
      </Helmet>
      <Card
        title={
          <Title level={4}>{t("notification.templates_page.title")}</Title>
        }
        bordered={false}
        extra={
          <Button
            type="primary"
            icon={<Icon library="lucide" name="Plus" size={16} />}
            onClick={showCreateModal}
          >
            {t("notification.templates_page.create")}
          </Button>
        }
      >
        <Form
          form={searchForm}
          layout="horizontal"
          style={{ marginBottom: 24 }}
          name="templateSearchForm"
        >
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fill, minmax(240px, 1fr))",
              gap: "16px",
              marginBottom: "16px",
            }}
          >
            <Form.Item
              name="search_name"
              label={t("notification.templates_page.name")}
            >
              <Input
                placeholder={t("notification.templates_page.search")}
                allowClear
              />
            </Form.Item>
            <Form.Item
              name="search_type"
              label={t("notification.templates_page.type")}
            >
              <Select
                placeholder={t("notification.templates_page.type")}
                allowClear
                options={templateTypeOptions}
              />
            </Form.Item>
            <Form.Item
              name="search_channels"
              label={t("notification.templates_page.channel")}
            >
              <Select
                placeholder={t("notification.templates_page.channel")}
                allowClear
                options={channelTypeOptions}
              />
            </Form.Item>
          </div>
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Form.Item style={{ marginRight: 8, marginBottom: 0 }}>
              <Button
                type="primary"
                htmlType="button"
                icon={<Icon library="lucide" name="Search" size={16} />}
                onClick={handleSearch}
              >
                {t("common.basic.search")}
              </Button>
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <Button htmlType="button" onClick={handleReset}>
                {t("common.basic.reset")}
              </Button>
            </Form.Item>
          </div>
        </Form>

        <Table
          columns={columns}
          dataSource={templates}
          rowKey="_id"
          scroll={{ x: "max-content" }}
          loading={loading}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("documents.common.emptyData")}
              />
            ),
          }}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
            showTotal: (total) =>
              `${t("common.table.total")} ${total} ${t("common.table.unit")}`,
          }}
        />
      </Card>

      <Modal
        title={
          isEditing
            ? t("notification.templates_page.edit")
            : t("notification.templates_page.create")
        }
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        width={650}
        okText={t("common.basic.confirm")}
        cancelText={t("common.basic.cancel")}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            type: TemplateType.LONG,
            channels: ChannelType.EMAIL,
          }}
        >
          <Form.Item
            name="name"
            label={t("notification.templates_page.name")}
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="type"
            label={t("notification.templates_page.type")}
            rules={[{ required: true }]}
          >
            <Select options={templateTypeOptions} />
          </Form.Item>
          <Form.Item
            name="channels"
            label={t("notification.templates_page.channel")}
            rules={[{ required: true }]}
          >
            <Select
              options={channelTypeOptions}
              onChange={handleChannelChange}
            />
          </Form.Item>
          <Form.Item
            name="subject"
            label={t("notification.templates_page.subject")}
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="content"
            label={t("notification.templates_page.content")}
            rules={[
              {
                required: true,
                validator: (_, value) => {
                  // 對於電子郵件渠道，使用 markdownValue 驗證
                  const contentToValidate =
                    selectedChannel === ChannelType.EMAIL
                      ? markdownValue
                      : value;

                  return contentToValidate
                    ? Promise.resolve()
                    : Promise.reject(new Error(t("common.form.required")));
                },
              },
            ]}
          >
            {selectedChannel === ChannelType.EMAIL ? (
              <MarkdownEditor
                value={markdownValue}
                onChange={setMarkdownValue}
              />
            ) : (
              <Input.TextArea rows={6} />
            )}
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default NotificationTemplatesPage;
