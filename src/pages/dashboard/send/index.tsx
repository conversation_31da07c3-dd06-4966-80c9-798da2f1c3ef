import Icon from "@/components/icon";
import Markdown from "@/components/markdown";
import {
  <PERSON><PERSON>,
  Card,
  DatePicker,
  Descriptions,
  Empty,
  Form,
  Input,
  List,
  Modal,
  Radio,
  Select,
  Space,
  Table,
  Typography,
  message,
} from "antd";
import dayjs from "dayjs";
import htmlToMd from "html-to-md";
import { useEffect, useState } from "react";
import * as ReactDOMServer from "react-dom/server";
import { useTranslation } from "react-i18next";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";

import { ChannelType } from "@/api/models/notification-templates";
import type { NotificationTemplateResponse } from "@/api/models/notification-templates";
import type { ResourceResponse } from "@/api/models/resources";
import { CategoryEnum, ContactTypeEnum, SendTypeEnum } from "@/api/models/send";
import type { FileObject, SendRecordResponse } from "@/api/models/send";
import type { TableProps } from "antd";
import type { RangePickerProps } from "antd/es/date-picker";

// 導入共用元件
import {
  RecipientSelect,
  ResourceSelect,
  TemplateSelect,
} from "@/components/custom-select";

// Markdown 轉 HTML 函數
const convertMarkdownToHtml = (markdown: string): string => {
  try {
    const reactElement = (
      <ReactMarkdown rehypePlugins={[rehypeRaw]}>{markdown}</ReactMarkdown>
    );
    return ReactDOMServer.renderToString(reactElement);
  } catch (error) {
    console.error("Markdown 轉 HTML 失敗:", error);
    return markdown;
  }
};

import { sendService } from "@/api/services/sendService";

const { Title, Text } = Typography;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

const SendPage = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState<SendRecordResponse[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isAttachmentsModalVisible, setIsAttachmentsModalVisible] =
    useState(false);
  const [currentAttachments, setCurrentAttachments] = useState<FileObject[]>(
    []
  );

  // 批次刪除相關狀態
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [isBatchDeleteModalVisible, setIsBatchDeleteModalVisible] =
    useState(false);

  // 模板相關狀態
  const [isTemplateModalVisible, setIsTemplateModalVisible] = useState(false);

  // 資源相關狀態
  const [isResourceModalVisible, setIsResourceModalVisible] = useState(false);

  // 編輯器相關狀態
  const [markdownContent, setMarkdownContent] = useState<string>("");
  const [selectedSendType, setSelectedSendType] = useState<string | undefined>(
    undefined
  );

  const [form] = Form.useForm();
  const [keyword, setKeyword] = useState("");
  const [dateRange, setDateRange] = useState<[string, string] | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(
    undefined
  );
  const [selectedSearchSendType, setSelectedSearchSendType] = useState<
    string | undefined
  >(undefined);

  // 獲取記錄列表
  const fetchRecords = async (page = 1, limit = 10) => {
    try {
      setLoading(true);
      const response = await sendService.getSendRecords({
        page,
        limit,
        keyword,
        category: selectedCategory as CategoryEnum | undefined,
        send_type: selectedSearchSendType as SendTypeEnum | undefined,
        start_date: dateRange ? dateRange[0] : undefined,
        end_date: dateRange ? dateRange[1] : undefined,
      });
      setRecords(response.records);
      setTotal(response.total);
      setCurrentPage(response.page);
      setPageSize(response.limit);
    } catch (error) {
      message.error(t("notification.send_page.fetch_failed"));
      console.error("Failed to fetch send records:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecords();
  }, []);
  // 檢視模態框相關狀態
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [viewRecord, setViewRecord] = useState<SendRecordResponse | null>(null);

  // 檢視功能
  const showViewModal = (record: SendRecordResponse) => {
    setViewRecord(record);
    setIsViewModalVisible(true);
  };

  const handleViewCancel = () => {
    setIsViewModalVisible(false);
  };

  // 表格列定義
  const columns: TableProps<SendRecordResponse>["columns"] = [
    {
      title: t("notification.send_page.customer_name"),
      dataIndex: "customer_name",
      key: "customer_name",
      width: 160,
      ellipsis: true,
      render: (text) => text || "-",
    },
    {
      title: t("notification.send_page.customer_tax_id"),
      dataIndex: "customer_tax_id",
      key: "customer_tax_id",
      width: 140,
      ellipsis: true,
      render: (text) => text || "-",
    },
    {
      title: t("notification.send_page.customer_number"),
      dataIndex: "customer_number",
      key: "customer_number",
      width: 120,
      ellipsis: true,
      render: (text) => text || "-",
    },
    {
      title: t("notification.send_page.recipients"),
      dataIndex: "recipient",
      key: "recipient",
      width: 200,
      ellipsis: true,
      render: (text) => text || "-",
    },
    {
      title: t("notification.send_page.sent_time"),
      dataIndex: "send_time",
      key: "send_time",
      width: 180,
      render: (send_time) => dayjs(send_time).format("YYYY-MM-DD HH:mm:ss"),
    },
    {
      title: t("common.basic.actions"),
      key: "actions",
      align: "center",
      width: 180,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<Icon library="lucide" name="Eye" size={16} />}
            onClick={() => showViewModal(record)}
            title={t("common.basic.view")}
          />
          <Button
            className="text-[#007bff] disabled:text-gray-400"
            type="text"
            icon={<Icon library="lucide" name="File" size={16} />}
            onClick={() => record.fileObject && openFile(record.fileObject.url)}
            disabled={!record.fileObject}
            title={t("notification.send_page.file")}
          />
          <Button
            className="text-[#4CAF50] disabled:text-gray-400"
            type="text"
            icon={<Icon library="lucide" name="Paperclip" size={16} />}
            onClick={() =>
              record.additionalFiles &&
              record.additionalFiles.length > 0 &&
              showAttachmentsModal(record.additionalFiles)
            }
            disabled={
              !record.additionalFiles || record.additionalFiles.length === 0
            }
            title={t("notification.send_page.attachment")}
          />
        </Space>
      ),
    },
  ];

  // 處理搜尋
  const handleSearch = () => {
    setCurrentPage(1);
    fetchRecords(1, pageSize);
  };

  // 處理日期範圍變更
  const handleDateRangeChange: RangePickerProps["onChange"] = (
    _,
    dateStrings
  ) => {
    setDateRange(
      dateStrings[0] && dateStrings[1] ? [dateStrings[0], dateStrings[1]] : null
    );
  };

  // 重置搜尋
  const handleReset = () => {
    setKeyword("");
    setDateRange(null);
    setSelectedCategory(undefined);
    setSelectedSearchSendType(undefined);
    setCurrentPage(1);
    fetchRecords(1, pageSize);
  };

  // 處理表格分頁變更
  const handleTableChange: TableProps<SendRecordResponse>["onChange"] = (
    pagination
  ) => {
    setCurrentPage(pagination.current || 1);
    setPageSize(pagination.pageSize || 10);
    fetchRecords(pagination.current || 1, pagination.pageSize || 10);
  };

  // 處理開啟發送模態框
  const handleShowModal = () => {
    form.resetFields();
    setMarkdownContent("");
    setIsModalVisible(true);
  };

  // 處理關閉模態框
  const handleCancel = () => {
    setIsModalVisible(false);
    setSelectedSendType(undefined);
    setMarkdownContent("");
    form.resetFields();
  };
  // 處理表單提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // recipients現在已經是聯繫方式字符串數組，不需要轉換
      const recipients: string[] = values.recipients || [];

      // 根據發送類型確定使用的內容
      const content =
        values.send_type === SendTypeEnum.EMAIL
          ? convertMarkdownToHtml(markdownContent) // 如果是電子郵件，使用markdown內容轉換成HTML
          : values.content; // 如果是LINE，使用普通文本內容

      // 發送請求
      const response = await sendService.sendMessage({
        category: CategoryEnum[values.category],
        send_type: values.send_type,
        recipients,
        subject: values.subject,
        content,
      });

      if (response.success) {
        message.success(t("notification.send_page.send_success"));
        setIsModalVisible(false);
        fetchRecords(currentPage, pageSize);
      } else {
        message.error(
          response.message || t("notification.send_page.send_failed")
        );
      }
    } catch (error) {
      console.error("Failed to send message:", error);
      message.error(t("notification.send_page.send_failed"));
    } finally {
      setLoading(false);
    }
  };
  // 發送類型改變時的處理
  const handleSendTypeChange = (value: string) => {
    form.setFieldsValue({ send_type: value });
    setSelectedSendType(value);

    // 當類型改變時，清空內容和收件人
    if (value === SendTypeEnum.EMAIL) {
      setMarkdownContent("");
      form.setFieldsValue({ content: "", recipients: [] });
    } else {
      form.setFieldsValue({ content: "", recipients: [] });
    }
  };

  // 顯示附件模態框
  const showAttachmentsModal = (files: FileObject[]) => {
    setCurrentAttachments(files);
    setIsAttachmentsModalVisible(true);
  };

  // 關閉附件模態框
  const handleAttachmentsModalClose = () => {
    setIsAttachmentsModalVisible(false);
  };

  // 開啟檔案
  const openFile = (url: string) => {
    window.open(url, "_blank");
  };

  // 顯示模板選擇模態框
  const showTemplateModal = () => {
    const sendType = form.getFieldValue("send_type");
    if (!sendType) {
      message.warning(t("notification.send_page.select_send_type_first"));
      return;
    }

    setIsTemplateModalVisible(true);
  };

  // 處理模板選擇
  const handleTemplateSelect = (template: NotificationTemplateResponse) => {
    const sendType = form.getFieldValue("send_type");

    // 清空現有內容
    if (sendType === SendTypeEnum.EMAIL) {
      setMarkdownContent(htmlToMd(template.content));
      form.setFieldsValue({ content: "" });
    } else {
      form.setFieldsValue({ content: template.content });
    }

    // 如果模板有主旨，也設置主旨
    if (template.subject) {
      form.setFieldsValue({ subject: template.subject });
    }

    setIsTemplateModalVisible(false);
  };

  // 顯示資源選擇模態框
  const showResourceModal = () => {
    setIsResourceModalVisible(true);
  };
  // 處理資源選擇
  const handleResourceSelect = (resource: ResourceResponse) => {
    const sendType = form.getFieldValue("send_type");
    const resourceTitle = resource.title || "資源";
    const resourceUrl = resource.url || "";

    if (sendType === SendTypeEnum.EMAIL) {
      // Markdown 格式: [資源名稱](資源連結)
      const markdownLink = `[${resourceTitle}](${resourceUrl})`;
      setMarkdownContent(
        (prevContent) =>
          prevContent + (prevContent ? "\n\n" : "") + markdownLink
      );
    } else {
      // LINE 格式: 資源名稱：資源連結
      const textLink = `${resourceTitle}：${resourceUrl}`;
      const currentContent = form.getFieldValue("content") || "";
      form.setFieldsValue({
        content: currentContent + (currentContent ? "\n\n" : "") + textLink,
      });
    }
  };

  // 處理批次刪除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning(t("notification.send_page.select_records_first"));
      return;
    }

    try {
      setLoading(true);
      const response = await sendService.batchDeleteSendRecords({
        record_ids: selectedRowKeys,
      });

      if (response.success) {
        message.success(
          t("notification.send_page.batch_delete_success", {
            deleted: response.deleted_count,
            failed: response.failed_count,
          })
        );

        // 如果有失敗的記錄，顯示詳細信息
        if (response.failed_count > 0) {
          message.warning(
            t("notification.send_page.batch_delete_partial_failure", {
              failed_ids: response.failed_ids.join(", "),
            })
          );
        }
      } else {
        message.error(
          response.message || t("notification.send_page.batch_delete_failed")
        );
      }

      // 重新載入數據並清空選擇
      setSelectedRowKeys([]);
      fetchRecords(currentPage, pageSize);
    } catch (error) {
      message.error(t("notification.send_page.batch_delete_failed"));
      console.error("Batch delete failed:", error);
    } finally {
      setLoading(false);
      setIsBatchDeleteModalVisible(false);
    }
  };

  // 處理表格行選擇
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys as string[]);
    },
    onSelectAll: (
      selected: boolean,
      selectedRows: SendRecordResponse[],
      changeRows: SendRecordResponse[]
    ) => {
      if (selected) {
        const newSelectedKeys = [
          ...selectedRowKeys,
          ...changeRows.map((row) => row._id),
        ];
        setSelectedRowKeys(newSelectedKeys);
      } else {
        const changeRowKeys = changeRows.map((row) => row._id);
        setSelectedRowKeys(
          selectedRowKeys.filter((key) => !changeRowKeys.includes(key))
        );
      }
    },
  };

  return (
    <div>
      <Card>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginBottom: 16,
          }}
        >
          <Title level={4}>{t("notification.send_page.title")}</Title>
          <Space>
            {selectedRowKeys.length > 0 && (
              <Button
                danger
                icon={<Icon library="lucide" name="Trash2" size={16} />}
                onClick={() => setIsBatchDeleteModalVisible(true)}
              >
                {t("notification.send_page.batch_delete")} (
                {selectedRowKeys.length})
              </Button>
            )}
            <Button
              type="primary"
              icon={<Icon library="lucide" name="Plus" size={16} />}
              onClick={handleShowModal}
            >
              {t("notification.send_page.create")}
            </Button>
          </Space>
        </div>
        {/* 搜尋區域 */}
        <Form layout="vertical" style={{ marginBottom: 24 }}>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fill, minmax(240px, 1fr))",
              gap: "16px",
              marginBottom: "16px",
            }}
          >
            <Form.Item label={t("notification.send_page.search")}>
              <Input
                placeholder={t("notification.send_page.search")}
                value={keyword}
                onChange={(e) => setKeyword(e.target.value)}
                allowClear
                prefix={<Icon library="lucide" name="Search" size={16} />}
              />
            </Form.Item>
            <Form.Item label={t("notification.send_page.category")}>
              <Select
                placeholder={t("notification.send_page.category")}
                value={selectedCategory}
                onChange={setSelectedCategory}
                allowClear
                style={{ width: "100%" }}
                options={Object.keys(CategoryEnum).map((key) => ({
                  value: key,
                  label: t(`notification.category_types.${key}`),
                }))}
              />
            </Form.Item>
            <Form.Item label={t("notification.send_page.send_type")}>
              <Select
                placeholder={t("notification.send_page.send_type")}
                value={selectedSearchSendType}
                onChange={setSelectedSearchSendType}
                allowClear
                style={{ width: "100%" }}
                options={Object.values(SendTypeEnum).map((key) => ({
                  value: key,
                  label: t(`notification.send_types.${key}`),
                }))}
              />
            </Form.Item>
            <Form.Item label={t("common.date.dateRange")}>
              <RangePicker
                onChange={handleDateRangeChange}
                value={
                  dateRange
                    ? [
                        dateRange[0] ? dayjs(dateRange[0]) : null,
                        dateRange[1] ? dayjs(dateRange[1]) : null,
                      ]
                    : null
                }
                placeholder={[
                  t("common.date.startDate"),
                  t("common.date.endDate"),
                ]}
                style={{ width: "100%" }}
              />
            </Form.Item>
          </div>
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Space>
              <Button
                type="primary"
                onClick={handleSearch}
                icon={<Icon library="lucide" name="Search" size={16} />}
              >
                {t("notification.send_page.search")}
              </Button>
              <Button
                onClick={handleReset}
                icon={<Icon library="lucide" name="RotateCcw" size={16} />}
              >
                {t("common.basic.reset")}
              </Button>
            </Space>
          </div>
        </Form>
        {/* 表格區域 */}
        <Table
          columns={columns}
          dataSource={records}
          rowKey="_id"
          scroll={{ x: "max-content" }}
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
            showTotal: (total) =>
              `${t("common.table.total")} ${total} ${t("common.table.unit")}`,
          }}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("documents.common.emptyData")}
              />
            ),
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* 發送訊息模態框 */}
      <Modal
        title={t("notification.send_page.create")}
        open={isModalVisible}
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            {t("notification.send_page.cancel")}
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={handleSubmit}
          >
            {t("notification.send_page.send")}
          </Button>,
        ]}
        width={700}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="category"
            label={t("notification.send_page.category")}
            rules={[
              {
                required: true,
                message: t("notification.send_page.select_category"),
              },
            ]}
          >
            <Radio.Group>
              {Object.keys(CategoryEnum).map((key) => (
                <Radio key={key} value={key}>
                  {t(`notification.category_types.${key}`)}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
          <Form.Item
            name="send_type"
            label={t("notification.send_page.send_type")}
            rules={[
              {
                required: true,
                message: t("notification.send_page.select_send_type"),
              },
            ]}
          >
            <Radio.Group onChange={(e) => handleSendTypeChange(e.target.value)}>
              {Object.values(SendTypeEnum).map((key) => (
                <Radio key={key} value={key}>
                  {t(`notification.send_types.${key}`)}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
          <Form.Item
            name="recipients"
            label={t("notification.send_page.recipients")}
            rules={[
              {
                required: true,
                message: t("notification.send_page.select_recipients"),
              },
            ]}
          >
            <RecipientSelect
              contactType={
                selectedSendType === SendTypeEnum.EMAIL
                  ? ContactTypeEnum.EMAIL
                  : selectedSendType === SendTypeEnum.LINE
                  ? ContactTypeEnum.LINE
                  : ContactTypeEnum.EMAIL // 預設值
              }
              placeholder={t("notification.send_page.select_recipients")}
              disabled={!selectedSendType}
            />
          </Form.Item>
          <Form.Item
            name="subject"
            label={t("notification.send_page.subject")}
            rules={[
              {
                required: true,
                message: t("notification.send_page.enter_subject"),
              },
            ]}
          >
            <Input placeholder={t("notification.send_page.enter_subject")} />
          </Form.Item>
          {/* 新增的模板和資源按鈕 */}
          <div style={{ marginBottom: 16, display: "flex", gap: 8 }}>
            <Button
              icon={<Icon library="lucide" name="FileText" size={16} />}
              onClick={showTemplateModal}
            >
              {t("notification.send_page.select_template")}
            </Button>
            <Button
              icon={<Icon library="lucide" name="Link" size={16} />}
              onClick={showResourceModal}
            >
              {t("notification.send_page.select_resource")}
            </Button>
          </div>
          {/* 根據發送類型顯示不同的內容編輯器 */}
          <Form.Item
            name="content"
            label={t("notification.send_page.content")}
            rules={[
              {
                required: selectedSendType !== SendTypeEnum.EMAIL,
                message: t("notification.send_page.enter_content"),
              },
            ]}
          >
            {selectedSendType === SendTypeEnum.EMAIL ? (
              <>
                <div className="markdown-editor">
                  <TextArea
                    rows={8}
                    value={markdownContent}
                    onChange={(e) => setMarkdownContent(e.target.value)}
                    placeholder={t(
                      "notification.templates_page.markdown_placeholder"
                    )}
                    style={{ marginBottom: 8 }}
                  />
                  <div style={{ marginBottom: 8, fontSize: 12, color: "#999" }}>
                    {t("notification.templates_page.markdown_tip")}
                  </div>
                  {markdownContent && (
                    <div
                      className="markdown-preview"
                      style={{
                        border: "1px solid #d9d9d9",
                        padding: "8px 16px",
                        borderRadius: 6,
                      }}
                    >
                      <Typography.Title level={5}>
                        {t("notification.send_page.preview")}
                      </Typography.Title>
                      <Markdown>{markdownContent}</Markdown>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <TextArea
                rows={6}
                placeholder={t("notification.send_page.enter_content")}
              />
            )}
          </Form.Item>
          {selectedSendType === SendTypeEnum.LINE && (
            <div style={{ marginTop: -12, fontSize: 12, color: "#999" }}>
              {t("notification.templates_page.markdown_placeholder_line")}
            </div>
          )}
        </Form>
      </Modal>

      {/* 模板選擇模態框 */}
      <TemplateSelect
        visible={isTemplateModalVisible}
        onClose={() => setIsTemplateModalVisible(false)}
        onSelect={handleTemplateSelect}
        channelType={
          selectedSendType === SendTypeEnum.EMAIL
            ? ChannelType.EMAIL
            : selectedSendType === SendTypeEnum.LINE
            ? ChannelType.LINE
            : undefined
        }
      />

      {/* 資源選擇模態框 */}
      <ResourceSelect
        visible={isResourceModalVisible}
        onClose={() => setIsResourceModalVisible(false)}
        onSelect={handleResourceSelect}
      />

      {/* 附件顯示模態框 */}
      <Modal
        title={t("notification.send_page.attachments_list")}
        open={isAttachmentsModalVisible}
        onCancel={handleAttachmentsModalClose}
        footer={[
          <Button key="close" onClick={handleAttachmentsModalClose}>
            {t("notification.send_page.close")}
          </Button>,
        ]}
        width={700}
      >
        <List
          itemLayout="horizontal"
          dataSource={currentAttachments}
          renderItem={(item, index) => (
            <List.Item
              actions={[
                <Button
                  key="view"
                  type="primary"
                  onClick={() => openFile(item.url)}
                >
                  {t("notification.send_page.view")}
                </Button>,
              ]}
            >
              <List.Item.Meta
                avatar={<Icon library="lucide" name="File" size={24} />}
                title={
                  item.originalFile ||
                  `${t("notification.send_page.attachment")} ${index + 1}`
                }
                description={`${item.type} - ${(item.size / 1024).toFixed(
                  2
                )} KB`}
              />
            </List.Item>
          )}
        />
      </Modal>

      {/* 批次刪除確認模態框 */}
      <Modal
        title={t("notification.send_page.batch_delete_confirmation")}
        open={isBatchDeleteModalVisible}
        onCancel={() => setIsBatchDeleteModalVisible(false)}
        footer={[
          <Button
            key="cancel"
            onClick={() => setIsBatchDeleteModalVisible(false)}
          >
            {t("notification.send_page.cancel")}
          </Button>,
          <Button
            key="confirm"
            type="primary"
            danger
            loading={loading}
            onClick={handleBatchDelete}
          >
            {t("notification.send_page.confirm_delete")}
          </Button>,
        ]}
      >
        <p>
          {t("notification.send_page.batch_delete_warning", {
            count: selectedRowKeys.length,
          })}
        </p>
        <p style={{ color: "#ff4d4f", fontSize: "14px" }}>
          {t("notification.send_page.delete_irreversible")}
        </p>
      </Modal>

      {/* 檢視模態框 */}
      <Modal
        title={t("notification.send_page.view_details")}
        open={isViewModalVisible}
        onCancel={handleViewCancel}
        footer={[
          <Button key="close" onClick={handleViewCancel}>
            {t("common.basic.close")}
          </Button>,
        ]}
        width={700}
      >
        {viewRecord && (
          <Descriptions bordered column={1}>
            <Descriptions.Item
              label={t("notification.send_page.customer_name")}
            >
              {viewRecord.customer_name || "-"}
            </Descriptions.Item>
            <Descriptions.Item
              label={t("notification.send_page.customer_tax_id")}
            >
              {viewRecord.customer_tax_id || "-"}
            </Descriptions.Item>
            <Descriptions.Item
              label={t("notification.send_page.customer_number")}
            >
              {viewRecord.customer_number || "-"}
            </Descriptions.Item>
            <Descriptions.Item label={t("notification.send_page.recipients")}>
              {viewRecord.recipient || "-"}
            </Descriptions.Item>
            <Descriptions.Item label={t("notification.send_page.user_name")}>
              {viewRecord.user_name || "-"}
            </Descriptions.Item>
            <Descriptions.Item label={t("notification.send_page.subject")}>
              {viewRecord.title || "-"}
            </Descriptions.Item>
            <Descriptions.Item label={t("notification.send_page.content")}>
              <div
                style={{
                  whiteSpace: "pre-wrap",
                  padding: "10px",
                  border: "1px solid #eee",
                  borderRadius: "4px",
                  backgroundColor: "#fafafa",
                  maxHeight: "300px",
                  overflow: "auto",
                }}
              >
                {viewRecord.content || "-"}
              </div>
            </Descriptions.Item>
            <Descriptions.Item label={t("notification.send_page.category")}>
              {viewRecord.category === CategoryEnum.APPLICATION_REPORT ||
              viewRecord.category === "APPLICATION_REPORT"
                ? t("notification.category_types.APPLICATION_REPORT")
                : viewRecord.category === CategoryEnum.VIDEO_DATA ||
                  viewRecord.category === "VIDEO_DATA"
                ? t("notification.category_types.VIDEO_DATA")
                : viewRecord.category === CategoryEnum.IMPORTANT_MESSAGE ||
                  viewRecord.category === "IMPORTANT_MESSAGE"
                ? t("notification.category_types.IMPORTANT_MESSAGE")
                : viewRecord.category || "-"}
            </Descriptions.Item>
            <Descriptions.Item label={t("notification.send_page.send_type")}>
              {viewRecord.send_type === SendTypeEnum.LINE ||
              viewRecord.send_type === "LINE"
                ? t("notification.send_types.Line")
                : viewRecord.send_type === SendTypeEnum.EMAIL ||
                  viewRecord.send_type === "EMAIL"
                ? t("notification.send_types.Email")
                : viewRecord.send_type || "-"}
            </Descriptions.Item>
            <Descriptions.Item label={t("notification.send_page.sent_time")}>
              {viewRecord.send_time
                ? dayjs(viewRecord.send_time).format("YYYY-MM-DD HH:mm:ss")
                : "-"}
            </Descriptions.Item>
            <Descriptions.Item label={t("notification.send_page.created_at")}>
              {viewRecord.created_at
                ? dayjs(viewRecord.created_at).format("YYYY-MM-DD HH:mm:ss")
                : "-"}
            </Descriptions.Item>
            {viewRecord.updated_at && (
              <Descriptions.Item label={t("notification.send_page.updated_at")}>
                {dayjs(viewRecord.updated_at).format("YYYY-MM-DD HH:mm:ss")}
              </Descriptions.Item>
            )}
            {viewRecord.fileObject && (
              <Descriptions.Item label={t("notification.send_page.file")}>
                <Button
                  type="link"
                  icon={<Icon library="lucide" name="File" size={16} />}
                  onClick={() => openFile(viewRecord.fileObject?.url || "")}
                >
                  {viewRecord.fileObject.originalFile ||
                    t("notification.send_page.view_file")}
                </Button>
              </Descriptions.Item>
            )}
            {viewRecord.additionalFiles &&
              viewRecord.additionalFiles.length > 0 && (
                <Descriptions.Item
                  label={t("notification.send_page.attachments")}
                >
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      gap: "8px",
                    }}
                  >
                    {viewRecord.additionalFiles.map((file, index) => (
                      <Button
                        key={`attachment-${file.url}-${index}`}
                        type="link"
                        icon={
                          <Icon library="lucide" name="Paperclip" size={16} />
                        }
                        onClick={() => openFile(file.url)}
                        style={{ textAlign: "left", padding: 0 }}
                      >
                        {file.originalFile ||
                          `${t("notification.send_page.attachment")} ${
                            index + 1
                          }`}
                      </Button>
                    ))}
                  </div>
                </Descriptions.Item>
              )}
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default SendPage;
