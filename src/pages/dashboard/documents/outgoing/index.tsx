import type {
  CreateOutgoingDocumentRequest,
  OutgoingDocumentResponse,
} from "@/api/models/documents";
import { documentCodeService } from "@/api/services/documentCodeService";
import { documentService } from "@/api/services/documentService";
import { enumService } from "@/api/services/enumService";
import { CustomerSelect, UserSelect } from "@/components/custom-select";
import Icon from "@/components/icon";
import {
  Button,
  Card,
  DatePicker,
  Descriptions,
  Empty,
  Form,
  Input,
  Modal,
  Select,
  Space,
  Table,
  Upload,
  message,
} from "antd";
import type { UploadFile, UploadProps } from "antd";
import Title from "antd/es/typography/Title";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { Helmet } from "react-helmet-async";
import { useTranslation } from "react-i18next";

// 擴展API響應類型，添加可能存在的檔案連結
interface ExtendedOutgoingDocumentResponse extends OutgoingDocumentResponse {
  file_url?: string;
  file_id?: string;
  status?: string;
  handler_id?: string;
}

const OutgoingDocumentPage = () => {
  const { t } = useTranslation();
  const [documents, setDocuments] = useState<
    ExtendedOutgoingDocumentResponse[]
  >([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentDocument, setCurrentDocument] =
    useState<ExtendedOutgoingDocumentResponse | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [viewDocument, setViewDocument] =
    useState<ExtendedOutgoingDocumentResponse | null>(null);
  const [departmentOptions, setDepartmentOptions] = useState<
    Array<{
      value: string;
      label: string;
    }>
  >([]);
  const [documentTypeOptions, setDocumentTypeOptions] = useState<
    Array<{
      value: string;
      label: string;
    }>
  >([]);
  // 獲取文件類型選項
  const fetchDocumentTypeOptions = async () => {
    try {
      const response = await documentCodeService.getAllDocumentCodes({
        limit: 999,
        type: "發文",
      });
      const options = response.items.map((item) => ({
        value: item.name || "",
        label: item.name || "",
      }));
      setDocumentTypeOptions(options);
    } catch (error) {
      console.error("獲取文件類型選項失敗:", error);
    }
  };

  // 獲取所別選項
  const fetchDepartmentOptions = async () => {
    try {
      const response = await enumService.getDepartments();
      const options = response.values.map((value) => ({
        value,
        label: value,
      }));
      setDepartmentOptions(options);
    } catch (error) {
      console.error("獲取所別選項失敗:", error);
    }
  };

  const fetchDocuments = async (params?: any) => {
    try {
      setLoading(true);
      const response =
        await documentService.outgoingDocument.getAllOutgoingDocuments({
          page: currentPage,
          limit: pageSize,
          ...params,
        });
      setDocuments(response.items || []);
      setTotal(response.total_count || 0);
    } catch (error) {
      console.error("獲取發文列表失敗:", error);
      message.error(t("documents.outgoing.fetchFailed"));
    } finally {
      setLoading(false);
    }
  };
  // 只在組件首次載入時獲取部門選項和文件類型選項
  useEffect(() => {
    fetchDepartmentOptions();
    fetchDocumentTypeOptions();
  }, []);

  // 頁碼或頁面大小改變時重新獲取發文檔案
  useEffect(() => {
    fetchDocuments();
  }, [currentPage, pageSize]);

  const handleSearch = async () => {
    const values = await searchForm.validateFields();
    const params: any = {};

    if (values.department) params.department = values.department;
    if (values.document_type) params.document_type = values.document_type;
    if (values.customer_name) params.customer_name = values.customer_name;
    if (values.customer_number) params.customer_number = values.customer_number;
    if (values.document_number) params.document_number = values.document_number;
    if (values.content) params.content = values.content;

    if (values.send_date_start) {
      params.send_date_start = dayjs(values.send_date_start).format(
        "YYYY-MM-DD"
      );
    }
    if (values.send_date_end) {
      params.send_date_end = dayjs(values.send_date_end).format("YYYY-MM-DD");
    }

    setCurrentPage(1);
    fetchDocuments(params);
  };

  const handleReset = () => {
    searchForm.resetFields();
    setCurrentPage(1);
    fetchDocuments();
  };

  const showCreateModal = () => {
    setIsEditing(false);
    setCurrentDocument(null);
    form.resetFields();
    form.setFieldsValue({
      department:
        departmentOptions.length > 0 ? departmentOptions[0].value : "",
    });
    setFileList([]);
    setIsModalVisible(true);
  };

  const showEditModal = (document: ExtendedOutgoingDocumentResponse) => {
    setIsEditing(true);
    console.log("Editing document:", document);
    setCurrentDocument(document);
    setFileList([]);
    setIsModalVisible(true);
    form.setFieldsValue({
      ...document,
      handler_id: document.id || undefined,
      send_date: dayjs(document.send_date),
    });
  };

  const showViewModal = (document: ExtendedOutgoingDocumentResponse) => {
    setViewDocument(document);
    setIsViewModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
    setFileList([]);
  };

  const handleViewCancel = () => {
    setIsViewModalVisible(false);
  };

  const handleDelete = async (documentId: string) => {
    Modal.confirm({
      title: t("common.confirm.confirmDelete"),
      content: t("documents.outgoing.confirmDeleteDesc"),
      onOk: async () => {
        try {
          await documentService.outgoingDocument.deleteOutgoingDocument(
            documentId
          );
          message.success(t("common.result.deleteSuccess"));
          fetchDocuments();
        } catch (error) {
          message.error(t("common.result.deleteFailed"));
        }
      },
    });
  };

  const handleSubmit = async () => {
    try {
      const { document_number, ...values } = await form.validateFields();
      const formattedValues = {
        ...values,
        send_date: dayjs(values.send_date).format("YYYY-MM-DD"),
        updated_by: "current_user",
      };

      setUploading(true); // 開始上傳狀態

      // 準備檔案（如果有的話）
      const file = fileList.length > 0 ? fileList[0].originFileObj : undefined;

      if (isEditing && currentDocument) {
        // 更新發文記錄
        await documentService.outgoingDocument.updateOutgoingDocument(
          currentDocument.id,
          formattedValues,
          file
        );
        message.success(t("common.result.updateSuccess"));
      } else {
        // 創建發文記錄
        await documentService.outgoingDocument.createOutgoingDocument(
          {
            ...formattedValues,
            created_by: "current_user",
          } as CreateOutgoingDocumentRequest,
          file
        );
        message.success(t("common.result.createSuccess"));
      }

      setUploading(false);
      setIsModalVisible(false);
      form.resetFields();
      setFileList([]);
      fetchDocuments();
    } catch (error) {
      setUploading(false);
      message.error(t("common.result.submitFailed"));
    }
  };

  // 上傳檔案相關屬性設定
  const uploadProps: UploadProps = {
    onRemove: () => {
      setFileList([]);
    },
    beforeUpload: (file) => {
      const uploadFile: UploadFile = {
        uid: Date.now().toString(),
        name: file.name,
        size: file.size,
        type: file.type,
        originFileObj: file,
      };
      setFileList([uploadFile]);
      return false;
    },
    fileList,
    maxCount: 1,
  };

  const columns = [
    {
      title: t("documents.outgoing.department"),
      dataIndex: "department",
      key: "department",
      width: "140px",
    },
    {
      title: t("documents.outgoing.date"),
      dataIndex: "send_date",
      key: "send_date",
      width: "180px",
    },
    {
      title: t("documents.outgoing.type"),
      dataIndex: "document_type",
      key: "document_type",
      width: "140px",
    },
    {
      title: t("documents.outgoing.customer"),
      dataIndex: "customer_name",
      key: "customer_name",
      width: "180px",
      render: (text) => text || "",
    },
    {
      title: t("documents.outgoing.document_number"),
      dataIndex: "document_number",
      key: "document_number",
      width: "140px",
    },
    {
      title: t("documents.outgoing.content"),
      dataIndex: "content",
      key: "content",
      width: "200px",
      ellipsis: true,
    },
    {
      title: t("common.basic.actions"),
      key: "action",
      width: "160px",
      render: (_: any, record: ExtendedOutgoingDocumentResponse) => (
        <Space>
          <Button
            type="text"
            icon={<Icon library="lucide" name="Eye" size={16} />}
            onClick={() => showViewModal(record)}
            title={t("common.basic.view")}
          />
          <Button
            type="text"
            icon={<Icon library="lucide" name="Edit" size={16} />}
            onClick={() => showEditModal(record)}
            title={t("common.basic.edit")}
          />
          <Button
            type="text"
            danger
            icon={<Icon library="lucide" name="Trash2" size={16} />}
            onClick={() => handleDelete(record.id)}
            title={t("common.basic.delete")}
          />
          {record.file_url && (
            <Button
              type="text"
              icon={<Icon library="lucide" name="FileText" size={16} />}
              onClick={() => window.open(record.file_url, "_blank")}
              title={t("documents.outgoing.viewFile")}
            />
          )}
        </Space>
      ),
    },
  ];

  return (
    <>
      <Helmet>
        <title>{t("documents.outgoing.title")}</title>
      </Helmet>

      <Card
        title={<Title level={4}>{t("documents.outgoing.title")}</Title>}
        extra={
          <Button
            type="primary"
            icon={<Icon library="lucide" name="Plus" size={16} />}
            onClick={showCreateModal}
          >
            {t("common.basic.add")}
          </Button>
        }
      >
        <Form
          form={searchForm}
          layout="horizontal"
          style={{ marginBottom: 24 }}
        >
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fill, minmax(240px, 1fr))",
              gap: "16px",
              marginBottom: "16px",
            }}
          >
            <Form.Item
              name="send_date_start"
              label={t("documents.outgoing.dateFrom")}
            >
              <DatePicker
                placeholder={t("documents.outgoing.startDate")}
                style={{ width: "100%" }}
              />
            </Form.Item>
            <Form.Item
              name="send_date_end"
              label={t("documents.outgoing.dateTo")}
            >
              <DatePicker
                placeholder={t("documents.outgoing.endDate")}
                style={{ width: "100%" }}
              />
            </Form.Item>
            <Form.Item
              name="department"
              label={t("documents.outgoing.department")}
            >
              <Select
                placeholder={t("documents.outgoing.selectDepartment")}
                options={departmentOptions}
                allowClear
                style={{ width: "100%" }}
              />
            </Form.Item>
            <Form.Item
              name="document_type"
              label={t("documents.outgoing.type")}
            >
              <Select
                placeholder={t("documents.outgoing.selectType")}
                options={documentTypeOptions}
                allowClear
                style={{ width: "100%" }}
              />
            </Form.Item>
            <Form.Item
              name="document_number"
              label={t("documents.outgoing.document_number")}
            >
              <Input
                placeholder={t("documents.outgoing.inputDocNumber")}
                allowClear
              />
            </Form.Item>
            <Form.Item name="customer_name" hidden>
              <Input />
            </Form.Item>
            <Form.Item
              name="customer_number"
              label={t("documents.outgoing.customer")}
              style={{ gridColumn: "span 1" }}
            >
              <CustomerSelect
                fullData={true}
                placeholder={t("documents.outgoing.selectCustomer")}
                valueField="customer_number"
                onChange={(value) => {
                  if (value) {
                    searchForm.setFieldsValue({
                      customer_name: value.customer_name,
                      customer_number: value.customer_number,
                    });
                  } else {
                    searchForm.setFieldsValue({
                      customer_name: "",
                      customer_number: "",
                    });
                  }
                }}
              />
            </Form.Item>
          </div>
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Form.Item style={{ marginRight: 8, marginBottom: 0 }}>
              <Button
                type="primary"
                htmlType="button"
                icon={<Icon library="lucide" name="Search" size={16} />}
                onClick={handleSearch}
              >
                {t("common.basic.search")}
              </Button>
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <Button htmlType="button" onClick={handleReset}>
                {t("common.basic.reset")}
              </Button>
            </Form.Item>
          </div>
        </Form>

        <Table
          columns={columns}
          dataSource={documents}
          rowKey="id"
          scroll={{ x: "max-content" }}
          loading={loading}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("documents.common.emptyData")}
              />
            ),
          }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
            showTotal: (total) =>
              `${t("common.table.total")} ${total} ${t("common.table.unit")}`,
          }}
        />
      </Card>

      {/* 新增/編輯模態窗 */}
      <Modal
        title={
          isEditing ? t("documents.outgoing.edit") : t("documents.outgoing.add")
        }
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        destroyOnClose
        width={600}
        confirmLoading={uploading}
      >
        <Form form={form} layout="horizontal">
          <table style={{ width: "100%", borderCollapse: "collapse" }}>
            <tbody>
              <tr>
                <td style={{ padding: "8px", width: "120px" }}>
                  <span style={{ color: "red" }}>*</span>
                  {t("documents.outgoing.department")}：
                </td>
                <td style={{ padding: "8px" }}>
                  <Form.Item
                    name="department"
                    rules={[
                      {
                        required: true,
                        message: t("documents.outgoing.departmentRequired"),
                      },
                    ]}
                    style={{ margin: 0 }}
                  >
                    <Select
                      placeholder={t("documents.outgoing.selectDepartment")}
                      style={{ width: "100%" }}
                      options={departmentOptions}
                    />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td style={{ padding: "8px" }}>
                  <span style={{ color: "red" }}>*</span>
                  {t("documents.outgoing.date")}：
                </td>
                <td style={{ padding: "8px" }}>
                  <Form.Item name="send_date" style={{ margin: 0 }}>
                    <DatePicker
                      style={{ width: "100%" }}
                      placeholder={t("documents.outgoing.dateRequired")}
                    />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td style={{ padding: "8px" }}>
                  <span style={{ color: "red" }}>*</span>
                  {t("documents.outgoing.type")}：
                </td>
                <td style={{ padding: "8px" }}>
                  <Form.Item
                    name="document_type"
                    rules={[
                      {
                        required: true,
                        message: t("documents.outgoing.typeRequired"),
                      },
                    ]}
                    style={{ margin: 0 }}
                  >
                    <Select
                      placeholder={t("documents.outgoing.selectType")}
                      style={{ width: "100%" }}
                      options={documentTypeOptions}
                    />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td style={{ padding: "8px" }}>
                  <span style={{ color: "red" }}>*</span>
                  {t("documents.outgoing.customer")}：
                </td>
                <td style={{ padding: "8px" }}>
                  <Form.Item name="customer_name" hidden>
                    <Input />
                  </Form.Item>
                  <Form.Item
                    name="customer_number"
                    rules={[
                      {
                        required: true,
                        message: t("documents.outgoing.customerRequired"),
                      },
                    ]}
                    style={{ margin: 0 }}
                  >
                    <CustomerSelect
                      placeholder={t("documents.outgoing.selectCustomer")}
                      valueField="customer_number"
                      fullData={true}
                      onChange={(value) => {
                        if (value) {
                          form.setFieldsValue({
                            customer_number: value.customer_number,
                            customer_name: value.customer_name,
                          });
                        }
                      }}
                    />
                  </Form.Item>
                </td>
              </tr>
              {isEditing && (
                <tr>
                  <td style={{ padding: "8px" }}>
                    {t("documents.outgoing.document_number")}：
                  </td>
                  <td style={{ padding: "8px" }}>
                    <Form.Item name="document_number" style={{ margin: 0 }}>
                      <Input disabled={true} />
                    </Form.Item>
                  </td>
                </tr>
              )}
              <tr>
                <td style={{ padding: "8px", verticalAlign: "top" }}>
                  {t("documents.outgoing.content")}：
                </td>
                <td style={{ padding: "8px" }}>
                  <Form.Item name="content" style={{ margin: 0 }}>
                    <Input.TextArea
                      placeholder={t("documents.outgoing.inputContent")}
                      rows={4}
                    />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td style={{ padding: "8px" }}>
                  {t("documents.outgoing.handler")}：
                </td>
                <td style={{ padding: "8px" }}>
                  <Form.Item name="handler" style={{ margin: 0 }}>
                    <UserSelect
                      placeholder={t("documents.outgoing.selectHandler")}
                      valueField="user_name"
                      onChange={(value) => {
                        if (value) {
                          form.setFieldsValue({
                            handler: value,
                          });
                        }
                      }}
                    />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td style={{ padding: "8px", verticalAlign: "top" }}>
                  {t("documents.outgoing.file")}：
                </td>
                <td style={{ padding: "8px" }}>
                  <Upload {...uploadProps}>
                    <Button
                      icon={<Icon library="lucide" name="Upload" size={16} />}
                    >
                      {t("documents.outgoing.selectFile")}
                    </Button>
                  </Upload>
                </td>
              </tr>
            </tbody>
          </table>
        </Form>
      </Modal>

      {/* 檢視模態窗 */}
      <Modal
        title={t("documents.outgoing.viewDetail")}
        open={isViewModalVisible}
        onCancel={handleViewCancel}
        footer={[
          <Button key="close" onClick={handleViewCancel}>
            {t("common.basic.close")}
          </Button>,
        ]}
        width={600}
      >
        {viewDocument && (
          <Descriptions bordered column={1}>
            <Descriptions.Item label={t("documents.outgoing.department")}>
              {viewDocument.department || ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("documents.outgoing.date")}>
              {viewDocument.send_date || ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("documents.outgoing.type")}>
              {viewDocument.document_type || ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("documents.outgoing.customer")}>
              {viewDocument.customer_name || ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("documents.outgoing.document_number")}>
              {viewDocument.document_number || ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("documents.outgoing.content")}>
              <div
                style={{
                  whiteSpace: "pre-wrap",
                  padding: "10px",
                  border: "1px solid #eee",
                  borderRadius: "4px",
                  backgroundColor: "#fafafa",
                }}
              >
                {viewDocument.content || ""}
              </div>
            </Descriptions.Item>
            <Descriptions.Item label={t("documents.outgoing.handler")}>
              {viewDocument.handler || ""}
            </Descriptions.Item>
            {viewDocument.file_url && (
              <Descriptions.Item label={t("documents.outgoing.file")}>
                <a
                  href={viewDocument.file_url}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {viewDocument.file_url.split("/").pop() ||
                    t("documents.outgoing.downloadFile")}
                </a>
              </Descriptions.Item>
            )}
            <Descriptions.Item label={t("common.record.createdAt")}>
              {viewDocument.created_at
                ? dayjs(viewDocument.created_at).format("YYYY-MM-DD HH:mm")
                : ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("common.record.createdBy")}>
              {viewDocument.created_by || ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("common.record.updatedAt")}>
              {viewDocument.updated_at
                ? dayjs(viewDocument.updated_at).format("YYYY-MM-DD HH:mm")
                : ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("common.record.updatedBy")}>
              {viewDocument.updated_by || ""}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </>
  );
};

export default OutgoingDocumentPage;
