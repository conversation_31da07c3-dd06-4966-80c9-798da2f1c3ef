import type {
  CreateMailRecordRequest,
  MailRecordResponse,
} from "@/api/models/documents";
import { documentService } from "@/api/services/documentService";
import { enumService } from "@/api/services/enumService";
import { CustomerSelect } from "@/components/custom-select";
import Icon from "@/components/icon";
import {
  Button,
  Card,
  DatePicker,
  Empty,
  Form,
  Input,
  Modal,
  Select,
  Space,
  Table,
  message,
} from "antd";
import Title from "antd/es/typography/Title";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { Helmet } from "react-helmet-async";
import { useTranslation } from "react-i18next";

const MailRecordPage = () => {
  const { t } = useTranslation();
  const [mailRecords, setMailRecords] = useState<MailRecordResponse[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<MailRecordResponse | null>(
    null
  );
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [departmentOptions, setDepartmentOptions] = useState<
    Array<{
      value: string;
      label: string;
    }>
  >([]);

  const fetchMailRecords = async (params?: any) => {
    try {
      setLoading(true);
      const response = await documentService.mailRecord.getAllMailRecords({
        page: currentPage,
        limit: pageSize,
        ...params,
      });
      setMailRecords(response.items || []);
      setTotal(response.total_count || 0);
    } catch (error) {
      console.error("獲取郵寄記錄失敗:", error);
      message.error(t("documents.mail.fetchFailed"));
    } finally {
      setLoading(false);
    }
  };

  // 獲取所別選項
  const fetchDepartmentOptions = async () => {
    try {
      const response = await enumService.getDepartments();
      const options = response.values.map((value) => ({
        value,
        label: value,
      }));
      setDepartmentOptions(options);
    } catch (error) {
      console.error("獲取所別選項失敗:", error);
    }
  };

  // 只在組件首次載入時獲取部門選項
  useEffect(() => {
    fetchDepartmentOptions();
  }, []);

  // 頁碼或頁面大小改變時重新獲取郵寄記錄
  useEffect(() => {
    fetchMailRecords();
  }, [currentPage, pageSize]);

  const handleSearch = async () => {
    const values = await searchForm.validateFields();
    const params: any = {};

    if (values.department) params.department = values.department;
    if (values.document_type) params.document_type = values.document_type;
    if (values.customer_name) params.customer_name = values.customer_name;
    if (values.customer_number) params.customer_number = values.customer_number;
    if (values.tracking_number) params.tracking_number = values.tracking_number;
    if (values.content) params.content = values.content;

    if (values.date_start) {
      params.date_start = dayjs(values.date_start).format("YYYY-MM-DD");
    }
    if (values.date_end) {
      params.date_end = dayjs(values.date_end).format("YYYY-MM-DD");
    }

    setCurrentPage(1);
    fetchMailRecords(params);
  };

  const handleReset = () => {
    searchForm.resetFields();
    setCurrentPage(1);
    fetchMailRecords();
  };

  const showCreateModal = () => {
    setIsEditing(false);
    setCurrentRecord(null);
    form.resetFields();
    form.setFieldsValue({
      department:
        departmentOptions.length > 0 ? departmentOptions[0].value : "",
    });
    setIsModalVisible(true);
  };

  const showEditModal = (record: MailRecordResponse) => {
    setIsEditing(true);
    setCurrentRecord(record);
    form.setFieldsValue({
      ...record,
      date: dayjs(record.date),
    });
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleDelete = async (recordId: string) => {
    Modal.confirm({
      title: t("common.confirm.confirmDelete"),
      content: t("documents.mail.confirmDeleteDesc"),
      onOk: async () => {
        try {
          await documentService.mailRecord.deleteMailRecord(recordId);
          message.success(t("common.result.deleteSuccess"));
          fetchMailRecords();
        } catch (error) {
          console.error("刪除郵寄記錄失敗:", error);
          message.error(t("common.result.deleteFailed"));
        }
      },
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const formattedValues = {
        ...values,
        date: dayjs(values.date).format("YYYY-MM-DD"),
        // 這裡需要從全局狀態獲取當前登入的用戶
        updated_by: "current_user",
      };

      if (isEditing && currentRecord) {
        // 更新郵寄記錄
        await documentService.mailRecord.updateMailRecord(
          currentRecord.id,
          formattedValues
        );
        message.success(t("common.result.updateSuccess"));
      } else {
        // 創建郵寄記錄
        await documentService.mailRecord.createMailRecord({
          ...formattedValues,
          created_by: "current_user",
        } as CreateMailRecordRequest);
        message.success(t("common.result.createSuccess"));
      }
      setIsModalVisible(false);
      form.resetFields();
      fetchMailRecords();
    } catch (error) {
      console.error("提交表單失敗:", error);
      message.error(t("common.result.submitFailed"));
    }
  };

  const columns = [
    {
      title: t("documents.mail.department"),
      dataIndex: "department",
      key: "department",
      width: "140px",
    },
    {
      title: t("documents.mail.date"),
      dataIndex: "date",
      key: "date",
      width: "120px",
    },
    {
      title: t("documents.mail.document_type"),
      dataIndex: "document_type",
      key: "document_type",
      width: "120px",
    },
    {
      title: t("documents.mail.customer"),
      dataIndex: "customer_name",
      key: "customer_name",
      width: "160px",
      render: (customer_name?: string) => customer_name || "-",
    },
    {
      title: t("documents.mail.tracking_number"),
      dataIndex: "tracking_number",
      key: "tracking_number",
      width: "160px",
    },
    {
      title: t("documents.mail.content"),
      dataIndex: "content",
      key: "content",
      width: "200px",
      ellipsis: true,
    },
    {
      title: t("common.basic.actions"),
      key: "action",
      width: "120px",
      render: (_: any, record: MailRecordResponse) => (
        <Space>
          <Button
            type="text"
            icon={<Icon library="lucide" name="Edit" size={16} />}
            onClick={() => showEditModal(record)}
            title={t("common.basic.edit")}
          />
          <Button
            type="text"
            danger
            icon={<Icon library="lucide" name="Trash2" size={16} />}
            onClick={() => handleDelete(record.id)}
            title={t("common.basic.delete")}
          />
        </Space>
      ),
    },
  ];

  // 文件類型選項
  const documentTypeOptions = [
    { value: "一般掛號", label: t("documents.mail.typeRegular") },
    { value: "限時掛號", label: t("documents.mail.typeLimited") },
    { value: "限時批號", label: t("documents.mail.typeLimitedBatch") },
    { value: "普通批號", label: t("documents.mail.typeNormal") },
  ];

  return (
    <>
      <Helmet>
        <title>{t("documents.mail.title")}</title>
      </Helmet>

      <Card
        title={<Title level={4}>{t("documents.mail.title")}</Title>}
        extra={
          <Button
            type="primary"
            icon={<Icon library="lucide" name="Plus" size={16} />}
            onClick={showCreateModal}
          >
            {t("common.basic.add")}
          </Button>
        }
      >
        <Form
          form={searchForm}
          layout="horizontal"
          style={{ marginBottom: 24 }}
        >
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fill, minmax(240px, 1fr))",
              gap: "16px",
              marginBottom: "16px",
            }}
          >
            <Form.Item name="date_start" label={t("documents.mail.dateFrom")}>
              <DatePicker
                placeholder={t("documents.mail.startDate")}
                style={{ width: "100%" }}
              />
            </Form.Item>
            <Form.Item name="date_end" label={t("documents.mail.dateTo")}>
              <DatePicker
                placeholder={t("documents.mail.endDate")}
                style={{ width: "100%" }}
              />
            </Form.Item>
            <Form.Item name="department" label={t("documents.mail.department")}>
              <Select
                placeholder={t("documents.mail.selectDepartment")}
                options={departmentOptions}
                allowClear
                style={{ width: "100%" }}
              />
            </Form.Item>
            <Form.Item
              name="document_type"
              label={t("documents.mail.document_type")}
            >
              <Select
                placeholder={t("documents.mail.selectType")}
                options={documentTypeOptions}
                allowClear
                style={{ width: "100%" }}
              />
            </Form.Item>
            <Form.Item name="customer_name" hidden>
              <Input />
            </Form.Item>
            <Form.Item
              name="customer_number"
              label={t("documents.mail.customer")}
            >
              <CustomerSelect
                fullData={true}
                valueField="customer_number"
                placeholder={t("documents.mail.selectCustomer")}
                onChange={(value) => {
                  if (value) {
                    searchForm.setFieldsValue({
                      customer_name: value.customer_name,
                      customer_number: value.customer_number,
                    });
                  } else {
                    searchForm.setFieldsValue({
                      customer_name: "",
                      customer_number: "",
                    });
                  }
                }}
              />
            </Form.Item>
            <Form.Item
              name="tracking_number"
              label={t("documents.mail.tracking_number")}
            >
              <Input
                placeholder={t("documents.mail.inputTrackingNumber")}
                allowClear
              />
            </Form.Item>
          </div>
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Form.Item style={{ marginRight: 8, marginBottom: 0 }}>
              <Button
                type="primary"
                htmlType="button"
                icon={<Icon library="lucide" name="Search" size={16} />}
                onClick={handleSearch}
              >
                {t("common.basic.search")}
              </Button>
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <Button htmlType="button" onClick={handleReset}>
                {t("common.basic.reset")}
              </Button>
            </Form.Item>
          </div>
        </Form>

        <Table
          columns={columns}
          dataSource={mailRecords}
          rowKey="id"
          scroll={{ x: "max-content" }}
          loading={loading}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("documents.common.emptyData")}
              />
            ),
          }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
            showTotal: (total) =>
              `${t("common.table.total")} ${total} ${t("common.table.unit")}`,
          }}
        />
      </Card>

      <Modal
        title={isEditing ? t("documents.mail.edit") : t("documents.mail.add")}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        destroyOnClose
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="department"
            label={t("documents.mail.department")}
            rules={[
              {
                required: true,
                message: t("documents.mail.departmentRequired"),
              },
            ]}
          >
            <Select
              placeholder={t("documents.mail.selectDepartment")}
              options={departmentOptions}
            />
          </Form.Item>
          <Form.Item
            name="date"
            label={t("documents.mail.date")}
            rules={[
              { required: true, message: t("documents.mail.dateRequired") },
            ]}
          >
            <DatePicker
              placeholder={t("common.prompt.selectDate")}
              style={{ width: "100%" }}
            />
          </Form.Item>
          <Form.Item
            name="document_type"
            label={t("documents.mail.document_type")}
            rules={[
              { required: true, message: t("documents.mail.typeRequired") },
            ]}
          >
            <Select
              placeholder={t("documents.mail.selectType")}
              options={documentTypeOptions}
            />
          </Form.Item>
          <Form.Item name="customer_name" hidden>
            <Input />
          </Form.Item>
          <Form.Item
            name="customer_number"
            label={t("documents.mail.customer")}
          >
            <CustomerSelect
              fullData={true}
              valueField="customer_number"
              placeholder={t("documents.mail.selectCustomerOptional")}
              onChange={(value) => {
                if (value) {
                  form.setFieldsValue({
                    customer_name: value.customer_name,
                    customer_number: value.customer_number,
                  });
                }
              }}
            />
          </Form.Item>
          <Form.Item
            name="tracking_number"
            label={t("documents.mail.tracking_number")}
            rules={[
              {
                required: true,
                message: t("documents.mail.trackingNumberRequired"),
              },
            ]}
          >
            <Input placeholder={t("documents.mail.inputTrackingNumber")} />
          </Form.Item>
          <Form.Item
            name="content"
            label={t("documents.mail.content")}
            rules={[
              { required: true, message: t("documents.mail.contentRequired") },
            ]}
          >
            <Input.TextArea
              placeholder={t("documents.mail.inputContent")}
              rows={4}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default MailRecordPage;
