import type { RolePermissionItem, RoleResponse } from "@/api/models";
import { permissionService, roleService } from "@/api/services";
import Icon from "@/components/icon";
import {
  Button,
  Card,
  Checkbox,
  Empty,
  Form,
  Select,
  Space,
  Table,
  Tooltip,
  Typography,
  message,
} from "antd";
import type React from "react";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

const { Title } = Typography;

/**
 * 權限管理頁面
 */
const PermissionsPage: React.FC = () => {
  const { t } = useTranslation();
  const [roles, setRoles] = useState<RoleResponse[]>([]);
  const [modules, setModules] = useState<RolePermissionItem[]>([]);
  const [permissions, setPermissions] = useState<RolePermissionItem[]>([]);
  const [selectedRoleId, setSelectedRoleId] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [form] = Form.useForm();
  const [initialPermissions, setInitialPermissions] = useState<
    Record<string, string[]>
  >({});

  // 獲取角色列表
  const fetchRoles = async () => {
    try {
      const response = await roleService.getAllRoles();
      setRoles(response.items || []);
      if (response.items?.length > 0) {
        setSelectedRoleId(response.items[0].id);
      }
    } catch (error) {
      message.error(t("permissions.fetchFailed"));
      console.error(error);
    }
  };

  // 獲取權限列表
  const fetchPermissions = async (roleId: string) => {
    if (!roleId) return;

    setLoading(true);
    try {
      const response = await permissionService.getRolePermissions(roleId);
      setPermissions(response || []);
      setModules(response || []);

      // 設置 form 初始值
      const initialValues: Record<string, string[]> = {};
      response.forEach((item) => {
        initialValues[item.module_code] = item.actions;
      });

      // 保存初始權限設定以便之後比較
      setInitialPermissions(initialValues);
      form.setFieldsValue(initialValues);
    } catch (error) {
      message.error(t("permissions.fetchFailed"));
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 處理角色變更
  const handleRoleChange = (value: string) => {
    setSelectedRoleId(value);
    fetchPermissions(value);
  };

  // 處理權限更新
  const handlePermissionUpdate = async (
    permissionId: string,
    actions: string[]
  ) => {
    if (!selectedRoleId) {
      message.warning(t("permissions.roleRequired"));
      return;
    }

    try {
      await permissionService.updatePermission(permissionId, { actions });
      message.success(t("permissions.updateSuccess"));
      fetchPermissions(selectedRoleId);
    } catch (error) {
      message.error(t("permissions.updateFailed"));
      console.error(error);
    }
  };

  // 批量儲存權限
  const handleSubmit = async (values: Record<string, string[]>) => {
    if (!selectedRoleId) {
      message.warning(t("permissions.roleRequired"));
      return;
    }

    try {
      setLoading(true);

      // 找出有變更的權限項目
      const changedPermissions = permissions.filter((permission) => {
        const moduleCode = permission.module_code;
        const currentActions = values[moduleCode] || [];
        const initialActions = initialPermissions[moduleCode] || [];

        // 檢查權限是否有變更
        if (currentActions.length !== initialActions.length) {
          return true;
        }

        // 檢查權限內容是否有變更
        return (
          !currentActions.every((action) => initialActions.includes(action)) ||
          !initialActions.every((action) => currentActions.includes(action))
        );
      });

      // 只更新有變更的權限
      const permissionUpdates = changedPermissions
        .filter((permission) => permission.id) // 確保有 ID
        .map((permission) => ({
          permission_id: permission.id || "",
          actions: values[permission.module_code] || [],
        }));

      // 如果沒有變更，直接返回
      if (permissionUpdates.length === 0) {
        message.info(t("permissions.noChanges"));
        setLoading(false);
        return;
      }

      await permissionService.batchUpdatePermissions({
        permission_updates: permissionUpdates,
      });

      message.success(t("permissions.updateSuccess"));
      fetchPermissions(selectedRoleId);
    } catch (error) {
      message.error(t("permissions.updateFailed"));
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRoles();
  }, []);

  useEffect(() => {
    if (selectedRoleId) {
      fetchPermissions(selectedRoleId);
    }
  }, [selectedRoleId]);

  // 定義每個模組可用的操作
  // 在實際應用中，這應該來自後端API
  const getModuleAvailableActions = (moduleCode: string): string[] => {
    // 預設操作
    const defaultActions = ["create", "view", "update", "delete"];

    // 特定模組的自定義操作
    const moduleSpecificActions: Record<string, string[]> = {
      // 這些是示例，應該根據實際需求調整
      user: [...defaultActions, "export", "import"],
      role: [...defaultActions, "export"],
      customer: [...defaultActions, "export", "import", "print"],
      case: [...defaultActions, "export", "print", "approve", "reject"],
      finance: [...defaultActions, "export", "approve", "reject"],
      document: [...defaultActions, "download", "upload", "print"],
      bulletin: [...defaultActions, "publish", "unpublish"],
    };

    // 返回特定模組的操作，如果沒有定義則返回預設操作
    return moduleSpecificActions[moduleCode] || defaultActions;
  };

  // 表格列
  const columns = [
    {
      title: t("permissions.module"),
      dataIndex: "module_name",
      key: "module_name",
      width: 200,
    },
    {
      title: t("permissions.moduleCode"),
      dataIndex: "module_code",
      key: "module_code",
      width: 150,
      render: (_: string, record: RolePermissionItem) => {
        // 當parent_id為null時，不顯示模組代碼
        if (record.parent_id === null || record.parent_id === undefined) {
          return "";
        }
        // 否則顯示模組代碼，如果為空則顯示破折號
        return record.module_code || "";
      },
    },
    {
      title: t("permissions.permission"),
      dataIndex: "actions",
      key: "actions",
      render: (_: any, record: RolePermissionItem) => {
        // 如果是父模組 (parent_id 為 null 或未定義)，則不顯示權限設定選項
        if (record.parent_id === null || record.parent_id === undefined) {
          return <span></span>;
        }

        // 獲取該模組可用的操作
        const availableActions = getModuleAvailableActions(record.module_code);

        if (!availableActions || availableActions.length === 0) {
          return <span>{t("permissions.noAvailableActions")}</span>;
        }

        return (
          <Form.Item name={record.module_code} noStyle>
            <Checkbox.Group>
              {availableActions.map((action) => (
                <Tooltip key={action} title={getActionName(action)}>
                  <Checkbox value={action}>{getActionName(action)}</Checkbox>
                </Tooltip>
              ))}
            </Checkbox.Group>
          </Form.Item>
        );
      },
    },
  ];

  // 獲取動作中文名稱
  const getActionName = (action: string) => {
    return t(`permissions.actions.${action}`) || action;
  };

  return (
    <div style={{ padding: "24px" }}>
      <Card>
        <Space direction="vertical" style={{ width: "100%" }} size="large">
          <Space>
            <Title level={4} style={{ margin: 0 }}>
              {t("permissions.title")}
            </Title>
            <Select
              placeholder={t("permissions.selectRole")}
              style={{ width: 200 }}
              value={selectedRoleId}
              onChange={handleRoleChange}
              options={roles.map((role) => ({
                label: role.role_name,
                value: role.id,
              }))}
            />
          </Space>

          <Form form={form} onFinish={handleSubmit}>
            <Table
              rowKey="module_code"
              columns={columns}
              dataSource={modules}
              scroll={{ x: "max-content" }}
              pagination={false}
              loading={loading}
              locale={{
                emptyText: (
                  <Empty
                    image={<Icon library="lucide" name="ArchiveX" size={40} />}
                    description={t("documents.common.emptyData")}
                  />
                ),
              }}
            />

            <div style={{ marginTop: "24px", textAlign: "right" }}>
              <Button type="primary" htmlType="submit" loading={loading}>
                {t("permissions.savePermissions")}
              </Button>
            </div>
          </Form>
        </Space>
      </Card>
    </div>
  );
};

export default PermissionsPage;
