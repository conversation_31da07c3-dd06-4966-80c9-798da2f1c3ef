import { SettingType } from "@/api/models/enum";
import type {
  SystemSettingResponse,
  UpdateSystemSettingRequest,
} from "@/api/models/system-setttings";
import { enumService, systemSettingService } from "@/api/services";
import Icon from "@/components/icon";
import { useUserInfo } from "@/store/userStore";
import {
  Button,
  Card,
  Empty,
  Form,
  Input,
  Modal,
  Select,
  Space,
  Switch,
  Table,
  Tag,
  message,
} from "antd";
import Title from "antd/es/typography/Title";
import { useEffect, useState } from "react";
import { Helmet } from "react-helmet-async";
import { useTranslation } from "react-i18next";

const SystemSettingsPage = () => {
  const { t } = useTranslation();
  const userInfo = useUserInfo();
  const [settings, setSettings] = useState<SystemSettingResponse[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentSetting, setCurrentSetting] =
    useState<SystemSettingResponse | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [departmentOptions, setDepartmentOptions] = useState<
    Array<{
      value: string;
      label: string;
    }>
  >([]);

  // 獲取所別選項
  const fetchDepartmentOptions = async () => {
    try {
      const response = await enumService.getDepartments();
      const options = response.values.map((value) => ({
        value,
        label: value,
      }));
      setDepartmentOptions(options);
    } catch (error) {
      console.error("獲取所別選項失敗:", error);
    }
  };
  const fetchSettings = async (params?: any, isRetry = false) => {
    try {
      setLoading(true);
      const response = await systemSettingService.getSystemSettings({
        page: currentPage,
        limit: pageSize,
        ...params,
      });

      // 如果列表為空且不是重試請求，則嘗試初始化部門設定
      if (
        (response.settings || []).length === 0 &&
        response.total === 0 &&
        !isRetry
      ) {
        try {
          // 使用當前登入用戶的部門資訊進行初始化
          const initParams = userInfo.department
            ? { department: userInfo.department }
            : {}; // 如果用戶沒有部門資訊，則初始化所有預設部門

          await systemSettingService.initializeDepartmentSettings(initParams);
          message.success(t("systemSettings.messages.initializeSuccess"));

          // 重新獲取列表
          const retryResponse = await systemSettingService.getSystemSettings({
            page: currentPage,
            limit: pageSize,
            ...params,
          });
          setSettings(retryResponse.settings || []);
          setTotal(retryResponse.total || 0);
        } catch (initError) {
          console.error("初始化部門設定失敗:", initError);
          message.error(t("systemSettings.messages.initializeFailed"));
          // 即使初始化失敗，也要設置空列表
          setSettings(response.settings || []);
          setTotal(response.total || 0);
        }
      } else {
        setSettings(response.settings || []);
        setTotal(response.total || 0);
      }
    } catch (error) {
      console.error("獲取系統設定列表失敗:", error);
      message.error(t("common.result.operationFailed"));
    } finally {
      setLoading(false);
    }
  };

  // 只在組件首次載入時獲取部門選項
  useEffect(() => {
    fetchDepartmentOptions();
  }, []);

  useEffect(() => {
    fetchSettings();
  }, [currentPage, pageSize]);

  const handleSearch = async () => {
    const values = await searchForm.validateFields();
    // 將搜索表單的字段名映射回後端需要的字段名
    const params = {
      department: values.search_department,
      setting_type: values.search_setting_type,
      setting_key: values.search_setting_key,
      is_active: values.search_is_active,
    };
    setCurrentPage(1);
    fetchSettings(params);
  };
  const handleReset = () => {
    searchForm.resetFields();
    setCurrentPage(1);
    fetchSettings();
  };

  const showEditModal = (setting: SystemSettingResponse) => {
    setIsEditing(true);
    setCurrentSetting(setting);
    form.setFieldsValue({
      department: setting.department,
      setting_key: setting.setting_key,
      setting_value: setting.setting_value,
      setting_type: setting.setting_type,
      description: setting.description,
      is_active: setting.is_active,
    });
    setIsModalVisible(true);
  };
  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (isEditing && currentSetting) {
        // 更新系統設定
        const updateData: UpdateSystemSettingRequest = {
          setting_value: values.setting_value,
          setting_type: values.setting_type,
          description: values.description,
          is_active: values.is_active,
        };

        await systemSettingService.updateSystemSetting(
          currentSetting._id,
          updateData
        );
        message.success(t("common.result.updateSuccess"));
      }
      setIsModalVisible(false);
      form.resetFields();
      fetchSettings();
    } catch (error) {
      console.error("提交表單失敗:", error);
      message.error(t("common.result.updateFailed"));
    }
  };

  // 設定類型選項
  const settingTypeOptions = [
    { value: SettingType.ZOOM, label: t("systemSettings.settingTypes.zoom") },
    { value: SettingType.LINE, label: t("systemSettings.settingTypes.line") },
    { value: SettingType.SMTP, label: t("systemSettings.settingTypes.smtp") },
    {
      value: SettingType.SYSTEM,
      label: t("systemSettings.settingTypes.system"),
    },
  ];

  const columns = [
    {
      title: t("systemSettings.fields.department"),
      dataIndex: "department",
      key: "department",
    },
    {
      title: t("systemSettings.fields.settingKey"),
      dataIndex: "setting_key",
      key: "setting_key",
    },
    {
      title: t("systemSettings.fields.settingValue"),
      dataIndex: "setting_value",
      key: "setting_value",
      width: "300px",
      render: (value: string, record: SystemSettingResponse) => {
        return (
          <p className="break-all">
            {value.length > 200
              ? `<p className="break-all">${value.substring(0, 200)}...</p>`
              : value}
          </p>
        );
      },
    },
    {
      title: t("systemSettings.fields.settingType"),
      dataIndex: "setting_type",
      key: "setting_type",
      render: (type: SettingType) => {
        const colorMap = {
          [SettingType.ZOOM]: "blue",
          [SettingType.LINE]: "green",
          [SettingType.SMTP]: "orange",
          [SettingType.SYSTEM]: "purple",
        };
        return (
          <Tag color={colorMap[type]}>
            {t(`systemSettings.settingTypes.${type}`)}
          </Tag>
        );
      },
    },
    {
      title: t("systemSettings.fields.isActive"),
      dataIndex: "is_active",
      key: "is_active",
      render: (active: boolean) => (
        <Tag color={active ? "success" : "default"}>
          {active
            ? t("systemSettings.status.active")
            : t("systemSettings.status.inactive")}
        </Tag>
      ),
    },
    {
      title: t("common.basic.actions"),
      key: "action",
      render: (_: any, record: SystemSettingResponse) => (
        <Space>
          <Button
            type="text"
            icon={<Icon library="lucide" name="Edit" size={16} />}
            onClick={() => showEditModal(record)}
            title={t("common.basic.edit")}
          />
        </Space>
      ),
    },
  ];

  return (
    <>
      <Helmet>
        <title>{t("systemSettings.title")}</title>
      </Helmet>{" "}
      <Card title={<Title level={4}>{t("systemSettings.title")}</Title>}>
        <Form
          form={searchForm}
          layout="horizontal"
          style={{ marginBottom: 24 }}
          name="systemSettingsSearchForm"
        >
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fill, minmax(240px, 1fr))",
              gap: "16px",
              marginBottom: "16px",
            }}
          >
            <Form.Item
              name="search_department"
              label={t("systemSettings.fields.department")}
            >
              <Select
                placeholder={t("systemSettings.placeholders.selectDepartment")}
                options={departmentOptions}
                allowClear
                style={{ width: "100%" }}
              />
            </Form.Item>
            <Form.Item
              name="search_setting_type"
              label={t("systemSettings.fields.settingType")}
            >
              <Select
                placeholder={t("systemSettings.placeholders.selectSettingType")}
                options={settingTypeOptions}
                allowClear
                style={{ width: "100%" }}
              />
            </Form.Item>
            <Form.Item
              name="search_setting_key"
              label={t("systemSettings.fields.settingKey")}
            >
              <Input
                placeholder={t("systemSettings.placeholders.enterSettingKey")}
                allowClear
              />
            </Form.Item>
            <Form.Item
              name="search_is_active"
              label={t("systemSettings.fields.isActive")}
            >
              <Select
                placeholder={t("common.form.select")}
                allowClear
                style={{ width: "100%" }}
              >
                <Select.Option value={true}>
                  {t("systemSettings.status.active")}
                </Select.Option>
                <Select.Option value={false}>
                  {t("systemSettings.status.inactive")}
                </Select.Option>
              </Select>
            </Form.Item>
          </div>
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Form.Item style={{ marginRight: 8, marginBottom: 0 }}>
              <Button
                type="primary"
                htmlType="button"
                icon={<Icon library="lucide" name="Search" size={16} />}
                onClick={handleSearch}
              >
                {t("systemSettings.search")}
              </Button>
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <Button htmlType="button" onClick={handleReset}>
                {t("systemSettings.reset")}
              </Button>
            </Form.Item>
          </div>
        </Form>
        <Table
          columns={columns}
          dataSource={settings}
          rowKey="_id"
          scroll={{ x: "max-content" }}
          loading={loading}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("documents.common.emptyData")}
              />
            ),
          }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
            showTotal: (total) =>
              `${t("common.table.total")} ${total} ${t("common.table.unit")}`,
          }}
        />
      </Card>
      <Modal
        title={t("systemSettings.edit")}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        destroyOnClose
        forceRender
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          name="systemSettingModalForm"
          preserve={false}
        >
          <Form.Item
            name="department"
            label={t("systemSettings.fields.department")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <Select
              placeholder={t("systemSettings.placeholders.selectDepartment")}
              options={departmentOptions}
              disabled={isEditing}
              style={{ width: "100%" }}
            />
          </Form.Item>
          <Form.Item
            name="setting_key"
            label={t("systemSettings.fields.settingKey")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <Input
              placeholder={t("systemSettings.placeholders.enterSettingKey")}
              disabled={isEditing}
            />
          </Form.Item>
          <Form.Item
            name="setting_value"
            label={t("systemSettings.fields.settingValue")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <Input.TextArea
              placeholder={t("systemSettings.placeholders.enterSettingValue")}
              rows={3}
            />
          </Form.Item>
          <Form.Item
            name="setting_type"
            label={t("systemSettings.fields.settingType")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <Select
              placeholder={t("systemSettings.placeholders.selectSettingType")}
              options={settingTypeOptions}
            />
          </Form.Item>
          <Form.Item
            name="description"
            label={t("systemSettings.fields.description")}
          >
            <Input.TextArea
              placeholder={t("systemSettings.placeholders.enterDescription")}
              rows={2}
            />
          </Form.Item>
          <Form.Item
            name="is_active"
            label={t("systemSettings.fields.isActive")}
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default SystemSettingsPage;
