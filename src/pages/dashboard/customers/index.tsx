import type {
  CustomerCreate,
  CustomerRead,
  CustomerUpdate,
} from "@/api/models/customer";
import { enumService } from "@/api/services";
import { customerService } from "@/api/services/customerService";
import Icon from "@/components/icon";
import {
  Button,
  Card,
  Descriptions,
  Empty,
  Form,
  Input,
  Modal,
  Select,
  Space,
  Table,
  message,
} from "antd";
import Title from "antd/es/typography/Title";
import { useEffect, useState } from "react";
import { Helmet } from "react-helmet-async";
import { useTranslation } from "react-i18next";

const CustomersPage = () => {
  const { t } = useTranslation();
  const [customers, setCustomers] = useState<CustomerRead[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [currentCustomer, setCurrentCustomer] = useState<CustomerRead | null>(
    null
  );
  const [viewCustomer, setViewCustomer] = useState<CustomerRead | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [departmentOptions, setDepartmentOptions] = useState<
    Array<{
      value: string;
      label: string;
    }>
  >([]);

  // 獲取所別選項
  const fetchDepartmentOptions = async () => {
    try {
      const response = await enumService.getDepartments();
      const options = response.values.map((value) => ({
        value,
        label: value,
      }));
      setDepartmentOptions(options);
    } catch (error) {
      console.error("獲取所別選項失敗:", error);
    }
  };
  const fetchCustomers = async (params?: any) => {
    try {
      setLoading(true);
      const response = await customerService.getAllCustomers({
        page: currentPage,
        limit: pageSize,
        ...params,
      });
      setCustomers(response.items || []);
      setTotal(response.total || 0);
    } catch (error) {
      console.error("獲取客戶列表失敗:", error);
      message.error(t("common.result.operationFailed"));
    } finally {
      setLoading(false);
    }
  };

  // 只在組件首次載入時獲取部門選項
  useEffect(() => {
    fetchDepartmentOptions();
  }, []);

  useEffect(() => {
    fetchCustomers();
  }, [currentPage, pageSize]);
  const handleSearch = async () => {
    try {
      const values = await searchForm.validateFields();
      const params = {
        search: values.search,
        department: values.department,
      };
      setCurrentPage(1);
      fetchCustomers(params);
    } catch (error) {
      // 驗證失敗時不執行搜索
      console.log("搜索表單驗證失敗:", error);
    }
  };

  const handleReset = () => {
    searchForm.resetFields();
    setCurrentPage(1);
    fetchCustomers();
  };
  const showCreateModal = () => {
    setIsEditing(false);
    setCurrentCustomer(null);
    form.resetFields();
    // 預設選擇第一個部門選項
    form.setFieldsValue({
      department:
        departmentOptions.length > 0 ? departmentOptions[0].value : "",
    });
    setIsModalVisible(true);
  };

  const showViewModal = (customer: CustomerRead) => {
    setViewCustomer(customer);
    setIsViewModalVisible(true);
  };

  const showEditModal = (customer: CustomerRead) => {
    setIsEditing(true);
    setCurrentCustomer(customer);
    form.setFieldsValue({
      customer_number: customer.customer_number,
      customer_name: customer.customer_name,
      customer_alias: customer.customer_alias,
      attention_name: customer.attention_name,
      customer_tax_id: customer.customer_tax_id,
      mail_number: customer.mail_number,
      address: customer.address,
      telephone: customer.telephone,
      fax: customer.fax,
      department: customer.department,
    });
    setIsModalVisible(true);
  };
  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleViewCancel = () => {
    setIsViewModalVisible(false);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (isEditing && currentCustomer) {
        // 更新客戶
        const updateData: CustomerUpdate = {
          customer_number: values.customer_number,
          customer_name: values.customer_name,
          customer_alias: values.customer_alias || null,
          attention_name: values.attention_name || null,
          customer_tax_id: values.customer_tax_id || null,
          mail_number: values.mail_number || null,
          address: values.address || null,
          telephone: values.telephone || null,
          fax: values.fax || null,
          department: values.department || null,
        };

        await customerService.updateCustomer(currentCustomer.uuid, updateData);
        message.success(t("common.result.updateSuccess"));
      } else {
        // 創建客戶
        const createData: CustomerCreate = {
          customer_number: values.customer_number,
          customer_name: values.customer_name,
          customer_alias: values.customer_alias || null,
          attention_name: values.attention_name || null,
          customer_tax_id: values.customer_tax_id || null,
          mail_number: values.mail_number || null,
          address: values.address || null,
          telephone: values.telephone || null,
          fax: values.fax || null,
          department: values.department || null,
        };

        await customerService.createCustomer(createData);
        message.success(t("common.result.createSuccess"));
      }
      setIsModalVisible(false);
      form.resetFields();
      fetchCustomers();
    } catch (error) {
      console.error("提交表單失敗:", error.response.data.detail);
      if (error.response.data.detail) {
        message.error(error.response.data.detail);
      } else {
        message.error(
          isEditing
            ? t("common.result.updateFailed")
            : t("common.result.createFailed")
        );
      }
    }
  };
  const columns = [
    {
      title: t("customer.customer_number"),
      dataIndex: "customer_number",
      key: "customer_number",
    },
    {
      title: t("customer.customer_name"),
      dataIndex: "customer_name",
      key: "customer_name",
    },
    {
      title: t("customer.attention_name"),
      dataIndex: "attention_name",
      key: "attention_name",
    },
    {
      title: t("customer.customer_tax_id"),
      dataIndex: "customer_tax_id",
      key: "customer_tax_id",
    },
    {
      title: t("customer.department"),
      dataIndex: "department",
      key: "department",
      render: (department: string) => department || "",
    },
    {
      title: t("common.basic.actions"),
      key: "action",
      render: (_: any, record: CustomerRead) => (
        <Space>
          <Button
            type="text"
            icon={<Icon library="lucide" name="Eye" size={16} />}
            onClick={() => showViewModal(record)}
            title={t("common.basic.view")}
          />
          <Button
            type="text"
            icon={<Icon library="lucide" name="Edit" size={16} />}
            onClick={() => showEditModal(record)}
            title={t("common.basic.edit")}
          />
        </Space>
      ),
    },
  ];

  return (
    <>
      <Helmet>
        <title>{t("customer.title")}</title>
      </Helmet>

      <Card
        title={<Title level={4}>{t("customer.title")}</Title>}
        extra={
          <Button
            type="primary"
            icon={<Icon library="lucide" name="Plus" size={16} />}
            onClick={showCreateModal}
          >
            {t("common.basic.add")}
          </Button>
        }
      >
        <Form
          form={searchForm}
          layout="horizontal"
          style={{ marginBottom: 24 }}
          name="searchForm"
        >
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fill, minmax(300px, 1fr))",
              gap: "16px",
              marginBottom: "16px",
            }}
          >
            <Form.Item name="search" label={t("common.basic.search")}>
              <Input
                placeholder={t("customer.searchPlaceholder")}
                allowClear
                onPressEnter={handleSearch}
              />
            </Form.Item>
            <Form.Item name="department" label={t("customer.department")}>
              <Select
                placeholder={t("customer.selectDepartment")}
                options={departmentOptions}
                allowClear
                style={{ width: "100%" }}
              />
            </Form.Item>
          </div>
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Form.Item style={{ marginRight: 8, marginBottom: 0 }}>
              <Button
                type="primary"
                htmlType="button"
                icon={<Icon library="lucide" name="Search" size={16} />}
                onClick={handleSearch}
              >
                {t("common.basic.search")}
              </Button>
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <Button htmlType="button" onClick={handleReset}>
                {t("common.basic.reset")}
              </Button>
            </Form.Item>
          </div>
        </Form>

        <Table
          columns={columns}
          dataSource={customers}
          rowKey="uuid"
          scroll={{ x: "max-content" }}
          loading={loading}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("documents.common.emptyData")}
              />
            ),
          }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
            showTotal: (total) =>
              `${t("common.table.total")} ${total} ${t("common.table.unit")}`,
          }}
        />
      </Card>

      <Modal
        title={isEditing ? t("customer.edit") : t("customer.add")}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        destroyOnClose
        forceRender
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          name="customerForm"
          id="customerForm"
          preserve={false}
        >
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              gap: "16px",
            }}
          >
            <Form.Item
              name="customer_number"
              label={t("customer.customer_number")}
              rules={[
                {
                  required: true,
                  message: t("customer.customerNumberRequired"),
                },
              ]}
            >
              <Input
                placeholder={t("customer.input_customer_number")}
                disabled={isEditing}
              />
            </Form.Item>
            <Form.Item
              name="customer_name"
              label={t("customer.customer_name")}
              rules={[
                { required: true, message: t("customer.customerNameRequired") },
              ]}
            >
              <Input placeholder={t("customer.input_customer_name")} />
            </Form.Item>
          </div>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              gap: "16px",
            }}
          >
            <Form.Item
              name="customer_alias"
              label={t("customer.customer_alias")}
            >
              <Input placeholder={t("customer.input_customer_alias")} />
            </Form.Item>
            <Form.Item
              name="attention_name"
              label={t("customer.attention_name")}
            >
              <Input placeholder={t("customer.input_attention_name")} />
            </Form.Item>
          </div>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              gap: "16px",
            }}
          >
            <Form.Item
              name="customer_tax_id"
              label={t("customer.customer_tax_id")}
            >
              <Input placeholder={t("customer.input_customer_tax_id")} />
            </Form.Item>
            <Form.Item name="mail_number" label={t("customer.mail_number")}>
              <Input placeholder={t("customer.input_mail_number")} />
            </Form.Item>
          </div>
          <Form.Item name="address" label={t("customer.address")}>
            <Input placeholder={t("customer.input_address")} />
          </Form.Item>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              gap: "16px",
            }}
          >
            <Form.Item name="telephone" label={t("customer.telephone")}>
              <Input placeholder={t("customer.input_telephone")} />
            </Form.Item>
            <Form.Item name="fax" label={t("customer.fax")}>
              <Input placeholder={t("customer.input_fax")} />
            </Form.Item>
          </div>
          <Form.Item name="department" label={t("customer.department")}>
            <Select
              placeholder={t("customer.selectDepartment")}
              options={departmentOptions}
              allowClear
              style={{ width: "100%" }}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 檢視模態窗 */}
      <Modal
        title={t("common.basic.view")}
        open={isViewModalVisible}
        onCancel={handleViewCancel}
        footer={[
          <Button key="close" onClick={handleViewCancel}>
            {t("common.basic.close")}
          </Button>,
        ]}
        width={600}
      >
        {viewCustomer && (
          <Descriptions bordered column={1}>
            <Descriptions.Item label={t("customer.customer_number")}>
              {viewCustomer.customer_number}
            </Descriptions.Item>
            <Descriptions.Item label={t("customer.customer_name")}>
              {viewCustomer.customer_name}
            </Descriptions.Item>
            <Descriptions.Item label={t("customer.customer_alias")}>
              {viewCustomer.customer_alias || ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("customer.attention_name")}>
              {viewCustomer.attention_name || ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("customer.customer_tax_id")}>
              {viewCustomer.customer_tax_id || ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("customer.mail_number")}>
              {viewCustomer.mail_number || ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("customer.address")}>
              {viewCustomer.address || ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("customer.telephone")}>
              {viewCustomer.telephone || ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("customer.fax")}>
              {viewCustomer.fax || ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("customer.department")}>
              {viewCustomer.department || ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("customer.created_at")}>
              {viewCustomer.created_at
                ? new Date(viewCustomer.created_at).toLocaleString()
                : ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("customer.updated_at")}>
              {viewCustomer.updated_at
                ? new Date(viewCustomer.updated_at).toLocaleString()
                : ""}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </>
  );
};

export default CustomersPage;
