import { CollectionEnum } from "@/api/models/files"; // 引入檔案集合類型
import { ResourceType } from "@/api/models/resources";
import type {
  CreateResourceRequest,
  ResourceResponse,
  UpdateResourceRequest,
} from "@/api/models/resources";
import { fileService } from "@/api/services/fileService"; // 引入檔案服務
import { resourcesService } from "@/api/services/resourcesService";
import Icon from "@/components/icon";
import { Upload } from "@/components/upload"; // 引入上傳元件
import {
  Button,
  Card,
  DatePicker,
  Empty,
  Form,
  Input,
  Modal,
  Radio,
  Select,
  Space,
  Table,
  message,
} from "antd";
import Title from "antd/es/typography/Title";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { Helmet } from "react-helmet-async";
import { useTranslation } from "react-i18next";

// 擴展ResourceResponse類型來匹配實際API返回數據
interface ExtendedResourceResponse extends Omit<ResourceResponse, "_id"> {
  id: string; // 前端可能使用id而非_id
  _id: string; // 保留原始的_id
}

// API返回的響應類型
interface ApiResponse {
  resources: ExtendedResourceResponse[];
  total: number;
  page: number;
  limit: number;
}

// 擴展請求類型，用於前端表單
interface FormResourceData {
  title: string;
  resource_type: ResourceType;
  resource_group?: string;
  media_date?: string;
  url?: string;
  file?: File; // 新增上傳檔案欄位
}

const ResourcesPage = () => {
  const { t } = useTranslation();
  const [resources, setResources] = useState<ExtendedResourceResponse[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentResource, setCurrentResource] =
    useState<ExtendedResourceResponse | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();

  const resourceTypeOptions = [
    {
      value: ResourceType.REPORT,
      label: t("notification.resources_page.resource_types.REPORT"),
    },
    {
      value: ResourceType.VIDEO,
      label: t("notification.resources_page.resource_types.VIDEO"),
    },
  ];

  const resourceGroupOptions = [
    {
      value: "NEWS",
      label: t("notification.resources_page.resource_groups.NEWS"),
    },
    {
      value: "ANNOUNCEMENT",
      label: t("notification.resources_page.resource_groups.ANNOUNCEMENT"),
    },
    {
      value: "GUIDE",
      label: t("notification.resources_page.resource_groups.GUIDE"),
    },
  ];

  const fetchResources = async (params?: any) => {
    try {
      setLoading(true);
      const response = await resourcesService.getAllResources({
        page: currentPage,
        limit: pageSize,
        ...params,
      });

      // 使用斷言來兼容API返回結構
      const apiResponse = response as unknown as ApiResponse;

      // 轉換返回的數據為前端需要的格式
      const resources =
        apiResponse.resources?.map((resource) => ({
          ...resource,
          id: resource._id, // 確保資源有id屬性
        })) || [];

      setResources(resources);
      setTotal(apiResponse.total || 0);
    } catch (error) {
      console.error(t("notification.resources_page.fetch_failed"), error);
      message.error(t("common.result.operationFailed"));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchResources();
  }, [currentPage, pageSize]);

  const handleSearch = async () => {
    const values = await searchForm.validateFields();
    const params: Record<string, any> = {};

    if (values.search_title) {
      params.title = values.search_title;
    }

    if (values.search_resource_type) {
      params.resource_type = values.search_resource_type;
    }

    if (values.search_resource_group) {
      params.resource_group = values.search_resource_group;
    }

    if (values.search_media_date && values.search_media_date.length === 2) {
      params.media_date_from = values.search_media_date[0].format("YYYY-MM-DD");
      params.media_date_to = values.search_media_date[1].format("YYYY-MM-DD");
    }

    setCurrentPage(1);
    fetchResources(params);
  };

  const handleReset = () => {
    searchForm.resetFields();
    setCurrentPage(1);
    fetchResources();
  };

  const showCreateModal = () => {
    setIsEditing(false);
    setCurrentResource(null);
    form.resetFields();
    setIsModalVisible(true);
  };
  const showEditModal = (resource: ExtendedResourceResponse) => {
    setIsEditing(true);
    setCurrentResource(resource);
    form.setFieldsValue({
      title: resource.title,
      resource_type: resource.resource_type,
      resource_group: resource.resource_group,
      media_date: resource.media_date ? dayjs(resource.media_date) : null,
      linkType: "url", // 預設為URL模式
      url: resource.url,
    });
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleDelete = async (resourceId: string) => {
    Modal.confirm({
      title: t("common.confirm.confirmDelete"),
      content: t("common.confirmDelete"),
      okText: t("common.basic.confirm"),
      cancelText: t("common.basic.cancel"),
      onOk: async () => {
        try {
          await resourcesService.deleteResource(resourceId);
          message.success(t("common.result.deleteSuccess"));
          fetchResources();
        } catch (error) {
          message.error(t("common.result.deleteFailed"));
        }
      },
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const formattedValues = {
        ...values,
        media_date: values.media_date
          ? values.media_date.format("YYYY-MM-DD")
          : null,
      }; // 檢查使用者選擇的是URL還是檔案上傳
      let finalUrl = formattedValues.url;

      // 如果選擇了檔案上傳模式，且有上傳檔案
      if (
        values.linkType === "file" &&
        values.file &&
        Array.isArray(values.file) &&
        values.file.length > 0
      ) {
        try {
          const messageKey = "uploadLoading";
          message.loading({
            content: t("common.result.uploading"),
            key: messageKey,
            duration: 0,
          });

          const file = values.file[0].originFileObj;
          const uploadResult = await fileService.uploadFile(
            file,
            undefined,
            CollectionEnum.RESOURCES
          );

          // 如果上傳成功，使用返回的downloadUrl
          if (uploadResult?.data?.downloadUrl) {
            finalUrl = `${import.meta.env.VITE_APP_BASE_API}${
              uploadResult.data.downloadUrl.split("/api")[1]
            }`;

            message.success({
              content: t("common.result.uploadSuccess"),
              key: messageKey,
            });
          }
        } catch (uploadError) {
          message.error(t("common.result.uploadFailed"));
          return; // 如果檔案上傳失敗，中止後續操作
        }
      }

      if (isEditing && currentResource) {
        // 更新資源，只提取API模型支持的欄位
        const updateData: UpdateResourceRequest = {
          title: formattedValues.title,
          resource_type: formattedValues.resource_type,
          resource_group: formattedValues.resource_group,
          media_date: formattedValues.media_date,
          url: finalUrl, // 使用處理後的URL
        };

        await resourcesService.updateResource(currentResource.id, updateData);
        message.success(t("common.result.updateSuccess"));
      } else {
        // 創建資源，只提取API模型支持的欄位
        const createData: CreateResourceRequest = {
          title: formattedValues.title,
          resource_type: formattedValues.resource_type,
          resource_group: formattedValues.resource_group,
          media_date: formattedValues.media_date,
          url: finalUrl, // 使用處理後的URL
        };

        await resourcesService.createResource(createData);
        message.success(t("common.result.createSuccess"));
      }
      setIsModalVisible(false);
      form.resetFields();
      fetchResources();
    } catch (error) {
      message.error(
        isEditing
          ? t("common.result.updateFailed")
          : t("common.result.createFailed")
      );
    }
  };

  const columns = [
    {
      title: t("notification.resources_page.title_field"),
      dataIndex: "title",
      key: "title",
    },
    {
      title: t("notification.resources_page.resource_type"),
      dataIndex: "resource_type",
      key: "resource_type",
      render: (type: ResourceType) => {
        const typeOption = resourceTypeOptions.find(
          (option) => option.value === type
        );
        return typeOption ? typeOption.label : type;
      },
    },
    {
      title: t("notification.resources_page.resource_group"),
      dataIndex: "resource_group",
      key: "resource_group",
      render: (group: string) => {
        const groupOption = resourceGroupOptions.find(
          (option) => option.value === group
        );
        return groupOption ? groupOption.label : group;
      },
    },
    {
      title: t("notification.resources_page.media_date"),
      dataIndex: "media_date",
      key: "media_date",
      render: (date: string) => (date ? dayjs(date).format("YYYY-MM-DD") : ""),
    },
    {
      title: t("notification.resources_page.created_by"),
      dataIndex: "created_by",
      key: "created_by",
    },
    {
      title: t("notification.resources_page.created_at"),
      dataIndex: "created_at",
      key: "created_at",
      render: (date: string) =>
        date ? dayjs(date).format("YYYY-MM-DD HH:mm") : "",
    },
    {
      title: t("common.basic.actions"),
      key: "action",
      render: (_: any, record: ExtendedResourceResponse) => (
        <Space size="middle">
          <Button
            type="text"
            onClick={() => showEditModal(record)}
            icon={<Icon library="lucide" name="Edit" size={16} />}
            title={t("common.basic.edit")}
          />
          <Button
            type="text"
            danger
            onClick={() => handleDelete(record.id)}
            icon={<Icon library="lucide" name="Trash2" size={16} />}
            title={t("common.basic.delete")}
          />
        </Space>
      ),
    },
  ];

  return (
    <>
      <Helmet>
        <title>{t("notification.resources_page.title")} - Admin</title>
      </Helmet>
      <Card
        title={
          <Title level={4}>{t("notification.resources_page.title")}</Title>
        }
        bordered={false}
        extra={
          <Button
            type="primary"
            icon={<Icon library="lucide" name="Plus" size={16} />}
            onClick={showCreateModal}
          >
            {t("notification.resources_page.create")}
          </Button>
        }
      >
        <Form
          form={searchForm}
          layout="horizontal"
          style={{ marginBottom: 24 }}
          name="resourceSearchForm"
        >
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fill, minmax(240px, 1fr))",
              gap: "16px",
              marginBottom: "16px",
            }}
          >
            <Form.Item
              name="search_title"
              label={t("notification.resources_page.title_field")}
            >
              <Input
                placeholder={t("notification.resources_page.search")}
                allowClear
              />
            </Form.Item>
            <Form.Item
              name="search_resource_type"
              label={t("notification.resources_page.resource_type")}
            >
              <Select
                placeholder={t(
                  "notification.resources_page.select_resource_type"
                )}
                allowClear
                options={resourceTypeOptions}
              />
            </Form.Item>
            <Form.Item
              name="search_resource_group"
              label={t("notification.resources_page.resource_group")}
            >
              <Select
                placeholder={t(
                  "notification.resources_page.select_resource_group"
                )}
                allowClear
                options={resourceGroupOptions}
              />
            </Form.Item>
            <Form.Item
              name="search_media_date"
              label={t("notification.resources_page.media_date")}
            >
              <DatePicker.RangePicker
                style={{ width: "100%" }}
                placeholder={[
                  t("notification.resources_page.select_date"),
                  t("notification.resources_page.select_date"),
                ]}
              />
            </Form.Item>
          </div>
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Form.Item style={{ marginRight: 8, marginBottom: 0 }}>
              <Button
                type="primary"
                htmlType="button"
                icon={<Icon library="lucide" name="Search" size={16} />}
                onClick={handleSearch}
              >
                {t("common.basic.search")}
              </Button>
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <Button htmlType="button" onClick={handleReset}>
                {t("common.basic.reset")}
              </Button>
            </Form.Item>
          </div>
        </Form>

        <Table
          columns={columns}
          dataSource={resources}
          rowKey="id"
          scroll={{ x: "max-content" }}
          loading={loading}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("common.table.emptyText")}
              />
            ),
          }}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
            showTotal: (total) =>
              `${t("common.table.total")} ${total} ${t("common.table.unit")}`,
          }}
        />
      </Card>

      <Modal
        title={
          isEditing
            ? t("notification.resources_page.edit")
            : t("notification.resources_page.create")
        }
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        width={650}
        okText={t("common.basic.confirm")}
        cancelText={t("common.basic.cancel")}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{ resource_type: ResourceType.REPORT }}
        >
          <Form.Item
            name="title"
            label={t("notification.resources_page.title_field")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <Input placeholder={t("notification.resources_page.enter_title")} />
          </Form.Item>
          <Form.Item
            name="resource_type"
            label={t("notification.resources_page.resource_type")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <Select
              placeholder={t(
                "notification.resources_page.select_resource_type"
              )}
              options={resourceTypeOptions}
            />
          </Form.Item>
          <Form.Item
            name="resource_group"
            label={t("notification.resources_page.resource_group")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <Select
              placeholder={t(
                "notification.resources_page.select_resource_group"
              )}
              options={resourceGroupOptions}
            />
          </Form.Item>
          <Form.Item
            name="media_date"
            label={t("notification.resources_page.media_date")}
          >
            <DatePicker
              placeholder={t("notification.resources_page.select_date")}
              style={{ width: "100%" }}
            />
          </Form.Item>
          <Form.Item
            name="linkType"
            label={t("notification.resources_page.link_type")}
            initialValue="url"
          >
            <Radio.Group>
              <Radio value="url">
                {t("notification.resources_page.external_url")}
              </Radio>
              <Radio value="file">
                {t("notification.resources_page.upload_file")}
              </Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.linkType !== currentValues.linkType
            }
          >
            {({ getFieldValue }) =>
              getFieldValue("linkType") === "url" ? (
                <Form.Item
                  name="url"
                  label={t("notification.resources_page.url")}
                >
                  <Input
                    placeholder={t("notification.resources_page.enter_url")}
                  />
                </Form.Item>
              ) : (
                <Form.Item
                  name="file"
                  label={t("notification.resources_page.file")}
                  valuePropName="fileList"
                  getValueFromEvent={(e) => {
                    if (Array.isArray(e)) {
                      return e;
                    }
                    return e?.fileList;
                  }}
                >
                  <Upload name="file" maxCount={1} multiple={false} />
                </Form.Item>
              )
            }
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default ResourcesPage;
