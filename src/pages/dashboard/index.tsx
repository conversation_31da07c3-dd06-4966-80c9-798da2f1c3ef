import {
  <PERSON>,
  Typo<PERSON>,
  Row,
  Col,
  Stati<PERSON>,
  Spin,
  message,
  DatePicker,
  Button,
  Space,
} from "antd";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "recharts";
import { Wrapper, Status } from "@googlemaps/react-wrapper";
import type React from "react";
import { useEffect, useRef, useState } from "react";
import dayjs from "dayjs";
import { dashboardService } from "@/api/services/dashboardService";
import type {
  TripHotspot,
  UserActivityResponse,
  UserDeviceDistributionResponse,
} from "@/api/models/dashboard";

const { Title, Paragraph } = Typography;
const { RangePicker } = DatePicker;

// Google Map 組件
const MapComponent: React.FC<{ hotspots: TripHotspot[] }> = ({ hotspots }) => {
  const ref = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map>();
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);

  useEffect(() => {
    if (ref.current && !map) {
      // 固定中心點在台中
      const center = { lat: 24.1477, lng: 120.6736 }; // 台中車站
      const zoom = 12;

      const newMap = new window.google.maps.Map(ref.current, {
        center,
        zoom,
      });
      setMap(newMap);
    }
  }, [ref, map]);

  useEffect(() => {
    if (map) {
      // 清除現有標記
      markers.forEach((marker) => marker.setMap(null));
      setMarkers([]);

      if (hotspots.length > 0) {
        // 創建新標記
        const newMarkers: google.maps.Marker[] = [];

        hotspots.forEach((hotspot, index) => {
          const marker = new window.google.maps.Marker({
            position: { lat: hotspot.latitude, lng: hotspot.longitude },
            map: map,
            title: `接單熱點 #${index + 1}\n緯度: ${hotspot.latitude}\n經度: ${
              hotspot.longitude
            }`,
            icon: {
              path: window.google.maps.SymbolPath.CIRCLE,
              scale: 12,
              fillColor: "#ff4444",
              fillOpacity: 0.9,
              strokeColor: "#ffffff",
              strokeWeight: 3,
            },
          });

          newMarkers.push(marker);
        });

        setMarkers(newMarkers);
      }
    }
  }, [map, hotspots]);

  return <div ref={ref} style={{ width: "100%", height: "400px" }} />;
};

const render = (status: Status) => {
  if (status === Status.LOADING) return <div>載入地圖中...</div>;
  if (status === Status.FAILURE) return <div>地圖載入失敗</div>;
  return null;
};

const DashboardPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [mapLoading, setMapLoading] = useState(false);
  const [userActivity, setUserActivity] = useState<UserActivityResponse | null>(
    null
  );
  const [deviceDistribution, setDeviceDistribution] =
    useState<UserDeviceDistributionResponse | null>(null);
  const [tripHotspots, setTripHotspots] = useState<TripHotspot[]>([]);
  const [totalTripCount, setTotalTripCount] = useState(0);

  // 獲取本月初和本月底的預設日期
  const getDefaultDateRange = (): [dayjs.Dayjs, dayjs.Dayjs] => {
    const now = dayjs();
    const startOfMonth = now.startOf("month");
    const endOfMonth = now.endOf("month");
    return [startOfMonth, endOfMonth];
  };
  
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(getDefaultDateRange());
  const [hasConfirmed, setHasConfirmed] = useState(false);


  // Colors for pie chart
  const COLORS = ["#52c41a", "#ff4d4f"];

  const fetchBasicDashboardData = async () => {
    try {
      setLoading(true);

      const [activityRes, deviceRes] = await Promise.all([
        dashboardService.getUserActivity(),
        dashboardService.getUserDeviceDistribution(),
      ]);

      setUserActivity(activityRes);
      setDeviceDistribution(deviceRes);
    } catch (error) {
      console.error("獲取儀表板數據失敗:", error);
      message.error("獲取儀表板數據失敗");
    } finally {
      setLoading(false);
    }
  };

  const fetchHotspotsData = async (startDate: string, endDate: string) => {
    try {
      setMapLoading(true);

      const hotspotsRes = await dashboardService.getTripHotspots({
        startDate,
        endDate,
      });

      setTripHotspots(hotspotsRes.hotspots);
      setTotalTripCount(hotspotsRes.total_trip_count);
    } catch (error) {
      console.error("獲取熱點數據失敗:", error);
      message.error("獲取熱點數據失敗");
    } finally {
      setMapLoading(false);
    }
  };

  const handleDateRangeChange = (dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
    setDateRange(dates);
  };

  const handleConfirm = () => {
    if (dateRange) {
      setHasConfirmed(true);
      const [startDate, endDate] = dateRange;
      fetchHotspotsData(
        startDate.startOf("day").toISOString(),
        endDate.endOf("day").toISOString()
      );
    } else {
      message.warning('請選擇有效的日期範圍');
    }
  };

  useEffect(() => {
    fetchBasicDashboardData();
  }, []);

  // Format data for charts
  const deviceData = deviceDistribution
    ? [
        { name: "Android", users: deviceDistribution.android_user_count },
        { name: "iOS", users: deviceDistribution.ios_user_count },
      ]
    : [];

  const activityData = userActivity
    ? [
        { name: "活躍用戶", value: userActivity.active_users_count },
        { name: "非活躍用戶", value: userActivity.inactive_users_count },
      ]
    : [];

  if (loading) {
    return (
      <div style={{ padding: "24px", textAlign: "center" }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>載入儀表板數據中...</div>
      </div>
    );
  }

  return (
    <div style={{ padding: "24px" }}>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>Dashboard</Title>
      </div>

      {/* Charts Row */}
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="設備分佈" bordered={false}>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={deviceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="users" fill="#1890ff" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="用戶活躍狀態" bordered={false}>
            <div style={{ position: "relative", height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={activityData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) =>
                      `${name}: ${(percent * 100).toFixed(0)}%`
                    }
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {activityData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
              <div
                style={{
                  position: "absolute",
                  bottom: 16,
                  right: 16,
                  textAlign: "right",
                }}
              >
                <Statistic
                  title="總用戶數"
                  value={userActivity?.total_users || 0}
                  valueStyle={{ color: "#3f8600" }}
                />
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Map Section */}
      <Row gutter={[24, 24]}>
        <Col xs={24}>
          <Card
            title={
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  flexWrap: "wrap",
                  gap: "16px",
                }}
              >
                <span>接單熱區</span>
                <Space>
                  <RangePicker
                    value={dateRange}
                    onChange={handleDateRangeChange}
                    placeholder={["開始日期", "結束日期"]}
                    allowClear
                  />
                  <Button onClick={handleConfirm} type="primary">
                    確認
                  </Button>
                </Space>
              </div>
            }
            bordered={false}
          >
            {!hasConfirmed ? (
              <div
                style={{
                  height: "400px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  color: "#999",
                  fontSize: "16px",
                }}
              >
                請選擇日期範圍並點擊「確認」以查看接單熱區數據
              </div>
            ) : mapLoading ? (
              <div
                style={{
                  height: "400px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Spin size="large" />
                <div style={{ marginLeft: 16 }}>載入熱區數據中...</div>
              </div>
            ) : (
              <Wrapper
                apiKey="AIzaSyCBeRKRae6YchyF3kRsciz7cQ5uiEiEpls"
                render={render}
                libraries={["marker"]}
              >
                <MapComponent hotspots={tripHotspots} />
              </Wrapper>
            )}

            {/* Trip Count Statistics */}
            {hasConfirmed && !mapLoading && (
              <div
                style={{
                  marginTop: 16,
                  padding: "16px 0",
                  borderTop: "1px solid #f0f0f0",
                }}
              >
                <Row>
                  <Col span={24}>
                    <Statistic
                      title="總行程數"
                      value={totalTripCount}
                      valueStyle={{ color: "#1890ff" }}
                      style={{ textAlign: "center" }}
                    />
                  </Col>
                </Row>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DashboardPage;
