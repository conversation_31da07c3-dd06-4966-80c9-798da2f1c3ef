import Icon from "@/components/icon";
import {
  <PERSON><PERSON>,
  Card,
  DatePicker,
  Descriptions,
  Empty,
  Form,
  Input,
  Modal,
  Select,
  Space,
  Table,
  Typography,
  message,
} from "antd";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { Helmet } from "react-helmet-async";
import { useTranslation } from "react-i18next";

import { MeetingRequestStatus } from "@/api/models/meeting";
import type {
  AdminApproveMeetingRequest,
  AdminCancelMeetingRequest,
  ListMeetingRequestsParams,
  MeetingRejectionRequest,
  MeetingRequestResponse,
} from "@/api/models/meeting";
import { meetingService } from "@/api/services/meetingService";
import type { TableProps } from "antd";

const { Title, Text } = Typography;
const { TextArea } = Input;

const MeetingPage = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [meetings, setMeetings] = useState<MeetingRequestResponse[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  // 搜尋相關狀態
  const [searchForm] = Form.useForm();
  const [selectedStatus, setSelectedStatus] = useState<
    MeetingRequestStatus | undefined
  >(undefined);

  // 模態框相關狀態
  const [isApproveModalVisible, setIsApproveModalVisible] = useState(false);
  const [isRejectModalVisible, setIsRejectModalVisible] = useState(false);
  const [isCancelModalVisible, setIsCancelModalVisible] = useState(false);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [currentMeeting, setCurrentMeeting] =
    useState<MeetingRequestResponse | null>(null);

  // 表單
  const [approveForm] = Form.useForm();
  const [rejectForm] = Form.useForm();
  const [cancelForm] = Form.useForm();

  // 獲取會議列表
  const fetchMeetings = async (params?: ListMeetingRequestsParams) => {
    try {
      setLoading(true);
      const response = await meetingService.listMeetingRequests({
        page: currentPage,
        limit: pageSize,
        status: selectedStatus,
        ...params,
      });
      setMeetings(response.data || []);
      setTotal(response.total || 0);
    } catch (error) {
      console.error("獲取會議列表失敗:", error);
      message.error(t("notification.meeting_page.fetch_failed"));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMeetings();
  }, [currentPage, pageSize]);

  // 處理搜尋
  const handleSearch = () => {
    setCurrentPage(1);
    fetchMeetings({
      page: 1,
      limit: pageSize,
      status: selectedStatus,
    });
  };
  // 重置搜尋
  const handleReset = () => {
    setSelectedStatus(undefined);
    setCurrentPage(1);
    searchForm.resetFields();
    fetchMeetings({ page: 1, limit: pageSize });
  };
  // 處理表格分頁變更
  const handleTableChange: TableProps<MeetingRequestResponse>["onChange"] = (
    pagination
  ) => {
    setCurrentPage(pagination.current || 1);
    setPageSize(pagination.pageSize || 10);
  };
  // 顯示核准模態框
  const showApproveModal = (meeting: MeetingRequestResponse) => {
    Modal.confirm({
      title: t("notification.meeting_page.confirm_approve"),
      content: `確定要核准 "${meeting.subject}" 的會議申請嗎？`,
      okText: t("common.basic.confirm"),
      cancelText: t("common.basic.cancel"),
      onOk: () => {
        setCurrentMeeting(meeting);
        setIsApproveModalVisible(true);
        approveForm.resetFields();
      },
    });
  };
  // 顯示拒絕模態框
  const showRejectModal = (meeting: MeetingRequestResponse) => {
    Modal.confirm({
      title: t("notification.meeting_page.confirm_reject"),
      content: `確定要拒絕 "${meeting.subject}" 的會議申請嗎？`,
      okText: t("common.basic.confirm"),
      cancelText: t("common.basic.cancel"),
      onOk: () => {
        setCurrentMeeting(meeting);
        setIsRejectModalVisible(true);
        rejectForm.resetFields();
      },
    });
  }; // 顯示取消模態框
  const showCancelModal = (meeting: MeetingRequestResponse) => {
    Modal.confirm({
      title: t("common.confirm.confirmOperation"),
      content: `確定要取消 "${meeting.subject}" 的會議嗎？`,
      okText: t("common.basic.confirm"),
      cancelText: t("common.basic.cancel"),
      onOk: () => {
        setCurrentMeeting(meeting);
        setIsCancelModalVisible(true);
        cancelForm.resetFields();
      },
    });
  };

  // 顯示詳情模態框
  const showViewModal = (meeting: MeetingRequestResponse) => {
    setCurrentMeeting(meeting);
    setIsViewModalVisible(true);
  };

  // 核准會議
  const handleApproveMeeting = async () => {
    if (!currentMeeting) return;

    try {
      const values = await approveForm.validateFields();
      const data: AdminApproveMeetingRequest = {
        meeting_time: values.meeting_time.toISOString(),
        duration: values.duration || 60,
        message: values.message,
      };

      await meetingService.approveMeeting(currentMeeting.id, data);
      message.success(t("notification.meeting_page.approve_success"));
      setIsApproveModalVisible(false);
      fetchMeetings();
    } catch (error) {
      console.error("核准會議失敗:", error);
      message.error(t("notification.meeting_page.approve_failed"));
    }
  };

  // 拒絕會議
  const handleRejectMeeting = async () => {
    if (!currentMeeting) return;

    try {
      const values = await rejectForm.validateFields();
      const data: MeetingRejectionRequest = {
        reason: values.reason,
      };

      await meetingService.rejectMeeting(currentMeeting.id, data);
      message.success(t("notification.meeting_page.reject_success"));
      setIsRejectModalVisible(false);
      fetchMeetings();
    } catch (error) {
      console.error("拒絕會議失敗:", error);
      message.error(t("notification.meeting_page.reject_failed"));
    }
  };

  // 取消會議
  const handleCancelMeeting = async () => {
    if (!currentMeeting) return;

    try {
      const values = await cancelForm.validateFields();
      const data: AdminCancelMeetingRequest = {
        reason: values.reason,
      };

      await meetingService.cancelMeeting(currentMeeting.id, data);
      message.success(t("common.result.operationSuccess"));
      setIsCancelModalVisible(false);
      fetchMeetings();
    } catch (error) {
      console.error("取消會議失敗:", error);
      message.error(t("common.result.operationFailed"));
    }
  };

  // 刪除會議
  const handleDeleteMeeting = async (meetingId: string) => {
    Modal.confirm({
      title: t("common.confirm.confirmDelete"),
      content: t("notification.meeting_page.confirm_delete"),
      okText: t("common.basic.confirm"),
      cancelText: t("common.basic.cancel"),
      onOk: async () => {
        try {
          await meetingService.deleteMeeting(meetingId);
          message.success(t("notification.meeting_page.delete_success"));
          fetchMeetings();
        } catch (error) {
          console.error("刪除會議失敗:", error);
          message.error(t("notification.meeting_page.delete_failed"));
        }
      },
    });
  };

  // 表格列定義
  const columns: TableProps<MeetingRequestResponse>["columns"] = [
    {
      title: t("notification.meeting_page.subject"),
      dataIndex: "subject",
      key: "subject",
      width: 140,
      ellipsis: true,
    },
    {
      title: t("notification.meeting_page.requester_email"),
      dataIndex: "requester_email",
      key: "requester_email",
      width: 180,
      ellipsis: true,
    },
    {
      title: t("notification.meeting_page.attendees_emails"),
      dataIndex: "attendees_emails",
      key: "attendees_emails",
      width: 180,
      ellipsis: true,
      render: (emails: string[]) => emails?.join(", ") || "",
    },
    {
      title: t("notification.meeting_page.meeting_time"),
      dataIndex: "meeting_time",
      key: "meeting_time",
      width: 180,
      render: (time: string) =>
        time ? dayjs(time).format("YYYY-MM-DD HH:mm") : "",
    },
    {
      title: t("notification.meeting_page.status"),
      dataIndex: "status",
      key: "status",
      render: (status: MeetingRequestStatus) => {
        const statusMap = {
          [MeetingRequestStatus.PENDING]: {
            color: "orange",
            text: t("notification.meeting_status.pending"),
          },
          [MeetingRequestStatus.APPROVED]: {
            color: "blue",
            text: t("notification.meeting_status.approved"),
          },
          [MeetingRequestStatus.SCHEDULED]: {
            color: "green",
            text: t("notification.meeting_status.scheduled"),
          },
          [MeetingRequestStatus.REJECTED]: {
            color: "red",
            text: t("notification.meeting_status.rejected"),
          },
          [MeetingRequestStatus.CANCELLED]: {
            color: "gray",
            text: t("notification.meeting_status.cancelled"),
          },
          [MeetingRequestStatus.CREATION_FAILED]: {
            color: "red",
            text: t("notification.meeting_status.creation_failed"),
          },
        };

        const statusInfo = statusMap[status] || {
          color: "gray",
          text: status,
        };

        return (
          <span style={{ color: statusInfo.color }}>{statusInfo.text}</span>
        );
      },
    },
    {
      title: t("common.basic.actions"),
      key: "actions",
      align: "center",
      width: 240,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<Icon library="lucide" name="Eye" size={16} />}
            onClick={() => showViewModal(record)}
            title={t("notification.meeting_page.view")}
          />
          {record.status === MeetingRequestStatus.PENDING && (
            <>
              <Button
                type="text"
                icon={<Icon library="lucide" name="Check" size={16} />}
                onClick={() => showApproveModal(record)}
                style={{ color: "#52c41a" }}
                title={t("notification.meeting_page.approval")}
              />
              <Button
                type="text"
                icon={<Icon library="lucide" name="Undo2" size={16} />}
                onClick={() => showRejectModal(record)}
                style={{ color: "#faad14" }}
                title={t("notification.meeting_page.reject")}
              />
            </>
          )}
          {(record.status === MeetingRequestStatus.APPROVED ||
            record.status === MeetingRequestStatus.SCHEDULED) && (
            <Button
              type="text"
              icon={<Icon library="lucide" name="Ban" size={16} />}
              onClick={() => showCancelModal(record)}
              style={{ color: "#9E9E9E" }}
              title={t("common.basic.cancel")}
            />
          )}
          <Button
            type="text"
            danger
            icon={<Icon library="lucide" name="Trash2" size={16} />}
            onClick={() => handleDeleteMeeting(record.id)}
            title={t("notification.meeting_page.delete")}
          />
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Helmet>
        <title>{t("notification.meeting_page.title")}</title>
      </Helmet>

      <Card>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginBottom: 16,
          }}
        >
          <Title level={4}>{t("notification.meeting_page.title")}</Title>
        </div>
        {/* 搜尋區域 */}
        <Form form={searchForm} layout="vertical" style={{ marginBottom: 24 }}>
          <div
            style={{
              display: "flex",
              gap: "16px",
              marginBottom: "16px",
              alignItems: "end",
            }}
          >
            <Form.Item label={t("notification.meeting_page.status")}>
              <Select
                placeholder={t("notification.meeting_page.status")}
                value={selectedStatus}
                onChange={setSelectedStatus}
                allowClear
                style={{ width: "240px" }}
                options={Object.values(MeetingRequestStatus).map((status) => ({
                  value: status,
                  label: t(`notification.meeting_status.${status}`),
                }))}
              />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  onClick={handleSearch}
                  icon={<Icon library="lucide" name="Search" size={16} />}
                >
                  {t("notification.meeting_page.search")}
                </Button>
                <Button
                  onClick={handleReset}
                  icon={<Icon library="lucide" name="RotateCcw" size={16} />}
                >
                  {t("common.basic.reset")}
                </Button>
              </Space>
            </Form.Item>
          </div>
        </Form>
        {/* 表格區域 */}
        <Table
          columns={columns}
          dataSource={meetings}
          rowKey="id"
          scroll={{ x: "max-content" }}
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
            showTotal: (total) =>
              `${t("common.table.total")} ${total} ${t("common.table.unit")}`,
          }}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("common.status.noData")}
              />
            ),
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* 核准會議模態框 */}
      <Modal
        title={t("notification.meeting_page.approval")}
        open={isApproveModalVisible}
        onOk={handleApproveMeeting}
        onCancel={() => setIsApproveModalVisible(false)}
        okText={t("common.basic.confirm")}
        cancelText={t("common.basic.cancel")}
      >
        <Form form={approveForm} layout="vertical">
          <Form.Item
            name="meeting_time"
            label={t("notification.meeting_page.meeting_time")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <DatePicker
              showTime
              format="YYYY-MM-DD HH:mm"
              placeholder={t("common.prompt.selectDate")}
              style={{ width: "100%" }}
            />
          </Form.Item>
          <Form.Item
            name="duration"
            label="會議時長（分鐘）"
            initialValue={60}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <Select
              placeholder="請選擇會議時長"
              options={[
                { value: 15, label: "15分鐘" },
                { value: 30, label: "30分鐘" },
                { value: 60, label: "1小時" },
                { value: 90, label: "1.5小時" },
                { value: 120, label: "2小時" },
                { value: 180, label: "3小時" },
              ]}
            />
          </Form.Item>
          <Form.Item name="message" label="給客戶的附加訊息">
            <TextArea rows={4} placeholder="輸入給客戶的附加訊息（選填）" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 拒絕會議模態框 */}
      <Modal
        title={t("notification.meeting_page.reject")}
        open={isRejectModalVisible}
        onOk={handleRejectMeeting}
        onCancel={() => setIsRejectModalVisible(false)}
        okText={t("common.basic.confirm")}
        cancelText={t("common.basic.cancel")}
      >
        <Form form={rejectForm} layout="vertical">
          <Form.Item
            name="reason"
            label={t("notification.meeting_page.rejection_reason")}
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <TextArea
              rows={4}
              placeholder={t(
                "notification.meeting_page.enter_rejection_reason"
              )}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 取消會議模態框 */}
      <Modal
        title="取消會議"
        open={isCancelModalVisible}
        onOk={handleCancelMeeting}
        onCancel={() => setIsCancelModalVisible(false)}
        okText={t("common.basic.confirm")}
        cancelText={t("common.basic.cancel")}
      >
        <Form form={cancelForm} layout="vertical">
          <Form.Item
            name="reason"
            label="取消原因"
            rules={[{ required: true, message: t("common.form.required") }]}
          >
            <TextArea rows={4} placeholder="請輸入取消原因" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 檢視詳情模態框 */}
      <Modal
        title={t("notification.meeting_page.view_details")}
        open={isViewModalVisible}
        onCancel={() => setIsViewModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsViewModalVisible(false)}>
            {t("common.basic.close")}
          </Button>,
        ]}
        width={700}
      >
        {currentMeeting && (
          <Descriptions bordered column={1}>
            <Descriptions.Item label={t("notification.meeting_page.subject")}>
              {currentMeeting.subject || ""}
            </Descriptions.Item>
            <Descriptions.Item
              label={t("notification.meeting_page.requester_email")}
            >
              {currentMeeting.requester_email || ""}
            </Descriptions.Item>
            <Descriptions.Item
              label={t("notification.meeting_page.attendees_emails")}
            >
              {currentMeeting.attendees_emails?.join(", ") || ""}
            </Descriptions.Item>
            <Descriptions.Item
              label={t("notification.meeting_page.meeting_time")}
            >
              {currentMeeting.meeting_time
                ? dayjs(currentMeeting.meeting_time).format(
                    "YYYY-MM-DD HH:mm:ss"
                  )
                : ""}
            </Descriptions.Item>
            <Descriptions.Item label="會議時長">
              {currentMeeting.duration ? `${currentMeeting.duration}分鐘` : ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("notification.meeting_page.status")}>
              {t(`notification.meeting_status.${currentMeeting.status}`)}
            </Descriptions.Item>
            <Descriptions.Item
              label={t("notification.meeting_page.zoom_meeting_link")}
            >
              {currentMeeting.zoom_meeting_link ? (
                <Button
                  type="link"
                  className="px-0"
                  onClick={() =>
                    window.open(currentMeeting.zoom_meeting_link, "_blank")
                  }
                  icon={<Icon library="lucide" name="ExternalLink" size={16} />}
                >
                  {t("notification.meeting_page.zoom_meeting_link")}
                </Button>
              ) : (
                ""
              )}
            </Descriptions.Item>
            {currentMeeting.zoom_meeting_id && (
              <Descriptions.Item label="Zoom 會議 ID">
                {currentMeeting.zoom_meeting_id}
              </Descriptions.Item>
            )}
            {currentMeeting.rejection_reason && (
              <Descriptions.Item label="拒絕原因">
                {currentMeeting.rejection_reason}
              </Descriptions.Item>
            )}
            {currentMeeting.cancellation_reason && (
              <Descriptions.Item label="取消原因">
                {currentMeeting.cancellation_reason}
              </Descriptions.Item>
            )}
            {currentMeeting.customer_message && (
              <Descriptions.Item label="客戶附加訊息">
                {currentMeeting.customer_message}
              </Descriptions.Item>
            )}
            {currentMeeting.admin_message && (
              <Descriptions.Item label="管理員回覆訊息">
                {currentMeeting.admin_message}
              </Descriptions.Item>
            )}
            <Descriptions.Item label={t("common.record.createdAt")}>
              {currentMeeting.created_at
                ? dayjs(currentMeeting.created_at).format("YYYY-MM-DD HH:mm:ss")
                : ""}
            </Descriptions.Item>
            <Descriptions.Item label={t("common.record.updatedAt")}>
              {currentMeeting.updated_at
                ? dayjs(currentMeeting.updated_at).format("YYYY-MM-DD HH:mm:ss")
                : ""}
            </Descriptions.Item>
            {currentMeeting.approved_by_user_id && (
              <Descriptions.Item label="審批者">
                {currentMeeting.approved_by_user_id}
              </Descriptions.Item>
            )}
            {currentMeeting.approved_at && (
              <Descriptions.Item label="審批時間">
                {dayjs(currentMeeting.approved_at).format(
                  "YYYY-MM-DD HH:mm:ss"
                )}
              </Descriptions.Item>
            )}
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default MeetingPage;
