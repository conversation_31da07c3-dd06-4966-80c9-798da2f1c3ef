import type {
  CreateDocumentCodeRequest,
  DocumentCodeResponse,
  UpdateDocumentCodeRequest,
} from "@/api/models/document-codes";
import { documentCodeService } from "@/api/services";
import Icon from "@/components/icon";
import {
  Button,
  Card,
  Descriptions,
  Empty,
  Form,
  Input,
  Modal,
  Select,
  Space,
  Table,
  Tag,
  message,
} from "antd";
import Title from "antd/es/typography/Title";
import { useEffect, useState } from "react";
import { Helmet } from "react-helmet-async";
import { useTranslation } from "react-i18next";

const DocumentCodesPage = () => {
  const { t } = useTranslation();
  const [documentCodes, setDocumentCodes] = useState<DocumentCodeResponse[]>(
    []
  );
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentDocumentCode, setCurrentDocumentCode] =
    useState<DocumentCodeResponse | null>(null);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [viewDocumentCode, setViewDocumentCode] =
    useState<DocumentCodeResponse | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();

  // 選項資料
  const [initLoading, setInitLoading] = useState(false);

  // 類別選項
  const typeOptions = [
    { label: t("document_codes.typeOptions.incoming"), value: "收文類型" },
    { label: t("document_codes.typeOptions.outgoing"), value: "發文類型" },
    { label: t("document_codes.typeOptions.mail"), value: "郵件類型" },
    { label: t("document_codes.typeOptions.file"), value: "檔案類型" },
    { label: t("document_codes.typeOptions.case"), value: "案件類型" },
  ];

  const fetchDocumentCodes = async (params?: any) => {
    try {
      setLoading(true);
      const response = await documentCodeService.getAllDocumentCodes({
        page: currentPage,
        limit: pageSize,
        ...params,
      });
      setDocumentCodes(response.items || []);
      setTotal(response.total_count || 0);
    } catch (error) {
      console.error("獲取文件類別列表失敗:", error);
      message.error(t("document_codes.fetchFailed"));
    } finally {
      setLoading(false);
    }
  };

  // 初始化默認文件類型
  const initDocumentTypes = async () => {
    try {
      setInitLoading(true);
      await documentCodeService.initDocumentTypes();
      message.success(t("document_codes.initSuccess"));
      fetchDocumentCodes();
    } catch (error) {
      console.error("初始化文件類型失敗:", error);
      message.error(t("document_codes.initFailed"));
    } finally {
      setInitLoading(false);
    }
  };

  useEffect(() => {
    fetchDocumentCodes();
  }, [currentPage, pageSize]);

  const handleSearch = async () => {
    const values = await searchForm.validateFields();
    const params: any = {};

    if (values.code) params.code = values.code;
    if (values.name) params.name = values.name;
    if (values.type) {
      params.type = values.type;
    }

    setCurrentPage(1);
    fetchDocumentCodes(params);
  };

  const handleReset = () => {
    searchForm.resetFields();
    setCurrentPage(1);
    fetchDocumentCodes();
  };

  const showCreateModal = () => {
    setIsEditing(false);
    setCurrentDocumentCode(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const showEditModal = (documentCode: DocumentCodeResponse) => {
    setIsEditing(true);
    setCurrentDocumentCode(documentCode);
    form.setFieldsValue(documentCode);
    setIsModalVisible(true);
  };

  const showViewModal = (documentCode: DocumentCodeResponse) => {
    setViewDocumentCode(documentCode);
    setIsViewModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleViewCancel = () => {
    setIsViewModalVisible(false);
  };

  const handleDelete = async (codeId: string) => {
    Modal.confirm({
      title: t("common.confirm.confirmDelete"),
      content: t("document_codes.confirmDeleteDesc"),
      onOk: async () => {
        try {
          await documentCodeService.deleteDocumentCode(codeId);
          message.success(t("common.result.deleteSuccess"));
          fetchDocumentCodes();
        } catch (error) {
          console.error("刪除文件類型失敗:", error);
          message.error(t("common.result.deleteFailed"));
        }
      },
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (isEditing && currentDocumentCode) {
        // 更新文件類型
        const updateData: UpdateDocumentCodeRequest = {
          name: values.name,
          type: values.type,
        };

        await documentCodeService.updateDocumentCode(
          currentDocumentCode.id,
          updateData
        );
        message.success(t("common.result.updateSuccess"));
      } else {
        // 創建文件類型
        const createData: CreateDocumentCodeRequest = {
          code: values.code,
          name: values.name,
          type: values.type,
        };

        await documentCodeService.createDocumentCode(createData);
        message.success(t("common.result.createSuccess"));
      }
      setIsModalVisible(false);
      form.resetFields();
      fetchDocumentCodes();
    } catch (error) {
      console.error("提交表單失敗:", error);
      message.error(t("common.result.submitFailed"));
    }
  };

  const columns = [
    {
      title: t("document_codes.code"),
      dataIndex: "code",
      key: "code",
      width: 120,
    },
    {
      title: t("document_codes.name"),
      dataIndex: "name",
      key: "name",
      width: 200,
    },
    {
      title: t("document_codes.type"),
      dataIndex: "type",
      key: "type",
      width: 150,
      render: (type: string) => {
        let color: string;
        switch (type) {
          case "收文類型":
            color = "blue";
            break;
          case "發文類型":
            color = "green";
            break;
          case "郵件類型":
            color = "orange";
            break;
          case "檔案類型":
            color = "purple";
            break;
          case "案件類型":
            color = "red";
            break;
          default:
            color = "default";
        }
        return <Tag color={color}>{type}</Tag>;
      },
    },
    {
      title: t("common.basic.actions"),
      key: "actions",
      width: 180,
      render: (_: any, record: DocumentCodeResponse) => (
        <Space size="small">
          <Button
            type="text"
            icon={<Icon library="lucide" name="Eye" size={16} />}
            onClick={() => showViewModal(record)}
            title={t("document_codes.view")}
          />
          <Button
            type="text"
            icon={<Icon library="lucide" name="Edit" size={16} />}
            onClick={() => showEditModal(record)}
            title={t("common.basic.edit")}
          />
          <Button
            type="text"
            danger
            icon={<Icon library="lucide" name="Trash2" size={16} />}
            onClick={() => handleDelete(record.id)}
            title={t("common.basic.delete")}
          />
        </Space>
      ),
    },
  ];

  return (
    <>
      <Helmet>
        <title>{t("document_codes.title")} - Cosmos-Fleet Admin</title>
      </Helmet>

      <Card
        title={<Title level={4}>{t("document_codes.title")}</Title>}
        extra={
          <Space>
            <Button
              type="primary"
              icon={<Icon library="lucide" name="Plus" size={16} />}
              onClick={showCreateModal}
            >
              {t("document_codes.add")}
            </Button>
            <Button
              icon={<Icon library="lucide" name="RefreshCw" size={16} />}
              onClick={initDocumentTypes}
              loading={initLoading}
              title={t("document_codes.initTypes")}
            >
              {t("document_codes.init")}
            </Button>
          </Space>
        }
      >
        <Card
          size="small"
          style={{ marginBottom: 16 }}
          title={
            <span className="flex items-center gap-2">
              <Icon library="lucide" name="Search" size={16} />
              {t("common.basic.search")}
            </span>
          }
        >
          <Form
            form={searchForm}
            onFinish={handleSearch}
            layout="vertical"
            className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4"
          >
            <Form.Item name="code" label={t("document_codes.code")}>
              <Input
                placeholder={t("document_codes.inputCode")}
                style={{ width: "100%" }}
              />
            </Form.Item>
            <Form.Item name="name" label={t("document_codes.name")}>
              <Input
                placeholder={t("document_codes.inputName")}
                style={{ width: "100%" }}
              />
            </Form.Item>
            <Form.Item name="type" label={t("document_codes.type")}>
              <Select
                placeholder={t("document_codes.selectType")}
                style={{ width: "100%" }}
                options={typeOptions}
                allowClear
              />
            </Form.Item>
            <Form.Item className="flex items-end">
              <Space>
                <Button type="primary" htmlType="submit">
                  {t("common.basic.search")}
                </Button>
                <Button onClick={handleReset}>{t("common.basic.reset")}</Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>

        <Table
          columns={columns}
          dataSource={documentCodes}
          rowKey="id"
          scroll={{ x: "max-content" }}
          loading={loading}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("case.emptyData")}
              />
            ),
          }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showTotal: (total) =>
              `${t("common.table.total")} ${total} ${t("common.table.unit")}`,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
          }}
        />
      </Card>

      {/* 新增/編輯模態窗 */}
      <Modal
        title={isEditing ? t("document_codes.edit") : t("document_codes.add")}
        open={isModalVisible}
        onCancel={handleCancel}
        onOk={handleSubmit}
        width={500}
        maskClosable={false}
      >
        <Form form={form} layout="vertical">
          {!isEditing && (
            <Form.Item
              name="code"
              label={t("document_codes.code")}
              rules={[
                { required: true, message: t("document_codes.codeRequired") },
              ]}
            >
              <Input placeholder={t("document_codes.inputCode")} />
            </Form.Item>
          )}
          <Form.Item
            name="name"
            label={t("document_codes.name")}
            rules={[
              { required: true, message: t("document_codes.nameRequired") },
            ]}
          >
            <Input placeholder={t("document_codes.inputName")} />
          </Form.Item>
          <Form.Item
            name="type"
            label={t("document_codes.type")}
            rules={[
              { required: true, message: t("document_codes.typeRequired") },
            ]}
          >
            <Select
              placeholder={t("document_codes.selectType")}
              options={typeOptions}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 檢視模態窗 */}
      <Modal
        title={t("document_codes.viewDetails")}
        open={isViewModalVisible}
        onCancel={handleViewCancel}
        footer={[
          <Button key="close" onClick={handleViewCancel}>
            {t("common.basic.close")}
          </Button>,
        ]}
        width={600}
      >
        {viewDocumentCode && (
          <Descriptions bordered column={1}>
            <Descriptions.Item label={t("document_codes.code")}>
              {viewDocumentCode.code}
            </Descriptions.Item>
            <Descriptions.Item label={t("document_codes.name")}>
              {viewDocumentCode.name}
            </Descriptions.Item>
            <Descriptions.Item label={t("document_codes.type")}>
              <Tag
                color={
                  viewDocumentCode.type === "收文類型"
                    ? "blue"
                    : viewDocumentCode.type === "發文類型"
                    ? "green"
                    : viewDocumentCode.type === "郵件類型"
                    ? "orange"
                    : viewDocumentCode.type === "檔案類型"
                    ? "purple"
                    : viewDocumentCode.type === "案件類型"
                    ? "red"
                    : "default"
                }
              >
                {viewDocumentCode.type}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label={t("common.record.createdAt")}>
              {viewDocumentCode.created_at}
            </Descriptions.Item>
            <Descriptions.Item label={t("common.record.createdBy")}>
              {viewDocumentCode.created_by}
            </Descriptions.Item>
            <Descriptions.Item label={t("common.record.updatedAt")}>
              {viewDocumentCode.updated_at}
            </Descriptions.Item>
            <Descriptions.Item label={t("common.record.updatedBy")}>
              {viewDocumentCode.updated_by}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </>
  );
};

export default DocumentCodesPage;
