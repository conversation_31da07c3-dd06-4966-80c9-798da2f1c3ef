import { Dropdown, type MenuProps } from "antd";
import { useTranslation } from "react-i18next";
import { MultiTabOperation } from "#/enum";
import { useTabLabelRender } from "../hooks/use-tab-label-render";
import { useMultiTabsContext } from "../providers/multi-tabs-provider";
import type { TabItemProps } from "../types";
import Icon from "@/components/icon";

// 預先創建圖標元素
const closeIcon = <Icon library="lucide" name="X" size={12} />;

export function TabItem({ tab, style, onClose }: TabItemProps) {
  const { t } = useTranslation();
  const {
    tabs,
    refreshTab,
    closeTab,
    closeOthersTab,
    closeLeft,
    closeRight,
    closeAll,
  } = useMultiTabsContext();

  const renderTabLabel = useTabLabelRender();
  const menuItems: MenuProps["items"] = [
    {
      label: t(`sys.tab.${MultiTabOperation.REFRESH}`),
      key: MultiTabOperation.REFRESH,
    },
    {
      label: t(`sys.tab.${MultiTabOperation.CLOSE}`),
      key: MultiTabOperation.CLOSE,
      disabled: tabs.length === 1,
    },
    {
      type: "divider",
    },
    {
      label: t(`sys.tab.${MultiTabOperation.CLOSELEFT}`),
      key: MultiTabOperation.CLOSELEFT,
      disabled: tabs.findIndex((t) => t.key === tab.key) === 0,
    },
    {
      label: t(`sys.tab.${MultiTabOperation.CLOSERIGHT}`),
      key: MultiTabOperation.CLOSERIGHT,
      disabled: tabs.findIndex((t) => t.key === tab.key) === tabs.length - 1,
    },
    {
      type: "divider",
    },
    {
      label: t(`sys.tab.${MultiTabOperation.CLOSEOTHERS}`),
      key: MultiTabOperation.CLOSEOTHERS,
      disabled: tabs.length === 1,
    },
    {
      label: t(`sys.tab.${MultiTabOperation.CLOSEALL}`),
      key: MultiTabOperation.CLOSEALL,
    },
  ];

  const menuClick = (menuInfo: any) => {
    const { key, domEvent } = menuInfo;
    domEvent.stopPropagation();

    switch (key) {
      case MultiTabOperation.REFRESH:
        refreshTab(tab.key);
        break;
      case MultiTabOperation.CLOSE:
        closeTab(tab.key);
        break;
      case MultiTabOperation.CLOSEOTHERS:
        closeOthersTab(tab.key);
        break;
      case MultiTabOperation.CLOSELEFT:
        closeLeft(tab.key);
        break;
      case MultiTabOperation.CLOSERIGHT:
        closeRight(tab.key);
        break;
      case MultiTabOperation.CLOSEALL:
        closeAll();
        break;
      default:
        break;
    }
  };

  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClose?.();
  };

  return (
    <Dropdown
      trigger={["contextMenu"]}
      menu={{
        items: menuItems,
        onClick: menuClick,
      }}
    >
      <div
        className="relative flex select-none items-center px-4 py-1"
        style={style}
      >
        <div>{renderTabLabel(tab)}</div>
        {tabs.length > 1 && (
          <div
            className="ml-2 cursor-pointer hover:bg-gray-200 hover:text-primary rounded-full w-4 h-4 flex items-center justify-center transition-colors"
            onClick={handleClose}
          >
            {closeIcon}
          </div>
        )}
      </div>
    </Dropdown>
  );
}
