import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import type { KeepAliveTab } from "../types";

export function useTabLabelRender() {
  const { t } = useTranslation();

  const specialTabRenderMap = useMemo<
    Record<string, (tab: KeepAliveTab) => React.ReactNode>
  >(
    () => ({
      // 如果需要特殊渲染的標籤，可以在這裡添加
      // 例如：
      // "sys.menu.example": (tab: KeepAliveTab) => {
      //   return `特殊標籤-${t(tab.label)}`;
      // },
    }),
    [t]
  );

  const renderTabLabel = (tab: KeepAliveTab) => {
    const specialRender = specialTabRenderMap[tab.label];
    if (specialRender) {
      return specialRender(tab);
    }
    return t(tab.label);
  };

  return renderTabLabel;
}
