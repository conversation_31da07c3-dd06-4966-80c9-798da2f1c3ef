import { Drawer } from "antd";
import { type CSSProperties, useState } from "react";

import Logo from "@/components/logo";
import { useSettings } from "@/store/settingStore";
import BreadCrumb from "../_common/bread-crumb";

import { cn } from "@/utils";
import { ThemeLayout } from "#/enum";
import AccountDropdown from "../_common/account-dropdown";
import { HEADER_HEIGHT, NAV_COLLAPSED_WIDTH, NAV_WIDTH } from "./config";
import NavVertical from "./nav/nav-vertical";

export default function Header() {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const { themeLayout, breadCrumb } = useSettings();

  const headerStyle: CSSProperties = {
    borderBottom:
      themeLayout === ThemeLayout.Horizontal
        ? "1px dashed rgba(107, 114, 128, 0.2)"
        : "",
    backgroundColor: "rgba(249, 250, 251, 0.9)",
    width: "100%",
  };

  return (
    <>
      <header
        className={cn(
          themeLayout === ThemeLayout.Horizontal
            ? "relative"
            : "sticky top-0 right-0 left-auto"
        )}
        style={headerStyle}
      >
        <div
          className="flex flex-grow items-center justify-between px-4 text-gray backdrop-blur xl:px-6 2xl:px-10"
          style={{
            height: HEADER_HEIGHT,
            transition: "height 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms",
          }}
        >
          <div className="flex items-baseline">
            {themeLayout !== ThemeLayout.Horizontal ? (
              <button
                onClick={() => setDrawerOpen(true)}
                className="h-10 w-10 md:hidden bg-gray-500 border-0 cursor-pointer hover:scale-105"
              >
                ☰
              </button>
            ) : (
              <Logo />
            )}
            <div className="ml-4 hidden md:block">
              {breadCrumb ? <BreadCrumb /> : null}
            </div>
          </div>
          <AccountDropdown />
        </div>
      </header>
      <Drawer
        placement="left"
        onClose={() => setDrawerOpen(false)}
        open={drawerOpen}
        closeIcon={false}
        width={
          themeLayout === ThemeLayout.Mini ? NAV_COLLAPSED_WIDTH : NAV_WIDTH
        }
      >
        <NavVertical closeSideBarDrawer={() => setDrawerOpen(false)} />
      </Drawer>
    </>
  );
}
