import { <PERSON><PERSON>, <PERSON>, Drawer, Switch, Radio } from "antd";
import { useState, type CSSProperties } from "react";
import type { RadioChangeEvent } from "antd";

import Icon from "@/components/icon";
import CyanBlur from "@/assets/images/background/cyan-blur.png";
import RedBlur from "@/assets/images/background/red-blur.png";
import { useSettingActions, useSettings } from "@/store/settingStore";
import { cn } from "@/utils";
import { type ThemeColorPresets, ThemeLayout, ThemeMode } from "#/enum";

// 顏色預設值 - 與 tailwind.config.ts 保持同步
const PRESETS_COLORS = {
  default: {
    default: "#00A76F",
  },
  cyan: {
    default: "#00B8D9",
  },
  blue: {
    default: "#2065D1",
  },
  purple: {
    default: "#7635DC",
  },
  orange: {
    default: "#FDA92D",
  },
  red: {
    default: "#FF3030",
  },
};

// 字體預設值
const FONT_FAMILY_PRESET = {
  openSans: "Open Sans Variable",
  inter: "Inter Variable",
};

/**
 * App Setting
 */
export default function SettingButton() {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const settings = useSettings();
  const {
    themeMode,
    themeColorPresets,
    themeLayout,
    themeStretch,
    breadCrumb,
    multiTab,
    darkSidebar,
    fontSize,
    fontFamily,
  } = settings;
  const { setSettings } = useSettingActions();

  const setThemeMode = (themeMode: ThemeMode) => {
    setSettings({
      ...settings,
      themeMode,
    });
  };

  const setThemeColorPresets = (themeColorPresets: ThemeColorPresets) => {
    setSettings({
      ...settings,
      themeColorPresets,
    });
  };

  const setThemeLayout = (themeLayout: ThemeLayout) => {
    setSettings({
      ...settings,
      themeLayout,
    });
  };

  const setThemeStretch = (themeStretch: boolean) => {
    setSettings({
      ...settings,
      themeStretch,
    });
  };

  const setBreadCrumn = (checked: boolean) => {
    setSettings({
      ...settings,
      breadCrumb: checked,
    });
  };

  const setMultiTab = (checked: boolean) => {
    setSettings({
      ...settings,
      multiTab: checked,
    });
  };

  const setDarkSidebar = (checked: boolean) => {
    setSettings({
      ...settings,
      darkSidebar: checked,
    });
  };

  const setFontFamily = (fontFamily: string) => {
    setSettings({
      ...settings,
      fontFamily,
    });
  };

  const setFontSize = (fontSize: number) => {
    setSettings({
      ...settings,
      fontSize,
    });
  };

  const style: CSSProperties = {
    backdropFilter: "blur(20px)",
    backgroundImage: `url("${CyanBlur}"), url("${RedBlur}")`,
    backgroundRepeat: "no-repeat, no-repeat",
    backgroundColor: `rgba(255, 255, 255, 0.9)`,
    backgroundPosition: "right top, left bottom",
    backgroundSize: "50, 50%",
  };

  const toggleFullScreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 取得當前主題色
  const primaryColor =
    PRESETS_COLORS[themeColorPresets]?.default ||
    PRESETS_COLORS.default.default;

  // 計算布局背景色
  const layoutBackground = (layout: ThemeLayout) =>
    themeLayout === layout
      ? `linear-gradient(135deg, #F4F6F8 0%, ${primaryColor} 100%)`
      : "#919EAB";

  // 深色模式背景
  const isDarkMode = themeMode === ThemeMode.Dark;
  if (isDarkMode) {
    style.backgroundColor = "rgba(33, 43, 54, 0.9)";
  }

  return (
    <>
      <div className="flex items-center justify-center">
        <button
          className="h-10 w-10 flex items-center justify-center rounded-full bg-white shadow-md hover:scale-105 transition-transform duration-200"
          onClick={() => setDrawerOpen(true)}
        >
          <Icon library="lucide" name="Settings" size={18} />
        </button>
      </div>
      <Drawer
        placement="right"
        title="Settings"
        onClose={() => setDrawerOpen(false)}
        open={drawerOpen}
        closable={false}
        styles={{
          body: { padding: 0 },
          mask: { backgroundColor: "transparent" },
        }}
        style={style}
        extra={
          <button
            onClick={() => setDrawerOpen(false)}
            className="h-9 w-9 hover:scale-105 flex items-center justify-center"
          >
            <Icon library="lucide" name="X" className="text-gray-400" />
          </button>
        }
        footer={
          <Button type="dashed" block size="large" onClick={toggleFullScreen}>
            <div className="flex items-center justify-center">
              {isFullscreen ? (
                <>
                  <Icon library="lucide" name="FullscreenExit" size={16} />
                  <span className="ml-2">Exit FullScreen</span>
                </>
              ) : (
                <>
                  <Icon library="lucide" name="Fullscreen" size={16} />
                  <span className="ml-2 text-gray">FullScreen</span>
                </>
              )}
            </div>
          </Button>
        }
      >
        <div className="flex flex-col gap-6 p-6">
          {/* theme mode */}
          <div>
            <div className="mb-3 text-base font-semibold text-text-secondary">
              Mode
            </div>
            <div className="flex flex-row gap-4">
              <Card
                onClick={() => setThemeMode(ThemeMode.Light)}
                className="flex h-20 w-full cursor-pointer items-center justify-center"
              >
                <span
                  style={{
                    color:
                      themeMode === ThemeMode.Light ? primaryColor : "#637381",
                  }}
                >
                  🔆
                </span>
              </Card>
              <Card
                onClick={() => setThemeMode(ThemeMode.Dark)}
                className="flex h-20 w-full cursor-pointer items-center justify-center"
              >
                <span
                  style={{
                    color:
                      themeMode === ThemeMode.Dark ? primaryColor : "#637381",
                  }}
                >
                  🌙
                </span>
              </Card>
            </div>
          </div>

          {/* theme color */}
          <div>
            <div className="mb-3 text-base font-semibold text-text-secondary">
              Presets
            </div>
            <Radio.Group
              value={themeColorPresets}
              onChange={(e) =>
                setThemeColorPresets(e.target.value as ThemeColorPresets)
              }
            >
              <div className="flex flex-wrap gap-4">
                {(
                  Object.keys(PRESETS_COLORS) as Array<
                    keyof typeof PRESETS_COLORS
                  >
                ).map((color) => (
                  <div className="flex items-center justify-center" key={color}>
                    <Radio
                      value={color}
                      style={{ display: "none" }}
                      id={`picker-${color}`}
                    />
                    <label htmlFor={`picker-${color}`}>
                      <div
                        className={cn(
                          "flex h-10 w-10 cursor-pointer items-center justify-center rounded-full",
                          themeColorPresets === color &&
                            "ring-2 ring-primary ring-offset-2"
                        )}
                        style={{
                          backgroundColor: PRESETS_COLORS[color].default,
                        }}
                      />
                    </label>
                  </div>
                ))}
              </div>
            </Radio.Group>
          </div>

          {/* theme layout */}
          <div>
            <div className="mb-3 text-base font-semibold text-text-secondary">
              Layout
            </div>
            <Radio.Group
              value={themeLayout}
              onChange={(e: RadioChangeEvent) =>
                setThemeLayout(e.target.value as ThemeLayout)
              }
            >
              <div className="flex flex-col gap-4">
                <Card className="cursor-pointer">
                  <div className="flex items-center">
                    <Radio value={ThemeLayout.Horizontal}>
                      <div className="ml-2">Horizontal</div>
                    </Radio>
                    <div
                      className="ml-auto h-3 w-10 rounded-full"
                      style={{
                        background: layoutBackground(ThemeLayout.Horizontal),
                      }}
                    />
                  </div>
                </Card>
                <Card className="cursor-pointer">
                  <div className="flex items-center">
                    <Radio value={ThemeLayout.Vertical}>
                      <div className="ml-2">Vertical</div>
                    </Radio>
                    <div
                      className="ml-auto h-3 w-10 rounded-full"
                      style={{
                        background: layoutBackground(ThemeLayout.Vertical),
                      }}
                    />
                  </div>
                </Card>
                <Card className="cursor-pointer">
                  <div className="flex items-center">
                    <Radio value={ThemeLayout.Mini}>
                      <div className="ml-2">Mini</div>
                    </Radio>
                    <div
                      className="ml-auto h-3 w-10 rounded-full"
                      style={{
                        background: layoutBackground(ThemeLayout.Mini),
                      }}
                    />
                  </div>
                </Card>
              </div>
            </Radio.Group>
          </div>

          {/* stretch */}
          <div>
            <div className="mb-3 text-base font-semibold text-text-secondary">
              Stretch
            </div>
            <Card className="cursor-pointer">
              <div className="flex items-center">
                <div>Content</div>
                <Switch
                  className="ml-auto"
                  checked={themeStretch}
                  onChange={(checked) => setThemeStretch(checked)}
                />
              </div>
            </Card>
          </div>

          {/* breadcrumb */}
          <div>
            <div className="mb-3 text-base font-semibold text-text-secondary">
              Breadcrumb
            </div>
            <Card className="cursor-pointer">
              <div className="flex items-center">
                <div>Breadcrumb</div>
                <Switch
                  className="ml-auto"
                  checked={breadCrumb}
                  onChange={(checked) => setBreadCrumn(checked)}
                />
              </div>
            </Card>
          </div>

          {/* multi tab */}
          <div>
            <div className="mb-3 text-base font-semibold text-text-secondary">
              Multi Tab
            </div>
            <Card className="cursor-pointer">
              <div className="flex items-center">
                <div>Multi Tab</div>
                <Switch
                  className="ml-auto"
                  checked={multiTab}
                  onChange={(checked) => setMultiTab(checked)}
                />
              </div>
            </Card>
          </div>

          {/* dark sidebar */}
          <div>
            <div className="mb-3 text-base font-semibold text-text-secondary">
              Dark Sidebar
            </div>
            <Card className="cursor-pointer">
              <div className="flex items-center">
                <div>Dark Sidebar</div>
                <Switch
                  className="ml-auto"
                  checked={darkSidebar}
                  onChange={(checked) => setDarkSidebar(checked)}
                />
              </div>
            </Card>
          </div>

          {/* font family */}
          <div>
            <div className="mb-3 text-base font-semibold text-text-secondary">
              Font Family
            </div>
            <Radio.Group
              value={fontFamily}
              onChange={(e: RadioChangeEvent) =>
                setFontFamily(e.target.value as string)
              }
            >
              <div className="flex flex-col gap-4">
                <Card className="cursor-pointer">
                  <div className="flex items-center">
                    <Radio value={FONT_FAMILY_PRESET.openSans}>
                      <div className="ml-2">Open Sans</div>
                    </Radio>
                  </div>
                </Card>
                <Card className="cursor-pointer">
                  <div className="flex items-center">
                    <Radio value={FONT_FAMILY_PRESET.inter}>
                      <div className="ml-2">Inter</div>
                    </Radio>
                  </div>
                </Card>
              </div>
            </Radio.Group>
          </div>

          {/* font size */}
          <div>
            <div className="mb-3 text-base font-semibold text-text-secondary">
              Font Size
            </div>
            <Radio.Group
              value={fontSize}
              onChange={(e: RadioChangeEvent) => setFontSize(e.target.value)}
            >
              <div className="flex flex-col gap-4">
                <Card className="cursor-pointer">
                  <div className="flex items-center">
                    <Radio value={14}>
                      <div className="ml-2">Small</div>
                    </Radio>
                  </div>
                </Card>
                <Card className="cursor-pointer">
                  <div className="flex items-center">
                    <Radio value={16}>
                      <div className="ml-2">Medium</div>
                    </Radio>
                  </div>
                </Card>
                <Card className="cursor-pointer">
                  <div className="flex items-center">
                    <Radio value={18}>
                      <div className="ml-2">Large</div>
                    </Radio>
                  </div>
                </Card>
              </div>
            </Radio.Group>
          </div>
        </div>
      </Drawer>
    </>
  );
}
