import { Breadcrumb, type BreadcrumbProps, type GetProp } from "antd";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Link, useMatches } from "react-router";

import { useFlattenedRoutes, usePermissionRoutes } from "@/router/hooks";
import { menuFilter } from "@/router/utils";

type MenuItem = GetProp<BreadcrumbProps, "items">[number];

/**
 * 將任何可能的 className 值轉換為安全的字串
 */
function normalizeClassName(className: any): string {
  if (!className) {
    return "";
  }

  if (typeof className === "string") {
    return className;
  }

  if (Array.isArray(className)) {
    return className
      .filter((item) => item && typeof item === "string")
      .join(" ");
  }

  if (typeof className === "object") {
    return Object.entries(className)
      .filter(([_, value]) => Boolean(value))
      .map(([key]) => key)
      .join(" ");
  }

  return "";
}

// 安全的麵包屑分隔符組件
const SafeSeparator = () => <span>•</span>;

/**
 * 動態麵包屑解決方案
 */
export default function BreadCrumb() {
  const { t } = useTranslation();
  const matches = useMatches();
  const flattenedRoutes = useFlattenedRoutes();
  const permissionRoutes = usePermissionRoutes();

  const breadCrumbs = useMemo(() => {
    const menuRoutes = menuFilter(permissionRoutes);
    const paths = matches
      .filter((item) => item.pathname !== "/")
      .map((item) => item.pathname);

    const pathRouteMetas = flattenedRoutes.filter((item) =>
      paths.includes(item.key)
    );

    let currentMenuItems = [...menuRoutes];

    return pathRouteMetas.map((routeMeta): MenuItem => {
      const { key, label } = routeMeta;

      // Find current level menu items
      const currentRoute = currentMenuItems.find(
        (item) => item.meta?.key === key
      );

      // Update menu items for next level
      currentMenuItems =
        currentRoute?.children?.filter((item) => !item.meta?.hideMenu) ?? [];

      return {
        key,
        title: t(label),
        ...(currentMenuItems.length > 0 && {
          menu: {
            items: currentMenuItems.map((item) => ({
              key: item.meta?.key,
              label: item.meta?.key ? (
                <Link to={item.meta.key}>{t(item.meta.label)}</Link>
              ) : null,
            })),
          },
        }),
      };
    });
  }, [matches, flattenedRoutes, t, permissionRoutes]);

  return (
    <Breadcrumb
      items={breadCrumbs}
      className={normalizeClassName("!text-sm")}
      separator={<SafeSeparator />}
    />
  );
}
