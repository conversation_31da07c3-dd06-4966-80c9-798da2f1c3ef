import Icon from "@/components/icon";
import type { MenuProps } from "antd";
import { Descriptions, Modal } from "antd";
import Dropdown, { type DropdownProps } from "antd/es/dropdown/dropdown";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { NavLink } from "react-router";

import { useLoginStateContext } from "@/pages/sys/login/providers/LoginStateProvider";
import { useRouter } from "@/router/hooks";
import { useUserActions, useUserInfo } from "@/store/userStore";
import { useTheme } from "@/theme/hooks";

const { VITE_APP_HOMEPAGE: HOMEPAGE } = import.meta.env;

/**
 * Account Dropdown
 */
export default function AccountDropdown() {
  const { replace } = useRouter();
  const { clearUserData } = useUserActions();
  const { backToLogin } = useLoginStateContext();
  const { t } = useTranslation();
  const userInfo = useUserInfo();
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);

  const logout = () => {
    try {
      clearUserData();
      backToLogin();
    } catch (error) {
      console.log(error);
    } finally {
      replace("/login");
    }
  };
  const { isDarkMode } = useTheme();

  const contentStyle: React.CSSProperties = {
    backgroundColor: isDarkMode ? "#1f2937" : "#ffffff",
    borderRadius: "0.5rem",
    boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
  };

  const menuStyle: React.CSSProperties = {
    boxShadow: "none",
  };

  const dropdownRender: DropdownProps["dropdownRender"] = (menu) => (
    <div style={contentStyle}>
      {React.cloneElement(menu as React.ReactElement, { style: menuStyle })}
    </div>
  );

  const showProfileModal = () => {
    setIsProfileModalOpen(true);
  };

  const handleProfileModalClose = () => {
    setIsProfileModalOpen(false);
  };

  const items: MenuProps["items"] = [
    {
      label: <NavLink to={HOMEPAGE}>{t("sys.menu.dashboard")}</NavLink>,
      key: "1",
    },
    {
      label: (
        <button
          className="w-full text-left p-0 border-0 bg-transparent cursor-pointer"
          type="button"
          onClick={showProfileModal}
        >
          {t("user.profile")}
        </button>
      ),
      key: "2",
    },
    { type: "divider" },
    {
      label: (
        <button className="font-bold text-warning" type="button">
          {t("sys.login.logout")}
        </button>
      ),
      key: "3",
      onClick: logout,
    },
  ];

  return (
    <>
      <Dropdown
        menu={{ items }}
        trigger={["click"]}
        dropdownRender={dropdownRender}
      >
        <button className="h-10 w-10 transform-none px-0 hover:scale-105 bg-gray-500 border-0  rounded-full cursor-pointer flex justify-center items-center">
          <Icon library="lucide" name="User" size={16} />
        </button>
      </Dropdown>

      <Modal
        title={t("user.profile")}
        open={isProfileModalOpen}
        onCancel={handleProfileModalClose}
        footer={null}
        width={600}
        centered
      >
        <Descriptions
          bordered
          column={1}
          labelStyle={{ width: "140px", backgroundColor: "#fafafa" }}
        >
          <Descriptions.Item label={t("user.account")}>
            {userInfo.account || ""}
          </Descriptions.Item>
          <Descriptions.Item label={t("user.name")}>
            {userInfo.username || ""}
          </Descriptions.Item>
          <Descriptions.Item label={t("user.role")}>
            {userInfo.roleName || ""}
          </Descriptions.Item>
          <Descriptions.Item label={t("user.department")}>
            {userInfo.department || ""}
          </Descriptions.Item>
          <Descriptions.Item label={t("user.position")}>
            {userInfo.position || ""}
          </Descriptions.Item>
          <Descriptions.Item label={t("user.isAdmin")}>
            {userInfo.isAdmin ? "是" : "否"}
          </Descriptions.Item>
        </Descriptions>
      </Modal>
    </>
  );
}
