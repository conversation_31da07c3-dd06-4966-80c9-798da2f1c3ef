import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>er, <PERSON>, Tabs, type TabsProps, Tag } from "antd";
import { type CSSProperties, type ReactNode, useState } from "react";

import CyanBlur from "@/assets/images/background/cyan-blur.png";
import RedBlur from "@/assets/images/background/red-blur.png";
import Icon from "@/components/icon";
export default function NoticeButton() {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [count, setCount] = useState(4);

  const style: CSSProperties = {
    backdropFilter: "blur(20px)",
    backgroundImage: `url("${CyanBlur}"), url("${RedBlur}")`,
    backgroundRepeat: "no-repeat, no-repeat",
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    backgroundPosition: "right top, left bottom",
    backgroundSize: "50, 50%",
  };

  return (
    <div>
      <button
        onClick={() => setDrawerOpen(true)}
        className="bg-transparent border-0 cursor-pointer"
      >
        <Badge
          count={count}
          styles={{
            root: { color: "inherit" },
            indicator: { color: "#fff" },
          }}
        >
          <Icon library="lucide" name="Bell" style={{ fontSize: "24px" }} />
        </Badge>
      </button>
      <Drawer
        placement="right"
        title="Notifications"
        onClose={() => setDrawerOpen(false)}
        open={drawerOpen}
        closable={false}
        width={420}
        styles={{
          body: { padding: 0 },
          mask: { backgroundColor: "transparent" },
        }}
        style={style}
        extra={
          <button
            style={{
              color: "#2065D1",
              background: "transparent",
              border: "none",
              cursor: "pointer",
            }}
            onClick={() => {
              setCount(0);
              setDrawerOpen(false);
            }}
          >
            ✓
          </button>
        }
        footer={
          <div className="flex h-10 w-full items-center justify-center font-semibold text-gray-700">
            View All
          </div>
        }
      >
        <NoticeTab />
      </Drawer>
    </div>
  );
}

function NoticeTab() {
  const tabChildren: ReactNode = (
    <div className="text-sm">
      <div className="flex">
        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
          <span>👤</span>
        </div>
        <div className="ml-2">
          <div>
            <span className="font-medium">John Doe</span>
            <span className="text-xs font-light">
              sent you a friend request
            </span>
          </div>
          <span className="text-xs font-light opacity-60">
            about 1 hour ago
          </span>
          <div className="mt-2">
            <Space>
              <Button type="primary">Accept</Button>
              <Button>Refuse</Button>
            </Space>
          </div>
        </div>
      </div>

      <div className="mt-8 flex">
        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
          <span>👤</span>
        </div>
        <div className="ml-2">
          <div>
            <span className="font-medium">Jane Smith</span>
            <span className="text-xs font-light"> added file to </span>
            <span className="font-medium">File Manager</span>
          </div>
          <span className="text-xs font-light opacity-60">5 hour ago</span>
          <div className="mt-2 flex items-center rounded-lg bg-bg-neutral p-4">
            <div className="ml-2 flex flex-col text-gray">
              <span className="font-medium">@Jane</span>
              <span className="text-xs">
                Please review the latest document I shared with you.
              </span>
            </div>
          </div>
          <div className="mt-2">
            <Space>
              <Button type="primary">Reply</Button>
            </Space>
          </div>
        </div>
      </div>

      <div className="mt-8 flex">
        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
          <span>👤</span>
        </div>
        <div className="ml-2">
          <div>
            <span className="font-medium">System</span>
            <span className="text-xs font-light"> mentioned you in </span>
            <span className="font-medium">Cosmos-Fleet Admin</span>
          </div>
          <span className="text-xs font-light opacity-60">1 days ago</span>
          <div className="mt-2">
            <Space>
              <Button type="primary">Reply</Button>
            </Space>
          </div>
        </div>
      </div>

      <div className="mt-8 flex">
        <button className="bg-transparent border-0">
          <span style={{ fontSize: "30px" }}>📦</span>
        </button>
        <div className="ml-2">
          <div>
            <span className="font-light">
              Your order is placed waiting for shipping
            </span>
          </div>
          <span className="text-xs font-light opacity-60">4 days ago</span>
        </div>
      </div>

      <div className="mt-8 flex">
        <button className="bg-transparent border-0">
          <span style={{ fontSize: "30px" }}>✉️</span>
        </button>
        <div className="ml-2">
          <div>
            <span className="font-light">You have new mail</span>
          </div>
          <span className="text-xs font-light opacity-60">5 days ago</span>
        </div>
      </div>

      <div className="mt-8 flex">
        <button className="bg-transparent border-0">
          <span style={{ fontSize: "30px" }}>💬</span>
        </button>
        <div className="ml-2">
          <div>
            <span className="font-light">
              You have new message 5 unread message
            </span>
          </div>
          <span className="text-xs font-light opacity-60">7 days ago</span>
        </div>
      </div>
    </div>
  );
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: (
        <div className="flex">
          <span>All</span>
          <Tag color="processing">22</Tag>
        </div>
      ),
      children: tabChildren,
    },
    {
      key: "2",
      label: (
        <div className="flex">
          <span>Unread</span>
          <Tag color="error">12</Tag>
        </div>
      ),
      children: tabChildren,
    },
    {
      key: "3",
      label: (
        <div className="flex">
          <span>Archived</span>
          <Tag color="green">10</Tag>
        </div>
      ),
      children: tabChildren,
    },
  ];
  return (
    <div className="flex flex-col px-6">
      <Tabs defaultActiveKey="1" items={items} />
    </div>
  );
}
