import { Empty, Input, type InputRef, Modal, Tag } from "antd";
import {
  type CSSProperties,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";

import Icon from "@/components/icon";
import Scrollbar from "@/components/scrollbar";
import { useFlattenedRoutes, useRouter } from "@/router/hooks";
import { cn } from "@/utils";

export default function SearchBar() {
  const { t } = useTranslation();
  const { replace } = useRouter();
  const inputRef = useRef<InputRef>(null);
  const listRef = useRef<HTMLDivElement>(null);

  const [search, setSearch] = useState(false);

  const flattenedRoutes = useFlattenedRoutes();

  const activeStyle: CSSProperties = {
    border: "1px dashed #2065D1",
    backgroundColor: "rgba(32, 101, 209, 0.1)",
  };

  const [searchQuery, setSearchQuery] = useState("");
  const [selectedItemIndex, setSelectedItemIndex] = useState(0);

  const searchResult = useMemo(() => {
    return flattenedRoutes.filter(
      (item) =>
        t(item.label).toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.key.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery, t, flattenedRoutes]);

  // 在搜索結果變化時重置選中索引
  useEffect(() => {
    setSelectedItemIndex(0);
  }, [searchResult.length]);

  // 鍵盤事件處理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 打開搜索框 (Meta+K / Ctrl+K)
      if ((event.metaKey || event.ctrlKey) && event.key === "k") {
        event.preventDefault();
        handleOpen();
      }

      if (!search) return;

      if (event.key === "ArrowUp") {
        event.preventDefault();
        let nextIndex = selectedItemIndex - 1;
        if (nextIndex < 0) {
          nextIndex = searchResult.length - 1;
        }
        setSelectedItemIndex(nextIndex);
        scrollSelectedItemIntoView(nextIndex);
      }

      if (event.key === "ArrowDown") {
        event.preventDefault();
        let nextIndex = selectedItemIndex + 1;
        if (nextIndex > searchResult.length - 1) {
          nextIndex = 0;
        }
        setSelectedItemIndex(nextIndex);
        scrollSelectedItemIntoView(nextIndex);
      }

      if (event.key === "Enter" && searchResult.length > 0) {
        event.preventDefault();
        const selectItem = searchResult[selectedItemIndex].key;
        if (selectItem) {
          handleSelect(selectItem);
          handleCancel();
        }
      }

      if (event.key === "Escape") {
        handleCancel();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [search, selectedItemIndex, searchResult]);

  const handleOpen = () => {
    setSearch(true);
    setSearchQuery("");
    setSelectedItemIndex(0);
  };

  const handleCancel = () => {
    setSearch(false);
  };

  const handleAfterOpenChange = (open: boolean) => {
    if (open) {
      // auto focus
      inputRef.current?.focus();
    }
  };

  const scrollSelectedItemIntoView = (index: number) => {
    if (listRef.current) {
      const selectedItem = listRef.current.children[index];
      if (selectedItem instanceof HTMLElement) {
        selectedItem.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
      }
    }
  };

  const handleHover = (index: number) => {
    if (index === selectedItemIndex) return;
    setSelectedItemIndex(index);
  };

  const handleSelect = (key: string) => {
    replace(key);
    handleCancel();
  };

  // 高亮匹配文本
  const highlightMatch = (text: string, query: string) => {
    if (!query) return text;

    try {
      const regex = new RegExp(`(${query})`, "gi");
      return text.replace(regex, '<span class="text-primary">$1</span>');
    } catch (e) {
      return text;
    }
  };

  return (
    <>
      <div className="flex items-center justify-center">
        <button
          className="h-8 rounded-xl bg-hover py-2 text-xs font-bold"
          onClick={handleOpen}
        >
          <div className="flex items-center justify-center gap-2">
            <Icon library="lucide" name="Search" size={16} />
            <span className="flex h-6 items-center justify-center rounded-md bg-common-white px-1.5 font-bold text-gray-800">
              ⌘K
            </span>
          </div>
        </button>
      </div>
      <Modal
        centered
        keyboard
        open={search}
        onCancel={handleCancel}
        closeIcon={false}
        afterOpenChange={handleAfterOpenChange}
        styles={{
          body: {
            height: "400px",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
          },
        }}
        title={
          <Input
            ref={inputRef}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search..."
            variant="borderless"
            autoFocus
            prefix={<Icon library="lucide" name="Search" size={16} />}
            suffix={
              <button
                className="h-6 rounded-md bg-hover text-xs"
                onClick={handleCancel}
              >
                Esc
              </button>
            }
          />
        }
        footer={
          <div className="flex flex-wrap">
            <div className="flex">
              <Tag color="cyan">↑</Tag>
              <Tag color="cyan">↓</Tag>
              <span>to navigate</span>
            </div>
            <div className="flex">
              <Tag color="cyan">↵</Tag>
              <span>to select</span>
            </div>
            <div className="flex">
              <Tag color="cyan">ESC</Tag>
              <span>to close</span>
            </div>
          </div>
        }
      >
        {searchResult.length === 0 ? (
          <Empty />
        ) : (
          <Scrollbar>
            <div ref={listRef} className="py-2">
              {searchResult.map(({ key, label }, index) => {
                return (
                  <div
                    key={key}
                    className={cn(
                      `flex justify-between ${
                        index === selectedItemIndex ? "bg-hover" : ""
                      } cursor-pointer p-2`
                    )}
                    onClick={() => handleSelect(key)}
                    onMouseMove={() => handleHover(index)}
                  >
                    <div>
                      <div
                        className="font-medium"
                        dangerouslySetInnerHTML={{
                          __html: highlightMatch(t(label), searchQuery),
                        }}
                      />
                      <div
                        className="text-xs text-text-secondary"
                        dangerouslySetInnerHTML={{
                          __html: highlightMatch(key, searchQuery),
                        }}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </Scrollbar>
        )}
      </Modal>
    </>
  );
}
