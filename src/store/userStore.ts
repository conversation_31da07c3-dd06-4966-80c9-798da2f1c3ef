import { useMutation } from "@tanstack/react-query";
import { useNavigate } from "react-router";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

import type {
  CurrentUserResponse,
  LoginRequest,
  TokenResponse,
  UserPermissionsResponse,
} from "@/api/models/auth";
import { authService } from "@/api/services/authService";

import { toast } from "sonner";
import type { UserInfo, UserToken } from "#/entity";
import { PermissionType, StorageEnum } from "#/enum";

const { VITE_APP_HOMEPAGE: HOMEPAGE } = import.meta.env;

type UserStore = {
  userInfo: Partial<UserInfo>;
  userToken: UserToken;
  userPermissions: UserPermissionsResponse | null;
  // 使用 actions 命名空間來存放所有的 action
  actions: {
    setUserInfo: (userInfo: UserInfo) => void;
    setUserToken: (token: UserToken) => void;
    setUserPermissions: (permissions: UserPermissionsResponse) => void;
    clearUserData: () => void;
  };
};

const useUserStore = create<UserStore>()(
  persist(
    (set) => ({
      userInfo: {},
      userToken: {},
      userPermissions: null,
      actions: {
        setUserInfo: (userInfo) => {
          set({ userInfo });
        },
        setUserToken: (userToken) => {
          set({ userToken });
        },
        setUserPermissions: (userPermissions) => {
          set({ userPermissions });
        },
        clearUserData() {
          set({ userInfo: {}, userToken: {}, userPermissions: null });
        },
      },
    }),
    {
      name: "userStore", // name of the item in the storage (must be unique)
      storage: createJSONStorage(() => localStorage), // (optional) by default, 'localStorage' is used
      partialize: (state) => ({
        [StorageEnum.UserInfo]: state.userInfo,
        [StorageEnum.UserToken]: state.userToken,
        [StorageEnum.UserPermissions]: state.userPermissions,
      }),
    }
  )
);

export const useUserInfo = () => useUserStore((state) => state.userInfo);
export const useUserToken = () => useUserStore((state) => state.userToken);
export const useUserPermissions = () =>
  useUserStore((state) => state.userPermissions);
// export const useUserPermission = () =>
//   useUserStore((state) => state.userInfo.permissions);
export const useUserActions = () => useUserStore((state) => state.actions);

export const useSignIn = () => {
  const navigate = useNavigate();
  const { setUserToken, setUserInfo, setUserPermissions } = useUserActions();

  const signInMutation = useMutation({
    mutationFn: authService.login,
  });

  const getCurrentUserMutation = useMutation({
    mutationFn: authService.getCurrentUser,
  });

  const getUserPermissionsMutation = useMutation({
    mutationFn: authService.getUserPermissions,
  });

  const signIn = async (data: LoginRequest) => {
    try {
      // 登入並獲取令牌
      const tokenResponse = await signInMutation.mutateAsync(data);

      if (!tokenResponse || !tokenResponse.token) {
        throw new Error("登入回應格式不正確");
      }

      // 檢查用戶角色，只允許admin或super_admin角色登入
      if (
        tokenResponse.user.role !== "管理員" &&
        tokenResponse.user.role !== "超级管理員"
      ) {
        throw new Error("登入失敗：只允許管理員角色登入");
      }

      // 設置令牌
      setUserToken({
        accessToken: tokenResponse.token,
        refreshToken: "",
      });

      try {
        // 獲取用戶基本資訊
        // const userResponse = await getCurrentUserMutation.mutateAsync();

        // 獲取用戶權限
        // const permissionsResponse =
        //   await getUserPermissionsMutation.mutateAsync();

        // 保存完整的權限響應資料
        // setUserPermissions(permissionsResponse);

        // 將 API 用戶資訊轉換為 UserInfo
        const userInfo: UserInfo = {
          userId: tokenResponse.user._id,
          account: tokenResponse.user.account,
          password: tokenResponse.user.password,
          role: tokenResponse.user.role || "",
          isAdmin: tokenResponse.user.is_admin || false,
          isActive: tokenResponse.user.is_active || false,
          // permissions: permissionsResponse.permissions_list.flatMap((module) =>
          //   module.actions.map((action) => ({
          //     id: `${module.module_code}_${action}`,
          //     parentId: module.module_code,
          //     name: action,
          //     label: action,
          //     type: PermissionType.BUTTON,
          //     route: "",
          //   }))
          // ),
        };

        // 設置用戶信息
        setUserInfo(userInfo);

        // 導航到首頁
        navigate(HOMEPAGE, { replace: true });
        toast.success("登入成功！");
      } catch (userInfoErr) {
        console.error("獲取用戶信息失敗:", userInfoErr);
        // 即使獲取用戶信息失敗，仍然嘗試導航到首頁
        navigate(HOMEPAGE, { replace: true });
        toast.success("登入成功，但無法獲取完整用戶信息");
      }
    } catch (err) {
      console.error("登入失敗:", err);
      toast.error(err.message || "登入失敗，請稍後重試", {
        position: "top-center",
      });
    }
  };

  return signIn;
};

export default useUserStore;
