import { type MotionProps, m } from "framer-motion";
import type { ReactNode } from "react";

import { varContainer } from "./variants/container";
import { cn } from "@/utils";

interface Props extends MotionProps {
  className?: string;
  children?: ReactNode;
}

/**
 * Motion 容器组件
 */
export default function MotionContainer({
  animate,
  initial = false,
  variants,
  className = "",
  children,
  ...other
}: Props) {
  if (!animate) {
    return <div className={cn(className)}>{children}</div>;
  }

  return (
    <m.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={variants || varContainer()}
      className={cn(className)}
      {...other}
    >
      {children}
    </m.div>
  );
}
