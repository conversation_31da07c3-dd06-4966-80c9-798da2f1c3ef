import type { UserResponse } from "@/api/models/users";
import { userService } from "@/api/services/userService";
import { CloseCircleOutlined, SearchOutlined } from "@ant-design/icons";
import { Button, Empty, Form, Input, Modal, Space, Table } from "antd";
import type React from "react";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import Icon from "../icon";

interface UserSelectProps {
  value?: string | null;
  valueField?: string;
  onChange?: (value: string | null) => void;
  placeholder?: string;
}

const UserSelect: React.FC<UserSelectProps> = ({
  value,
  valueField = "id",
  onChange,
  placeholder = "選擇使用者",
}) => {
  const { t } = useTranslation();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [users, setUsers] = useState<UserResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [userSearchForm] = Form.useForm();
  const [userData, setUserData] = useState<UserResponse | null>(null);

  // 獲取使用者詳細資訊
  const fetchUserDetails = useCallback(
    async (value: string) => {
      try {
        setLoading(true);
        // 先從已載入的使用者列表中查找
        const cachedUser = users.find((user) => user[valueField] === value);

        if (cachedUser) {
          setUserData(cachedUser);
        } else {
          // 如果本地沒有找到，則通過 API 獲取使用者詳細信息
          // 根據 valueField 決定如何查詢使用者
          let response = null;
          if (valueField === "id") {
            // 如果是 id，直接透過 ID 獲取使用者
            response = await userService.getUser(value);
          } else {
            // 如果不是 id，則使用搜索功能查詢
            // 由於 API 可能不支援精確匹配和指定欄位，因此這裡使用模糊搜索後再過濾
            const searchResponse = await userService.getUsers({
              [valueField]: value,
              limit: 100, // 獲取較多記錄以提高找到的可能性
            });

            // 在返回的記錄中尋找精確匹配的項目
            const exactMatch = searchResponse.items?.find(
              (item) => item[valueField] === value
            );
            response = exactMatch || null;
          }
          setUserData(response || null);
        }
      } catch (error) {
        console.error("獲取使用者詳細資訊失敗:", error);
        setUserData(null);
      } finally {
        setLoading(false);
      }
    },
    [users, valueField]
  );

  // 處理value值變更
  useEffect(() => {
    if (value) {
      fetchUserDetails(value);
    } else {
      setUserData(null);
    }
  }, [value, fetchUserDetails]);

  // 獲取使用者列表
  const fetchUsers = useCallback(
    async (params?: any) => {
      try {
        setLoading(true);
        const response = await userService.getUsers({
          page: currentPage,
          limit: pageSize,
          ...params,
        });
        setUsers(response.items || []);
        setTotal(response.total_count || 0);

        // 在設置完使用者列表後再查詢當前選中的使用者
        if (value) {
          const currentUser = response.items?.find(
            (user) => user[valueField] === value
          );
          if (currentUser) {
            setUserData(currentUser);
          }
        }
      } catch (error) {
        console.error("獲取使用者列表失敗:", error);
      } finally {
        setLoading(false);
      }
    },
    [currentPage, pageSize, value, valueField]
  );

  useEffect(() => {
    if (isModalVisible) {
      fetchUsers();
    }
  }, [fetchUsers, isModalVisible, currentPage, pageSize]);

  // 處理搜索
  const handleSearch = async () => {
    const values = await userSearchForm.validateFields();
    const params = {
      account: values.account,
      user_name: values.user_name,
      department: values.department,
    };
    setCurrentPage(1);
    fetchUsers(params);
  };

  // 處理重置
  const handleReset = () => {
    userSearchForm.resetFields();
    setCurrentPage(1);
    fetchUsers();
  };

  // 打開模態框
  const showModal = () => {
    setIsModalVisible(true);
  };

  // 關閉模態框
  const handleCancel = () => {
    setIsModalVisible(false);
  };

  // 選擇使用者
  const handleSelectUser = (record: UserResponse) => {
    setUserData(record);
    if (onChange) {
      onChange(record[valueField]);
    }
    setIsModalVisible(false);
  };

  // 清空選擇
  const handleClearSelection = () => {
    setUserData(null);
    if (onChange) {
      onChange(null);
    }
  };

  // 表格列配置
  const columns = [
    {
      title: "員工編號",
      dataIndex: "employee_no",
      key: "employee_no",
    },
    {
      title: "使用者名稱",
      dataIndex: "user_name",
      key: "user_name",
    },
    {
      title: "部門",
      dataIndex: "department",
      key: "department",
    },
    {
      title: "職位",
      dataIndex: "position",
      key: "position",
    },
    {
      title: "操作",
      key: "action",
      render: (_: any, record: UserResponse) => (
        <Button
          type="primary"
          size="small"
          onClick={() => handleSelectUser(record)}
        >
          {t("common.basic.select")}
        </Button>
      ),
    },
  ];

  // 顯示選擇的使用者信息
  const getSelectedUserDisplay = () => {
    if (!userData) return "";
    return `${userData.employee_no} | ${userData.user_name}`;
  };

  return (
    <div className="user-select w-full">
      <div className="flex items-center w-full">
        <Button
          onClick={showModal}
          className="w-full px-[11px] flex items-center justify-start min-w-[120px]"
        >
          {userData ? (
            getSelectedUserDisplay()
          ) : (
            <div className="flex justify-between items-center relative w-full font-normal">
              <span className="text-gray-400">{placeholder}</span>
              <Icon
                library="lucide"
                name="User"
                size={16}
                className="absolute right-0 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
            </div>
          )}
        </Button>
        {userData && (
          <Button
            type="text"
            icon={<CloseCircleOutlined />}
            onClick={handleClearSelection}
            title={t("common.basic.delete")}
          />
        )}
      </div>

      <Modal
        title="選擇使用者"
        open={isModalVisible}
        onCancel={handleCancel}
        width={900}
        footer={null}
      >
        <Form
          form={userSearchForm}
          id="user-search-form"
          layout="inline"
          onFinish={handleSearch}
          className="grid grid-cols-1 md:grid-cols-3 gap-4"
          style={{ marginBottom: "16px" }}
        >
          <Form.Item name="account" label={t("common.form.inputAccount")}>
            <Input placeholder={t("common.form.inputAccount")} allowClear />
          </Form.Item>
          <Form.Item name="user_name" label={t("common.form.inputName")}>
            <Input placeholder={t("common.form.inputName")} allowClear />
          </Form.Item>
          <Form.Item name="department" label={t("common.form.inputDepartment")}>
            <Input placeholder={t("common.form.inputDepartment")} allowClear />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SearchOutlined />}
              >
                {t("common.basic.search")}
              </Button>
              <Button onClick={handleReset}>{t("common.basic.reset")}</Button>
            </Space>
          </Form.Item>
        </Form>
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("documents.common.emptyData")}
              />
            ),
          }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              if (pageSize) setPageSize(pageSize);
            },
            showTotal: (total) =>
              `${t("common.table.total")} ${total} ${t("common.table.unit")}`,
          }}
        />
      </Modal>
    </div>
  );
};

export default UserSelect;
