import Icon from "@/components/icon";
import { Button, Empty, Input, List, Modal, Space, Spin } from "antd";
import type React from "react";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import type { ResourceResponse, ResourceType } from "@/api/models/resources";
import { resourcesService } from "@/api/services/resourcesService";

export interface ResourceSelectProps {
  /**
   * 資源類型過濾
   */
  resourceType?: ResourceType;
  /**
   * 是否顯示模態框
   */
  visible: boolean;
  /**
   * 關閉模態框的回調函數
   */
  onClose: () => void;
  /**
   * 選擇資源的回調函數
   */
  onSelect: (resource: ResourceResponse) => void;
  /**
   * 模態框標題
   */
  title?: string;
}

const ResourceSelect: React.FC<ResourceSelectProps> = ({
  resourceType,
  visible,
  onClose,
  onSelect,
  title,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [resources, setResources] = useState<ResourceResponse[]>([]);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [filteredResources, setFilteredResources] = useState<
    ResourceResponse[]
  >([]);

  // 獲取資源列表
  const fetchResources = useCallback(async () => {
    try {
      setLoading(true);
      const response = await resourcesService.getAllResources({
        resource_type: resourceType,
      });

      // 處理API返回
      if (response?.resources) {
        // 處理id與_id的差異，確保資源物件有id屬性
        const resourcesWithId = response.resources.map((resource) => ({
          ...resource,
          id: resource._id,
        }));
        setResources(resourcesWithId);
        setFilteredResources(resourcesWithId);
      } else {
        setResources([]);
        setFilteredResources([]);
      }
    } catch (error) {
      console.error("Failed to fetch resources:", error);
    } finally {
      setLoading(false);
    }
  }, [resourceType]);

  // 當顯示模態框或資源類型變更時，獲取資源列表
  useEffect(() => {
    if (visible) {
      fetchResources();
    }
  }, [visible, fetchResources]);

  // 處理搜索
  useEffect(() => {
    if (searchKeyword.trim() === "") {
      setFilteredResources(resources);
    } else {
      const keyword = searchKeyword.toLowerCase();
      const filtered = resources.filter(
        (resource) =>
          resource.title.toLowerCase().includes(keyword) ||
          resource.resource_group?.toLowerCase().includes(keyword) ||
          resource.url?.toLowerCase().includes(keyword)
      );
      setFilteredResources(filtered);
    }
  }, [searchKeyword, resources]);

  return (
    <Modal
      title={title || t("notification.send_page.select_resource")}
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          {t("notification.send_page.close")}
        </Button>,
      ]}
      width={700}
    >
      <Space direction="vertical" style={{ width: "100%" }} size="middle">
        <Input
          placeholder={t("common.basic.search")}
          value={searchKeyword}
          onChange={(e) => setSearchKeyword(e.target.value)}
          prefix={<Icon library="lucide" name="Search" size={16} />}
          allowClear
        />

        <Spin spinning={loading}>
          <List
            itemLayout="horizontal"
            dataSource={filteredResources}
            renderItem={(item) => (
              <List.Item
                actions={[
                  <Button
                    key="select"
                    type="primary"
                    onClick={() => onSelect(item)}
                  >
                    {t("notification.send_page.insert")}
                  </Button>,
                ]}
              >
                <List.Item.Meta
                  avatar={
                    <Icon
                      library="lucide"
                      name={
                        item.resource_type === "video" ? "Video" : "FileText"
                      }
                      size={24}
                    />
                  }
                  title={`${item.title} (${
                    t(
                      `notification.resources_page.resource_groups.${item.resource_group}`
                    ) || ""
                  })`}
                  description={<div>{item.url}</div>}
                />
              </List.Item>
            )}
            locale={{
              emptyText: (
                <Empty
                  image={<Icon library="lucide" name="FileX" size={40} />}
                  description={t("notification.send_page.no_resources")}
                />
              ),
            }}
          />
        </Spin>
      </Space>
    </Modal>
  );
};

export default ResourceSelect;
