import Icon from "@/components/icon";
import { Button, Empty, Input, List, Modal, Space, Spin } from "antd";
import type React from "react";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import type {
  ChannelType,
  NotificationTemplateResponse,
} from "@/api/models/notification-templates";
import { notificationTemplatesService } from "@/api/services/notificationTemplatesService";

export interface TemplateSelectProps {
  /**
   * 當前選擇的通知渠道類型
   */
  channelType?: ChannelType;
  /**
   * 是否顯示模態框
   */
  visible: boolean;
  /**
   * 關閉模態框的回調函數
   */
  onClose: () => void;
  /**
   * 選擇模板的回調函數
   */
  onSelect: (template: NotificationTemplateResponse) => void;
  /**
   * 模態框標題
   */
  title?: string;
}

const TemplateSelect: React.FC<TemplateSelectProps> = ({
  channelType,
  visible,
  onClose,
  onSelect,
  title,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState<NotificationTemplateResponse[]>(
    []
  );
  const [searchKeyword, setSearchKeyword] = useState("");
  const [filteredTemplates, setFilteredTemplates] = useState<
    NotificationTemplateResponse[]
  >([]);

  // 獲取模板列表
  const fetchTemplates = useCallback(async () => {
    try {
      setLoading(true);
      const response = await notificationTemplatesService.getAllTemplates({
        channels: channelType,
      });

      // 使用類型斷言處理 API 返回結構
      if (response?.templates) {
        setTemplates(response.templates);
        setFilteredTemplates(response.templates);
      } else {
        setTemplates([]);
        setFilteredTemplates([]);
      }
    } catch (error) {
      console.error("Failed to fetch templates:", error);
    } finally {
      setLoading(false);
    }
  }, [channelType]);

  // 當顯示模態框或頻道類型變更時，獲取模板列表
  useEffect(() => {
    if (visible) {
      fetchTemplates();
    }
  }, [visible, fetchTemplates]);

  // 處理搜索
  useEffect(() => {
    if (searchKeyword.trim() === "") {
      setFilteredTemplates(templates);
    } else {
      const keyword = searchKeyword.toLowerCase();
      const filtered = templates.filter(
        (template) =>
          template.name.toLowerCase().includes(keyword) ||
          template.subject?.toLowerCase().includes(keyword) ||
          template.content.toLowerCase().includes(keyword)
      );
      setFilteredTemplates(filtered);
    }
  }, [searchKeyword, templates]);

  return (
    <Modal
      title={title || t("notification.send_page.select_template")}
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          {t("notification.send_page.close")}
        </Button>,
      ]}
      width={700}
    >
      <Space direction="vertical" style={{ width: "100%" }} size="middle">
        <Input
          placeholder={t("common.basic.search")}
          value={searchKeyword}
          onChange={(e) => setSearchKeyword(e.target.value)}
          prefix={<Icon library="lucide" name="Search" size={16} />}
          allowClear
        />

        <Spin spinning={loading}>
          <List
            itemLayout="horizontal"
            dataSource={filteredTemplates}
            renderItem={(item) => (
              <List.Item
                actions={[
                  <Button
                    key="select"
                    type="primary"
                    onClick={() => onSelect(item)}
                  >
                    {t("notification.send_page.select")}
                  </Button>,
                ]}
              >
                <List.Item.Meta
                  avatar={<Icon library="lucide" name="FileText" size={24} />}
                  title={item.name}
                  description={
                    <div
                      style={{
                        maxHeight: "100px",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                      }}
                    >
                      {item.content && item.content.length > 100
                        ? `${item.content.substring(0, 100)}...`
                        : item.content}
                    </div>
                  }
                />
              </List.Item>
            )}
            locale={{
              emptyText: (
                <Empty
                  image={<Icon library="lucide" name="FileX" size={40} />}
                  description={t("notification.send_page.no_templates")}
                />
              ),
            }}
          />
        </Spin>
      </Space>
    </Modal>
  );
};

export default TemplateSelect;
