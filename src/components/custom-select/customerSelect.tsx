import type { CustomerRead } from "@/api/models/customer";
import { customerService } from "@/api/services/customerService";
import { CloseCircleOutlined, SearchOutlined } from "@ant-design/icons";
import { Button, Empty, Form, Input, Modal, Space, Table } from "antd";
import type React from "react";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import Icon from "../icon";

interface CustomerSelectProps {
  value?: string | null;
  valueField?: string;
  fullData?: boolean;
  onChange?: (value: any) => void;
  placeholder?: string;
}

const CustomerSelect: React.FC<CustomerSelectProps> = ({
  value,
  valueField = "uuid",
  fullData = false,
  onChange,
  placeholder = "選擇客戶",
}) => {
  const { t } = useTranslation();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [customers, setCustomers] = useState<CustomerRead[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchForm] = Form.useForm();
  const [customerData, setCustomerData] = useState<CustomerRead | null>(null);

  // 獲取客戶詳細資訊
  const fetchCustomerDetails = useCallback(
    async (value: string) => {
      try {
        setLoading(true);
        // 先從已載入的客戶列表中查找
        const cachedCustomer = customers.find(
          (customer) => customer[valueField] === value
        );

        if (cachedCustomer) {
          setCustomerData(cachedCustomer);
        } else {
          // 如果本地沒有找到，則通過 API 獲取客戶詳細信息
          // 根據 valueField 決定如何查詢客戶
          let response = null;
          if (valueField === "uuid") {
            // 如果是 uuid，直接透過 ID 獲取客戶
            response = await customerService.getCustomer(value);
          } else {
            // 如果不是 uuid，則使用搜索功能查詢
            // 由於 API 不支援精確匹配和指定欄位，因此這裡使用模糊搜索後再過濾
            const searchResponse = await customerService.getAllCustomers({
              search: value,
              limit: 100, // 獲取較多記錄以提高找到的可能性
            });

            // 在返回的記錄中尋找精確匹配的項目
            const exactMatch = searchResponse.items?.find(
              (item) => item[valueField] === value
            );
            response = exactMatch || null;
          }
          setCustomerData(response || null);
        }
      } catch (error) {
        console.error("獲取客戶詳細資訊失敗:", error);
        setCustomerData(null);
      } finally {
        setLoading(false);
      }
    },
    [customers, valueField]
  );

  // 處理value值變更
  useEffect(() => {
    if (value) {
      fetchCustomerDetails(value);
    } else {
      setCustomerData(null);
    }
  }, [value, fetchCustomerDetails]);

  // 獲取客戶列表
  const fetchCustomers = useCallback(
    async (params?: any) => {
      try {
        setLoading(true);
        const response = await customerService.getAllCustomers({
          page: currentPage,
          limit: pageSize,
          search: params?.search,
        });
        setCustomers(response.items || []);
        setTotal(response.total || 0);

        // 在設置完客戶列表後再查詢當前選中的客戶
        if (value) {
          const currentCustomer = response.items?.find(
            (customer) => customer[valueField] === value
          );
          setCustomerData(currentCustomer || null);
        }
      } catch (error) {
        console.error("獲取客戶列表失敗:", error);
      } finally {
        setLoading(false);
      }
    },
    [currentPage, pageSize, value, valueField]
  );

  useEffect(() => {
    if (isModalVisible) {
      fetchCustomers();
    }
  }, [fetchCustomers, isModalVisible]);

  // 處理搜索
  const handleSearch = async () => {
    const values = await searchForm.validateFields();

    setCurrentPage(1);
    fetchCustomers({ search: values.search || "" });
  };

  // 處理重置
  const handleReset = () => {
    searchForm.resetFields();
    setCurrentPage(1);
    fetchCustomers();
  };

  // 打開模態框
  const showModal = () => {
    setIsModalVisible(true);
  };

  // 關閉模態框
  const handleCancel = () => {
    setIsModalVisible(false);
  };

  // 選擇客戶
  const handleSelectCustomer = (record: CustomerRead) => {
    setCustomerData(record);
    if (onChange) {
      onChange(fullData ? record : record[valueField]);
    }
    setIsModalVisible(false);
  };

  // 清空選擇
  const handleClearSelection = () => {
    setCustomerData(null);
    if (onChange) {
      onChange(null);
    }
  };

  // 表格列配置
  const columns = [
    {
      title: "客戶編號",
      dataIndex: "customer_number",
      key: "customer_number",
    },
    {
      title: "客戶名稱",
      dataIndex: "customer_name",
      key: "customer_name",
    },
    {
      title: "地址",
      dataIndex: "address",
      key: "address",
      ellipsis: true,
    },
    {
      title: "聯絡電話",
      dataIndex: "telephone",
      key: "telephone",
    },
    {
      title: "操作",
      key: "action",
      render: (_: any, record: CustomerRead) => (
        <Button
          type="primary"
          size="small"
          onClick={() => handleSelectCustomer(record)}
        >
          {t("common.basic.select")}
        </Button>
      ),
    },
  ];

  // 顯示選擇的客戶信息
  const getSelectedCustomerDisplay = () => {
    if (!customerData) return "";
    return `${customerData.customer_number} | ${customerData.customer_name}`;
  };

  return (
    <div className="customer-select w-full">
      <div className="flex items-center w-full">
        <Button
          onClick={showModal}
          className="w-full px-[11px] flex items-center justify-start min-w-[120px]"
        >
          {customerData ? (
            getSelectedCustomerDisplay()
          ) : (
            <div className="flex justify-between items-center relative w-full font-normal">
              <span className="text-gray-400">{placeholder}</span>
              <Icon
                library="lucide"
                name="User"
                size={16}
                className="absolute right-0 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
            </div>
          )}
        </Button>
        {customerData && (
          <Button
            type="text"
            icon={<CloseCircleOutlined />}
            onClick={handleClearSelection}
            title={t("common.basic.delete")}
          />
        )}
      </div>

      <Modal
        title="選擇客戶"
        open={isModalVisible}
        onCancel={handleCancel}
        width={900}
        footer={null}
      >
        <Form
          form={searchForm}
          id="customer-search-form"
          layout="inline"
          onFinish={handleSearch}
          style={{ marginBottom: "16px" }}
        >
          <Form.Item name="search" label="模糊檢索">
            <Input placeholder="請輸入任意文字" allowClear />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SearchOutlined />}
              >
                {t("common.basic.search")}
              </Button>
              <Button onClick={handleReset}>{t("common.basic.reset")}</Button>
            </Space>
          </Form.Item>
        </Form>
        <Table
          columns={columns}
          dataSource={customers}
          rowKey="uuid"
          loading={loading}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("documents.common.emptyData")}
              />
            ),
          }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              if (pageSize) setPageSize(pageSize);
            },
            showTotal: (total) =>
              `${t("common.table.total")} ${total} ${t("common.table.unit")}`,
            showSizeChanger: true,
          }}
          size="middle"
        />
      </Modal>
    </div>
  );
};

export default CustomerSelect;
