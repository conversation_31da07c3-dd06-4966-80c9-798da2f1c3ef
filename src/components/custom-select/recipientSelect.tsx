import { Button, Empty, Form, Modal, Table, Tag, message } from "antd";
import type React from "react";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import type { ContactTypeEnum, SendUserResponse } from "../../api/models/send";
import { sendService } from "../../api/services/sendService";
import Icon from "../icon";

interface RecipientSelectProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  contactType: ContactTypeEnum;
  placeholder?: string;
  disabled?: boolean;
}

const RecipientSelect: React.FC<RecipientSelectProps> = ({
  value = [],
  onChange,
  contactType,
  placeholder = "選擇收件人",
  disabled = false,
}) => {
  const { t } = useTranslation();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [users, setUsers] = useState<SendUserResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchForm] = Form.useForm();
  const [selectedUserData, setSelectedUserData] = useState<SendUserResponse[]>(
    []
  );
  const [tempSelectedRecipients, setTempSelectedRecipients] = useState<
    string[]
  >([]);

  // 使用 ref 來追蹤上一次的 value
  const prevValueRef = useRef<string>();
  const currentValueString = JSON.stringify(value || []);

  // 處理value值變更
  useEffect(() => {
    // 只有當 value 真正改變時才執行
    if (prevValueRef.current === currentValueString) {
      return;
    }
    prevValueRef.current = currentValueString;

    const fetchData = async () => {
      if (!value || value.length === 0) {
        setSelectedUserData([]);
        return;
      }

      try {
        setLoading(true);
        // 由於API限制，我們需要搜索來找到匹配的使用者
        const response = await sendService.getUsers({
          contact_type: contactType,
          limit: 100, // 增加限制以獲取更多結果
        });

        // 過濾出匹配的使用者
        const matchedUsers = response.data.filter((user) =>
          value.includes(user.recipient)
        );
        setSelectedUserData(matchedUsers);
      } catch (error) {
        console.error("獲取使用者詳細資訊失敗:", error);
        setSelectedUserData([]);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [currentValueString, contactType]); // 使用字串化的值作為依賴
  useEffect(() => {
    if (isModalVisible) {
      // 當模態框打開時，重新載入使用者列表
      const loadUsers = async () => {
        try {
          setLoading(true);
          const response = await sendService.getUsers({
            contact_type: contactType,
            page: currentPage,
            limit: pageSize,
          });
          setUsers(response.data || []);
          setTotal(response.total || 0);
        } catch (error) {
          console.error("獲取使用者列表失敗:", error);
          message.error("獲取使用者列表失敗");
        } finally {
          setLoading(false);
        }
      };

      loadUsers();
    }
  }, [isModalVisible, currentPage, pageSize, contactType]); // 移除設置臨時選中收件人的邏輯
  // 單獨處理設置臨時選中的收件人
  useEffect(() => {
    if (isModalVisible) {
      setTempSelectedRecipients([...value]);
    }
  }, [isModalVisible]); // 移除 value 依賴，只在模態框打開時設置一次
  // 處理搜索
  const handleSearch = async () => {
    try {
      setCurrentPage(1);
      // 搜索時觸發重新加載
    } catch (error) {
      console.error("搜索失敗:", error);
    }
  };

  // 處理重置
  const handleReset = () => {
    searchForm.resetFields();
    setCurrentPage(1);
    // 重置時觸發重新加載
  };

  // 打開模態框
  const showModal = () => {
    if (disabled) return;
    setIsModalVisible(true);
  };

  // 關閉模態框
  const handleCancel = () => {
    setIsModalVisible(false);
    // 恢復原始選擇
    setTempSelectedRecipients([...value]);
  };

  // 確認選擇
  const handleConfirm = () => {
    setIsModalVisible(false);
    if (onChange) {
      onChange(tempSelectedRecipients);
    }
  };
  // 處理單個收件人選擇/取消選擇
  const handleToggleRecipient = (recipient: string) => {
    setTempSelectedRecipients((prev) => {
      if (prev.includes(recipient)) {
        return prev.filter((r) => r !== recipient);
      }
      return [...prev, recipient];
    });
  };

  // 清空所有選擇
  const handleClearAll = () => {
    setSelectedUserData([]);
    if (onChange) {
      onChange([]);
    }
  };

  // 移除單個選中項
  const handleRemoveItem = (recipient: string) => {
    const newValue = value.filter((r) => r !== recipient);
    if (onChange) {
      onChange(newValue);
    }
  };

  // 表格列配置
  const columns = [
    {
      title: "使用者名稱",
      dataIndex: "user_name",
      key: "user_name",
    },
    {
      title: contactType === "email" ? "電子郵件" : "LINE ID",
      dataIndex: "recipient",
      key: "recipient",
    },
    {
      title: "客戶稅籍編號",
      dataIndex: "customer_tax_id",
      key: "customer_tax_id",
    },
    {
      title: "客戶編號",
      dataIndex: "customer_number",
      key: "customer_number",
    },
    {
      title: "操作",
      key: "action",
      render: (_: any, record: SendUserResponse) => {
        const isSelected = tempSelectedRecipients.includes(record.recipient);
        return (
          <Button
            type={isSelected ? "default" : "primary"}
            size="small"
            onClick={() => handleToggleRecipient(record.recipient)}
          >
            {isSelected ? "取消選擇" : t("common.basic.select")}
          </Button>
        );
      },
    },
  ];

  // 表格行選擇配置
  const rowSelection = {
    selectedRowKeys: tempSelectedRecipients,
    onSelect: (record: SendUserResponse, selected: boolean) => {
      handleToggleRecipient(record.recipient);
    },
    onSelectAll: (
      selected: boolean,
      selectedRows: SendUserResponse[],
      changeRows: SendUserResponse[]
    ) => {
      if (selected) {
        const newRecipients = changeRows.map((row) => row.recipient);
        setTempSelectedRecipients((prev) => [...prev, ...newRecipients]);
      } else {
        const removeRecipients = changeRows.map((row) => row.recipient);
        setTempSelectedRecipients((prev) =>
          prev.filter((r) => !removeRecipients.includes(r))
        );
      }
    },
    getCheckboxProps: (record: SendUserResponse) => ({
      name: record.recipient,
    }),
  };

  // 顯示選擇的收件人信息
  const renderSelectedUsers = () => {
    if (!selectedUserData.length) {
      return (
        <Button
          onClick={showModal}
          disabled={disabled}
          style={{ minWidth: "200px", textAlign: "left" }}
        >
          {placeholder}
        </Button>
      );
    }

    return (
      <div
        style={{
          border: "1px solid #d9d9d9",
          borderRadius: "6px",
          padding: "4px 11px",
          minHeight: "32px",
        }}
      >
        <div
          style={{
            display: "flex",
            flexWrap: "wrap",
            gap: "4px",
            alignItems: "center",
          }}
        >
          {selectedUserData.map((user) => (
            <Tag
              key={user.recipient}
              closable
              onClose={() => handleRemoveItem(user.recipient)}
              style={{ margin: "2px 0" }}
              className="flex items-center gap-x-2"
            >
              <Icon
                library="lucide"
                name={contactType === "email" ? "Mail" : "MessageCircle"}
                size={16}
              />
              {user.user_name} ({user.recipient})
            </Tag>
          ))}
          <Button
            type="text"
            size="small"
            onClick={showModal}
            disabled={disabled}
            style={{ padding: "0 4px", height: "auto" }}
          >
            + 添加更多
          </Button>
        </div>
        {selectedUserData.length > 0 && (
          <div style={{ marginTop: "4px", textAlign: "right" }}>
            <Button
              type="text"
              size="small"
              onClick={handleClearAll}
              disabled={disabled}
              style={{ padding: "0", height: "auto", color: "#ff4d4f" }}
            >
              清空全部
            </Button>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="recipient-select">
      {renderSelectedUsers()}

      <Modal
        title={`選擇收件人 (${contactType === "email" ? "電子郵件" : "LINE"})`}
        open={isModalVisible}
        onCancel={handleCancel}
        onOk={handleConfirm}
        width={1000}
        okText="確認選擇"
        cancelText="取消"
      >
        <Form
          form={searchForm}
          id="recipient-search-form"
          layout="inline"
          onFinish={handleSearch}
          style={{ marginBottom: "16px" }}
        >
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              {t("common.basic.search")}
            </Button>
          </Form.Item>
        </Form>
        <div style={{ marginBottom: "16px" }}>
          <span>已選擇 {tempSelectedRecipients.length} 個收件人</span>
        </div>
        <Table
          columns={columns}
          dataSource={users}
          rowKey="recipient"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              if (pageSize) setPageSize(pageSize);
            },
            showTotal: (total) =>
              `${t("common.table.total")} ${total} ${t("common.table.unit")}`,
          }}
          locale={{
            emptyText: (
              <Empty
                image={<Icon library="lucide" name="ArchiveX" size={40} />}
                description={t("documents.common.emptyData")}
              />
            ),
          }}
        />
      </Modal>
    </div>
  );
};

export default RecipientSelect;
