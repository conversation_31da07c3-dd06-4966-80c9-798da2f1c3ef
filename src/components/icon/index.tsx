import { cn } from "@/utils";
import * as AntIcons from "@ant-design/icons";
import { Icon as IconifyIcon } from "@iconify/react";
import * as LucideIcons from "lucide-react";
import type React from "react";

export type IconLibrary = "ant" | "iconify" | "lucide";

export interface IconProps {
  /**
   * 圖示庫類型
   */
  library?: IconLibrary;
  /**
   * 圖示名稱
   * - ant: 如 'HomeOutlined'
   * - iconify: 如 'mdi:home'
   * - lucide: 如 'Home'
   */
  name: string;
  /**
   * 圖示大小
   */
  size?: number | string;
  /**
   * 圖示顏色
   */
  color?: string;
  /**
   * 旋轉角度
   */
  rotate?: number;
  /**
   * 是否旋轉動畫
   */
  spin?: boolean;
  /**
   * 額外的 className
   */
  className?: string;
  /**
   * 額外的樣式
   */
  style?: React.CSSProperties;
  /**
   * 點擊事件
   */
  onClick?: (event: React.MouseEvent<Element>) => void;
}

/**
 * 通用圖示組件，支援 Ant Design Icons、Iconify 和 Lucide React
 */
const Icon: React.FC<IconProps> = ({
  library = "lucide",
  name,
  size,
  color,
  rotate,
  spin = false,
  className = "",
  style = {},
  onClick,
}) => {
  // 計算樣式
  const mergedStyle: React.CSSProperties = {
    fontSize: typeof size === "number" ? `${size}px` : size,
    color,
    transform: rotate ? `rotate(${rotate}deg)` : undefined,
    ...style,
  };

  // 根據不同庫渲染圖示
  const renderIcon = () => {
    if (library === "ant") {
      // Ant Design Icons
      const AntIcon = (AntIcons as Record<string, any>)[name];
      if (!AntIcon) {
        console.warn(`Ant Design Icon "${name}" not found`);
        return null;
      }
      return (
        <AntIcon
          style={mergedStyle}
          spin={spin}
          className={cn(className)}
          onClick={onClick}
        />
      );
    }

    if (library === "iconify") {
      // Iconify
      return (
        <IconifyIcon
          icon={name}
          style={mergedStyle}
          className={cn(className)}
          onClick={onClick}
        />
      );
    }

    if (library === "lucide") {
      // Lucide React
      const LucideIcon = (LucideIcons as Record<string, any>)[name];
      if (!LucideIcon) {
        console.warn(`Lucide Icon "${name}" not found`);
        return null;
      }
      const lucideSize = size || 24;
      return (
        <LucideIcon
          size={lucideSize}
          color={color}
          className={cn(className)}
          style={{ ...mergedStyle, fontSize: undefined }}
          onClick={onClick}
        />
      );
    }

    console.warn(`Unsupported icon library: ${library}`);
    return null;
  };

  return renderIcon();
};

export default Icon;
