import { Dropdown } from "antd";
import useLocale, { LANGUAGE_MAP } from "@/locales/useLocale";
import { Languages } from "lucide-react";
import type { MenuProps } from "antd";
import type { LocalEnum } from "#/enum";

type Locale = keyof typeof LocalEnum;

/**
 * Locale Picker
 */
export default function LocalePicker() {
  const { setLocale, locale } = useLocale();

  const localeList: MenuProps["items"] = Object.values(LANGUAGE_MAP).map(
    (item) => {
      return {
        key: item.locale,
        label: item.label,
        icon: <Languages className="w-5 h-5" />,
      };
    },
  );

  return (
    <Dropdown
      placement="bottomRight"
      trigger={["click"]}
      menu={{ items: localeList, onClick: (e) => setLocale(e.key as Locale) }}
    >
      <button className="h-10 w-10 rounded-md hover:scale-105 transition-transform flex items-center justify-center">
        <Languages className="w-5 h-5" />
      </button>
    </Dropdown>
  );
}