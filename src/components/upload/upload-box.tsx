import type { UploadProps } from "antd";
import Dragger from "antd/es/upload/Dragger";
import type { ReactElement } from "react";

interface Props extends UploadProps {
  placeholder?: ReactElement;
}
export function UploadBox({ placeholder, ...other }: Props) {
  return (
    <div className="[&_.ant-upload]:!border-none [&_.ant-upload-list]:hidden">
      <Dragger {...other} showUploadList={false}>
        <div className="opacity-60 hover:opacity-50">
          {placeholder || (
            <div className="m-auto flex h-16 w-16 items-center justify-center ">
              <div>↑</div>
            </div>
          )}
        </div>
      </Dragger>
    </div>
  );
}
