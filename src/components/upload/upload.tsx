import { cn } from "@/utils";
import { Upload as AntdUpload, Typography } from "antd";
import type { UploadProps } from "antd";
import type { ItemRender } from "antd/es/upload/interface";
import UploadListItem from "./upload-list-item";

const { Dragger } = AntdUpload;
const { Text, Title } = Typography;

interface Props extends UploadProps {
  thumbnail?: boolean;
}

const itemRender: (thumbnail: boolean) => ItemRender = (thumbnail) => {
  return function temp(...args) {
    const [, file, , actions] = args;
    return (
      <UploadListItem file={file} actions={actions} thumbnail={thumbnail} />
    );
  };
};
import { useTranslation } from "react-i18next";

export function Upload({ thumbnail = false, ...other }: Props) {
  const { t } = useTranslation();

  return (
    <div className={cn(thumbnail ? "flex flex-wrap" : "")}>
      <Dragger
        {...other}
        itemRender={itemRender(thumbnail)}
        className="[&_.ant-upload]:!border-none"
      >
        <div className="opacity-100 hover:opacity-80">
          <p className="m-auto max-w-[200px]"></p>
          <Typography>
            <Title level={5} className="mt-4">
              {t("common.file.clickOrDragToUpload")}
            </Title>
            <Text type="secondary">{t("common.file.supportSingleUpload")}</Text>
          </Typography>
        </div>
      </Dragger>
    </div>
  );
}
