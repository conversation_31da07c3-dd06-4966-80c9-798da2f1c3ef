import { Link } from "react-router";
import { Typography } from "antd";

interface LogoProps {
  size?: number;
}

export default function Logo({ size = 30 }: LogoProps) {
  const boxSize = `${size}px`;
  const fontSize = `${Math.round(size * 0.6)}px`;

  return (
    <Link to="/" className="flex items-center no-underline">
      <div className="flex items-center">
        <div
          className="rounded-md bg-primary flex items-center justify-center"
          style={{ width: boxSize, height: boxSize }}
        >
          <Typography.Text
            className="text-white font-bold"
            style={{ fontSize }}
          >
            P
          </Typography.Text>
        </div>
      </div>
    </Link>
  );
}
