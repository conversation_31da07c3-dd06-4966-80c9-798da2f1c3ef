import React, { useState } from "react";
import {
  Modal,
  Form,
  Select,
  TimePicker,
  Input,
  Button,
  Row,
  Col,
  message,
} from "antd";
import { ClockCircleOutlined } from "@ant-design/icons";
import dayjs from "dayjs";

interface CreateOrderModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: (values: CreateOrderFormData) => void;
}

interface CreateOrderFormData {
  customerGroup: string;
  time: dayjs.Dayjs;
  service: string;
  pickupLocation: string;
  notes: string;
}

const CreateOrderModal: React.FC<CreateOrderModalProps> = ({
  open,
  onCancel,
  onOk,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      onOk(values);
      form.resetFields();
      message.success("訂單新增成功");
    } catch (error) {
      console.error("新增訂單失敗:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  // 客群選項
  const customerGroupOptions = [
    { value: "vip", label: "VIP客戶" },
    { value: "regular", label: "一般客戶" },
    { value: "corporate", label: "企業客戶" },
    { value: "student", label: "學生客戶" },
  ];

  // 服務類型選項
  const serviceOptions = [
    { value: "general", label: "一般" },
    { value: "premium", label: "高級" },
    { value: "luxury", label: "豪華" },
    { value: "wheelchair", label: "輪椅" },
  ];

  return (
    <Modal
      title="新增訂單"
      open={open}
      onCancel={handleCancel}
      width={600}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          新增訂單
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          time: dayjs(),
          service: "general",
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="客群"
              name="customerGroup"
              rules={[{ required: true, message: "請選擇客群" }]}
            >
              <Select placeholder="客群" options={customerGroupOptions} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="時間"
              name="time"
              rules={[{ required: true, message: "請選擇時間" }]}
            >
              <TimePicker
                style={{ width: "100%" }}
                placeholder="現在"
                suffixIcon={<ClockCircleOutlined />}
                format="HH:mm"
                showNow
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="上車地點"
              name="pickupLocation"
              rules={[{ required: true, message: "請輸入上車地點" }]}
            >
              <Select
                placeholder="ex：台中市西區"
                showSearch
                allowClear
                filterOption={(input, option) =>
                  (option?.label ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                options={[
                  { value: "taichung_station", label: "台中車站" },
                  { value: "taichung_hsr", label: "台中高鐵站" },
                  { value: "taichung_airport", label: "台中機場" },
                  { value: "fengjia", label: "逢甲夜市" },
                  { value: "yizhong", label: "一中商圈" },
                ]}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="服務（一般/跑腿）"
              name="service"
              rules={[{ required: true, message: "請選擇服務類型" }]}
            >
              <Select placeholder="一般" options={serviceOptions} />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item label="備註" name="notes">
          <Input.TextArea
            placeholder="請輸入"
            rows={3}
            maxLength={200}
            showCount
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateOrderModal;
