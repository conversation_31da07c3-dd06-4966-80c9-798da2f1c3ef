import React, { useState } from "react";
import {
  Card,
  Form,
  Select,
  TimePicker,
  Input,
  Button,
  Row,
  Col,
  message,
} from "antd";
import { ClockCircleOutlined } from "@ant-design/icons";
import dayjs from "dayjs";

interface CreateOrderFormProps {
  onSubmit?: (values: CreateOrderFormData) => void;
}

interface CreateOrderFormData {
  customerGroup: string;
  time: dayjs.Dayjs;
  service: string;
  pickupLocation: string;
  notes: string;
}

const CreateOrderForm: React.FC<CreateOrderFormProps> = ({ onSubmit }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      if (onSubmit) {
        onSubmit(values);
      } else {
        // 默认处理逻辑
        console.log("新增订单:", values);
        message.success("订单新增成功");
      }

      // 重置表单
      form.resetFields();
      // 重新设置默认值
      form.setFieldsValue({
        time: dayjs(),
        service: "general",
      });
    } catch (error) {
      console.error("新增订单失败:", error);
      message.error("请检查表单信息");
    } finally {
      setLoading(false);
    }
  };

  // 客群选项
  const customerGroupOptions = [
    { value: "vip", label: "VIP客户" },
    { value: "regular", label: "一般客户" },
    { value: "corporate", label: "企业客户" },
    { value: "student", label: "学生客户" },
  ];

  // 服务类型选项
  const serviceOptions = [
    { value: "general", label: "一般" },
    { value: "running", label: "跑腿" },
  ];

  return (
    <Card title="新增订单" className="mb-4">
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          time: dayjs(),
          service: "general",
        }}
      >
        <Row gutter={16}>
          <Col xs={24} sm={12} md={8}>
            <Form.Item
              label="客群"
              name="customerGroup"
              rules={[{ required: true, message: "請選擇客群" }]}
            >
              <Select placeholder="客群" options={customerGroupOptions} />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8}>
            <Form.Item
              label="時間"
              name="time"
              rules={[{ required: true, message: "請選擇時間" }]}
            >
              <TimePicker
                style={{ width: "100%" }}
                placeholder="請選擇時間"
                suffixIcon={<ClockCircleOutlined />}
                format="HH:mm"
                showNow
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8}>
            <Form.Item label="服務" name="service">
              <Select placeholder="一般" options={serviceOptions} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col xs={24} md={12}>
            <Form.Item
              label="上車地點"
              name="pickupLocation"
              rules={[{ required: true, message: "請輸入上車地點" }]}
            >
              <Input placeholder="ex：台中市西區" />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item label="备注" name="notes">
              <Input.TextArea
                placeholder="请输入"
                rows={1}
                maxLength={200}
                showCount
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>

      <div className="flex justify-end mt-2">
        <Button type="primary" loading={loading} onClick={handleSubmit}>
          新增订单
        </Button>
      </div>
    </Card>
  );
};

export default CreateOrderForm;
