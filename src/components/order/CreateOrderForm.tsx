import React, { useState } from "react";
import {
  Card,
  Form,
  Select,
  TimePicker,
  Input,
  Button,
  Row,
  Col,
  message,
} from "antd";
import { ClockCircleOutlined } from "@ant-design/icons";
import dayjs from "dayjs";

interface CreateOrderFormProps {
  onSubmit?: (values: CreateOrderFormData) => void;
}

interface CreateOrderFormData {
  customerGroup: string;
  time: dayjs.Dayjs;
  service: string;
  pickupLocation: string;
  notes: string;
}

const CreateOrderForm: React.FC<CreateOrderFormProps> = ({ onSubmit }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      if (onSubmit) {
        onSubmit(values);
      } else {
        // 默认处理逻辑
        console.log("新增订单:", values);
        message.success("订单新增成功");
      }

      // 重置表单
      form.resetFields();
      // 重新设置默认值
      form.setFieldsValue({
        time: dayjs(),
        service: "general",
      });
    } catch (error) {
      console.error("新增订单失败:", error);
      message.error("请检查表单信息");
    } finally {
      setLoading(false);
    }
  };

  // 客群选项
  const customerGroupOptions = [
    { value: "vip", label: "VIP客户" },
    { value: "regular", label: "一般客户" },
    { value: "corporate", label: "企业客户" },
    { value: "student", label: "学生客户" },
  ];

  // 服务类型选项
  const serviceOptions = [
    { value: "general", label: "一般" },
    { value: "premium", label: "高级" },
    { value: "luxury", label: "豪华" },
    { value: "wheelchair", label: "轮椅" },
  ];

  return (
    <Card title="新增订单" className="mb-4">
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          time: dayjs(),
          service: "general",
        }}
      >
        <Row gutter={16}>
          <Col xs={24} sm={12} md={6}>
            <Form.Item
              label="客群"
              name="customerGroup"
              rules={[{ required: true, message: "请选择客群" }]}
            >
              <Select placeholder="客群" options={customerGroupOptions} />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <Form.Item
              label="时间"
              name="time"
              rules={[{ required: true, message: "请选择时间" }]}
            >
              <TimePicker
                style={{ width: "100%" }}
                placeholder="现在"
                suffixIcon={<ClockCircleOutlined />}
                format="HH:mm"
                showNow
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <Form.Item
              label="服务（一般/跑腿）"
              name="service"
              rules={[{ required: true, message: "请选择服务类型" }]}
            >
              <Select placeholder="一般" options={serviceOptions} />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <Form.Item label=" " colon={false}>
              <Button
                type="primary"
                loading={loading}
                onClick={handleSubmit}
                style={{ width: "100%" }}
              >
                新增订单
              </Button>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col xs={24} md={12}>
            <Form.Item
              label="上车地点"
              name="pickupLocation"
              rules={[{ required: true, message: "请输入上车地点" }]}
            >
              <Select
                placeholder="ex：台中市西区"
                showSearch
                allowClear
                filterOption={(input, option) =>
                  (option?.label ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                options={[
                  { value: "taichung_station", label: "台中车站" },
                  { value: "taichung_hsr", label: "台中高铁站" },
                  { value: "taichung_airport", label: "台中机场" },
                  { value: "fengjia", label: "逢甲夜市" },
                  { value: "yizhong", label: "一中商圈" },
                ]}
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item label="备注" name="notes">
              <Input.TextArea
                placeholder="请输入"
                rows={1}
                maxLength={200}
                showCount
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Card>
  );
};

export default CreateOrderForm;
