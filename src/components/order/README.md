# 订单组件

## CreateOrderModal 新增订单模态框

### 功能特性

- 响应式表单布局
- 表单验证
- 支持客群选择
- 时间选择器
- 上车地点选择（支持搜索）
- 服务类型选择
- 备注输入
- 加载状态管理

### 使用方法

```tsx
import { CreateOrderModal } from '@/components/order';

function OrderList() {
  const [modalOpen, setModalOpen] = useState(false);

  const handleCreateOrder = (values: any) => {
    console.log('新增订单:', values);
    // 调用API创建订单
  };

  return (
    <>
      <Button onClick={() => setModalOpen(true)}>
        新增订单
      </Button>
      
      <CreateOrderModal
        open={modalOpen}
        onCancel={() => setModalOpen(false)}
        onOk={handleCreateOrder}
      />
    </>
  );
}
```

### Props

| 属性 | 类型 | 必填 | 说明 |
|------|------|------|------|
| open | boolean | 是 | 控制模态框显示/隐藏 |
| onCancel | () => void | 是 | 取消回调函数 |
| onOk | (values: CreateOrderFormData) => void | 是 | 确认回调函数 |

### 表单数据结构

```tsx
interface CreateOrderFormData {
  customerGroup: string;    // 客群
  time: dayjs.Dayjs;       // 时间
  service: string;         // 服务类型
  pickupLocation: string;  // 上车地点
  notes: string;          // 备注
}
```

### 客群选项

- `vip`: VIP客户
- `regular`: 一般客户
- `corporate`: 企业客户
- `student`: 学生客户

### 服务类型选项

- `general`: 一般
- `premium`: 高级
- `luxury`: 豪华
- `wheelchair`: 轮椅

### 上车地点预设选项

- 台中车站
- 台中高铁站
- 台中机场
- 逢甲夜市
- 一中商圈

### 样式特性

- 使用 Ant Design 组件库
- 响应式布局（栅格系统）
- 表单验证提示
- 加载状态显示
- 统一的视觉风格
