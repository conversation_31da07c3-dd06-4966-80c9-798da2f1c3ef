import React from "react";
import { Collapse, DatePicker, Select, Input, Button, Tag } from "antd";

interface OrderSearchFormProps {
  onSearch?: (values: OrderSearchFormData) => void;
  onReset?: () => void;
}

interface OrderSearchFormData {
  dateRange?: [string, string];
  fleet?: string;
  customerGroup?: string;
  status?: string;
  location?: string;
  orderNumber?: string;
}

const OrderSearchForm: React.FC<OrderSearchFormProps> = ({
  onSearch,
  onReset,
}) => {
  const handleSearch = () => {
    // 这里可以收集表单数据并调用 onSearch
    if (onSearch) {
      onSearch({
        // 实际使用时需要收集表单数据
        dateRange: undefined,
        fleet: undefined,
        customerGroup: undefined,
        status: undefined,
        location: undefined,
        orderNumber: undefined,
      });
    }
  };

  const handleReset = () => {
    // 重置表单逻辑
    if (onReset) {
      onReset();
    }
  };

  // 状态选项配置
  const statusOptions = [
    {
      value: "waiting",
      label: (
        <span>
          <Tag color="#F2B75B" style={{ borderRadius: "12px" }}>
            等待接單
          </Tag>
        </span>
      ),
    },
    {
      value: "canceled",
      label: (
        <span>
          <Tag color="#999999" style={{ borderRadius: "12px" }}>
            乘客取消
          </Tag>
        </span>
      ),
    },
    {
      value: "transfer",
      label: (
        <span>
          <Tag color="#FF7B7B" style={{ borderRadius: "12px" }}>
            流單
          </Tag>
        </span>
      ),
    },
    {
      value: "ongoing",
      label: (
        <span>
          <Tag color="#4D94FF" style={{ borderRadius: "12px" }}>
            執行任務
          </Tag>
        </span>
      ),
    },
    {
      value: "to_pickup",
      label: (
        <span>
          <Tag color="#4D94FF" style={{ borderRadius: "12px" }}>
            前往上車點
          </Tag>
        </span>
      ),
    },
    {
      value: "completed",
      label: (
        <span>
          <Tag color="#67C23A" style={{ borderRadius: "12px" }}>
            完成
          </Tag>
        </span>
      ),
    },
  ];

  return (
    <Collapse
      className="mb-4"
      items={[
        {
          key: "1",
          label: "訂單查詢",
          children: (
            <>
              <div className="grid grid-cols-4 gap-4">
                <div>
                  <div className="mb-2">日期區間</div>
                  <DatePicker.RangePicker style={{ width: "100%" }} />
                </div>
                <div>
                  <div className="mb-2">車隊</div>
                  <Select style={{ width: "100%" }} placeholder="RKS" />
                </div>
                <div>
                  <div className="mb-2">客群</div>
                  <Select style={{ width: "100%" }} placeholder="所有客群" />
                </div>
                <div>
                  <div className="mb-2">狀態</div>
                  <Select
                    style={{ width: "100%" }}
                    placeholder="所有狀態"
                    options={statusOptions}
                  />
                </div>
              </div>
              <div className="mt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="mb-2">上車地點/下車地點</div>
                    <Input placeholder="請輸入" />
                  </div>
                  <div>
                    <div className="mb-2">編號</div>
                    <Input placeholder="請輸入" />
                  </div>
                </div>
              </div>
              <div className="flex justify-end mt-4">
                <Button type="default" onClick={handleReset}>
                  重置
                </Button>
                <Button type="primary" className="ml-2" onClick={handleSearch}>
                  查詢
                </Button>
              </div>
            </>
          ),
        },
      ]}
      defaultActiveKey={["1"]}
    />
  );
};

export default OrderSearchForm;
