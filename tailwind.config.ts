import type { Config } from "tailwindcss";

// 字體定義
const fontFamily = {
  primary: "Open Sans Variable",
  secondary: "Inter Variable",
};

const config: Config = {
  content: ["./src/**/*.{js,ts,jsx,tsx,mdx}"],
  theme: {
    extend: {
      colors: {
        primary: {
          lighter: "#C8FAD6",
          light: "#5BE49B",
          DEFAULT: "#00A76F",
          dark: "#007867",
          darker: "#004B50",
        },
        secondary: {
          lighter: "#D6E4FF",
          light: "#84A9FF",
          DEFAULT: "#3366FF",
          dark: "#1939B7",
          darker: "#091A7A",
        },
        success: {
          lighter: "#D8FBDE",
          light: "#86E8AB",
          DEFAULT: "#36B37E",
          dark: "#1B806A",
          darker: "#0A5554",
        },
        warning: {
          lighter: "#FFF5CC",
          light: "#FFD666",
          DEFAULT: "#FFAB00",
          dark: "#B76E00",
          darker: "#7A4100",
        },
        error: {
          lighter: "#FFE9D5",
          light: "#FFAC82",
          DEFAULT: "#FF5630",
          dark: "#B71D18",
          darker: "#7A0916",
        },
        info: {
          lighter: "#CAFDF5",
          light: "#61F3F3",
          DEFAULT: "#00B8D9",
          dark: "#006C9C",
          darker: "#003768",
        },
        gray: {
          100: "#F9FAFB",
          200: "#F4F6F8",
          300: "#DFE3E8",
          400: "#C4CDD5",
          500: "#919EAB",
          600: "#637381",
          700: "#454F5B",
          800: "#212B36",
          900: "#161C24",
        },
        common: {
          white: "#FFFFFF",
          black: "#000000",
        },
        text: {
          primary: "#212B36",
          secondary: "#637381",
          disabled: "#919EAB",
        },
        bg: {
          paper: "#FFFFFF",
          default: "#F9FAFB",
          neutral: "#F4F6F8",
        },
        border: "rgba(145, 158, 171, 0.2)",
        hover: "rgba(145, 158, 171, 0.1)",

        // 深色模式顏色 (可通過 class 或 data-theme 切換)
        "dark-text": {
          primary: "#FFFFFF",
          secondary: "#919EAB",
          disabled: "#637381",
        },
        "dark-bg": {
          paper: "#212b36",
          default: "#161c24",
          neutral: "#28323D",
        },
      },
      borderRadius: {
        none: "0px",
        xs: "2px",
        sm: "4px",
        DEFAULT: "8px",
        md: "12px",
        lg: "16px",
        xl: "24px",
        full: "9999px",
      },
      boxShadow: {
        none: "none",
        sm: "0 1px 2px 0 rgba(145, 158, 171, 0.16)",
        DEFAULT: "0 4px 8px 0 rgba(145, 158, 171, 0.16)",
        md: "0 8px 16px 0 rgba(145, 158, 171, 0.16)",
        lg: "0 12px 24px 0 rgba(145, 158, 171, 0.16)",
        xl: "0 16px 32px 0 rgba(145, 158, 171, 0.16)",
        "2xl": "0 20px 40px 0 rgba(145, 158, 171, 0.16)",
        "3xl": "0 24px 48px 0 rgba(145, 158, 171, 0.16)",
        inner: "inset 0 2px 4px 0 rgba(145, 158, 171, 0.16)",
        card: "0px 0px 2px 0px rgba(145, 158, 171, 0.2), 0px 12px 24px -4px rgba(145, 158, 171, 0.12)",
        dropdown:
          "0px 0px 2px 0px rgba(145, 158, 171, 0.24), -20px 20px 40px -4px rgba(145, 158, 171, 0.24)",
        dialog: "-40px 40px 80px -8px rgba(0, 0, 0, 0.24)",
        primary: "0px 8px 16px 0px rgba(0, 167, 111, 0.24)",
        secondary: "0px 8px 16px 0px rgba(51, 102, 255, 0.24)",
        info: "0px 8px 16px 0px rgba(0, 184, 217, 0.24)",
        success: "0px 8px 16px 0px rgba(54, 179, 126, 0.24)",
        warning: "0px 8px 16px 0px rgba(255, 171, 0, 0.24)",
        error: "0px 8px 16px 0px rgba(255, 86, 48, 0.24)",
      },
      spacing: {
        0: "0px",
        0.5: "2px",
        1: "4px",
        1.5: "6px",
        2: "8px",
        2.5: "10px",
        3: "12px",
        3.5: "14px",
        4: "16px",
        5: "20px",
        6: "24px",
        7: "28px",
        8: "32px",
        9: "36px",
        10: "40px",
        12: "48px",
        16: "64px",
        20: "80px",
        24: "96px",
        32: "128px",
        48: "192px",
        64: "256px",
      },
      fontFamily: {
        primary: [fontFamily.primary, "sans-serif"],
        secondary: [fontFamily.secondary, "sans-serif"],
      },
      fontSize: {
        xs: "12px",
        sm: "14px",
        base: "16px",
        lg: "18px",
        xl: "20px",
      },
      fontWeight: {
        light: "300",
        normal: "400",
        medium: "500",
        semibold: "600",
        bold: "700",
      },
      lineHeight: {
        none: "1",
        tight: "1.25",
        normal: "1.375",
        relaxed: "1.5",
      },
      opacity: {
        0: "0",
        5: "0.05",
        10: "0.1",
        20: "0.2",
        25: "0.25",
        30: "0.3",
        35: "0.35",
        40: "0.4",
        45: "0.45",
        50: "0.5",
        55: "0.55",
        60: "0.6",
        65: "0.65",
        70: "0.7",
        75: "0.75",
        80: "0.8",
        85: "0.85",
        90: "0.9",
        95: "0.95",
        100: "1",
      },
      zIndex: {
        drawer: "1000",
        modal: "1000",
        snackbar: "1000",
        tooltip: "1000",
      },
    },
    screens: {
      xs: "375px", // mobile => @media (min-width: 375px) { ... }
      sm: "576px", // mobile => @media (min-width: 576px) { ... }
      md: "768px", // tablet => @media (min-width: 768px) { ... }
      lg: "1024px", // desktop => @media (min-width: 1024px) { ... }
      xl: "1280px", // desktop-lg => @media (min-width: 1280px) { ... }
      "2xl": "1536px", // desktop-xl => @media (min-width: 1536px) { ... }
    },
  },
  plugins: [],
};

export default config;
