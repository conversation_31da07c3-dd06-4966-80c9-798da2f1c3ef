{"typescript.tsdk": "./node_modules/typescript/lib", "editor.tabSize": 2, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"quickfix.biome": "explicit", "source.organizeImports.biome": "explicit"}, "editor.quickSuggestions": {"strings": "on"}, "tailwindCSS.experimental.classRegex": [["cn\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]], "npm.packageManager": "pnpm", "i18n-ally.localesPaths": ["src/locales/lang"], "i18n-ally.enabledParsers": ["json"], "i18n-ally.pathMatcher": "{locale}/{namespaces}.{ext}", "i18n-ally.keystyle": "flat", "i18n-ally.sortKeys": true, "i18n-ally.sourceLanguage": "en_US", "i18n-ally.displayLanguage": "zh_CN", "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}