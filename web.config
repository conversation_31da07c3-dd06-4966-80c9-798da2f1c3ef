<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <!-- 設定預設文件 -->
    <defaultDocument>
      <files>
        <clear />
        <add value="index.html" />
      </files>
    </defaultDocument>

    <!-- 啟用靜態檔案壓縮 -->
    <urlCompression doStaticCompression="true" doDynamicCompression="true" />
    
    <!-- 設定 MIME 類型 -->
    <staticContent>
      <!-- JavaScript 模組 -->
      <mimeMap fileExtension=".mjs" mimeType="application/javascript" />
      <mimeMap fileExtension=".js" mimeType="application/javascript" />
      
      <!-- CSS -->
      <mimeMap fileExtension=".css" mimeType="text/css" />
      
      <!-- 字體檔案 -->
      <mimeMap fileExtension=".woff" mimeType="font/woff" />
      <mimeMap fileExtension=".woff2" mimeType="font/woff2" />
      <mimeMap fileExtension=".ttf" mimeType="font/ttf" />
      <mimeMap fileExtension=".eot" mimeType="application/vnd.ms-fontobject" />
      
      <!-- 圖片檔案 -->
      <mimeMap fileExtension=".svg" mimeType="image/svg+xml" />
      <mimeMap fileExtension=".webp" mimeType="image/webp" />
      
      <!-- JSON 檔案 -->
      <mimeMap fileExtension=".json" mimeType="application/json" />
      
      <!-- Source Map -->
      <mimeMap fileExtension=".map" mimeType="application/json" />
    </staticContent>

    <!-- HTTP 響應標頭 -->
    <httpProtocol>
      <customHeaders>
        <!-- 安全標頭 -->
        <add name="X-Content-Type-Options" value="nosniff" />
        <add name="X-Frame-Options" value="DENY" />
        <add name="X-XSS-Protection" value="1; mode=block" />
        <add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
        
        <!-- 快取控制 -->
        <add name="Cache-Control" value="no-cache, no-store, must-revalidate" />
        <add name="Pragma" value="no-cache" />
        <add name="Expires" value="0" />
      </customHeaders>
    </httpProtocol>

    <!-- URL 重寫規則 - 支援 SPA 路由 -->
    <!-- 注意：由於使用 Hash Router，實際上不需要複雜的重寫規則 -->
    <rewrite>
      <rules>
        <!-- 處理靜態資源 -->
        <rule name="StaticAssets" stopProcessing="true">
          <match url="^(.*\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map|json))$" />
          <action type="None" />
        </rule>
        
        <!-- API 路由保持原樣 -->
        <rule name="ApiRoutes" stopProcessing="true">
          <match url="^api/.*" />
          <action type="None" />
        </rule>
        
        <!-- 所有其他請求導向 index.html (雖然 Hash Router 不太需要，但作為備用) -->
        <rule name="ReactSPA" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="/index.html" />
        </rule>
      </rules>
    </rewrite>

    <!-- 錯誤頁面 -->
    <httpErrors errorMode="Custom" defaultResponseMode="ExecuteURL">
      <remove statusCode="404" subStatusCode="-1" />
      <error statusCode="404" path="/index.html" responseMode="ExecuteURL" />
    </httpErrors>

    <!-- 安全設定 -->
    <security>
      <requestFiltering>
        <!-- 移除伺服器標頭 -->
        <hiddenSegments>
          <add segment="bin" />
          <add segment="App_Code" />
        </hiddenSegments>
      </requestFiltering>
    </security>

    <!-- 移除不必要的 HTTP 標頭 -->
    <httpProtocol>
      <customHeaders>
        <remove name="Server" />
      </customHeaders>
    </httpProtocol>

  </system.webServer>
</configuration> 