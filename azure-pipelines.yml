trigger:
  - main

variables:
  NODE_VERSION: "20.x"
  PNPM_VERSION: "9.1.0"
  VITE_APP_BASE_PATH: "/"

stages:
  - stage: Build
    displayName: "建置階段"
    jobs:
      - job: Build
        pool:
          vmImage: "ubuntu-latest"
        steps:
          # 安裝 Node.js
          - task: NodeTool@0
            inputs:
              versionSpec: $(NODE_VERSION)
            displayName: "安裝 Node.js"

          # 快取 pnpm 套件
          - task: Cache@2
            inputs:
              key: 'pnpm | "$(Agent.OS)" | package.json'
              restoreKeys: |
                pnpm | "$(Agent.OS)"
                pnpm
              path: $(Pipeline.Workspace)/.pnpm-store
            displayName: "快取 pnpm 套件"

          # 安裝 pnpm 並設定快取
          - script: |
              npm install -g pnpm@$(PNPM_VERSION)
              pnpm config set store-dir $(Pipeline.Workspace)/.pnpm-store
              pnpm --version
            displayName: "安裝 pnpm 並設定快取目錄"

          # 安裝專案依賴
          - script: |
              pnpm install --frozen-lockfile
            displayName: "安裝專案依賴"

          # 執行 Vite Build
          - script: |
              export NODE_OPTIONS="--max_old_space_size=4096"
              pnpm run build
            displayName: "執行建置（Vite + React）"
            env:
              NODE_ENV: production
              VITE_APP_BASE_PATH: $(VITE_APP_BASE_PATH)

          # 複製 dist/ 與 ecosystem.config.js
          - task: CopyFiles@2
            inputs:
              SourceFolder: "$(System.DefaultWorkingDirectory)"
              Contents: |
                dist/**
                ecosystem.config.js
              TargetFolder: "$(Build.ArtifactStagingDirectory)"
            displayName: "複製建置產物與 PM2 配置"

          # 發布 Build Artifact
          - task: PublishBuildArtifacts@1
            inputs:
              PathtoPublish: "$(Build.ArtifactStagingDirectory)"
              ArtifactName: "deployment"
              publishLocation: "Container"
            displayName: "發布建置產物"
