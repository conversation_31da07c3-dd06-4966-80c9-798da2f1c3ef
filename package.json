{"name": "Cosmos-Fleet-Admin", "private": true, "version": "0.0.0", "type": "module", "homepage": "https://github.com/d3george/cosmos-fleet-admin", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "preinstall": "lefthook install"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/plots": "^2.6.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fontsource-variable/inter": "^5.1.0", "@fontsource-variable/open-sans": "^5.1.0", "@googlemaps/react-wrapper": "^1.2.0", "@iconify/react": "^4.1.1", "@tanstack/react-query": "^5.50.1", "@tanstack/react-query-devtools": "^5.50.1", "@vercel/analytics": "^1.2.2", "@vitejs/plugin-react": "^4.1.0", "antd": "^5.9.3", "axios": "^1.5.1", "clsx": "^2.1.1", "color": "^4.2.3", "dayjs": "^1.11.13", "framer-motion": "^10.16.4", "highlight.js": "^11.9.0", "html-to-md": "^0.8.8", "i18next": "^23.5.1", "i18next-browser-languagedetector": "^7.1.0", "lucide-react": "^0.469.0", "nprogress": "^0.2.0", "numeral": "^2.0.6", "ramda": "^0.29.1", "react": "18.2.0", "react-dom": "18.2.0", "react-error-boundary": "^4.0.13", "react-helmet-async": "^2.0.5", "react-i18next": "^13.2.2", "react-markdown": "^8.0.7", "react-router": "^7.0.2", "recharts": "^3.1.0", "rehype-highlight": "^6.0.0", "rehype-raw": "^6.1.1", "remark-gfm": "^3.0.1", "reset-css": "^5.0.2", "simplebar-react": "^3.2.4", "sonner": "^1.7.0", "tailwind-merge": "^2.5.4", "zustand": "^4.4.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@commitlint/cli": "^17.7.2", "@commitlint/config-conventional": "^17.7.0", "@faker-js/faker": "^8.4.1", "@types/color": "^3.0.4", "@types/nprogress": "^0.2.1", "@types/numeral": "^2.0.3", "@types/ramda": "^0.29.6", "@types/react": "^18.2.28", "@types/react-dom": "^18.2.13", "autoprefixer": "^10.4.16", "lefthook": "^1.8.2", "msw": "^2.4.9", "postcss": "^8.4.31", "postcss-import": "^15.1.0", "postcss-nesting": "^11.3.0", "rollup-plugin-visualizer": "^5.9.2", "tailwindcss": "^3.3.3", "ts-node": "^10.9.1", "typescript": "^5.2.2", "vite": "^5.4.9", "vite-plugin-svg-icons": "^2.0.1", "vite-tsconfig-paths": "^5.0.1"}, "engines": {"node": "20.*"}, "packageManager": "pnpm@9.1.0", "msw": {"workerDirectory": "public"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}